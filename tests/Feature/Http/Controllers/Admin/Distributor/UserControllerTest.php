<?php

namespace Tests\Feature\Http\Controllers\Admin\Distributor;

use App\Actions\ShuttleHealth\SendUserInvitationAction;
use App\Enums\DistributorUserTypeEnum;
use App\Enums\ShuttleUserTypeEnum;
use App\Models\DistributorUser;
use App\Models\User;
use App\Notifications\Mail\AccountOwnerChangedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeDistributorOrganization();

        $admin = User::factory()
            ->withAdminRole(ShuttleUserTypeEnum::ADMINISTRATOR)
            ->create();
        $this->token = $this->fakeCognitoToken(['int-sha-scope'], $admin->cognito_id ?? null);
    }

    #[Test]
    public function it_can_return_filtered_users_by_name()
    {
        $user = DistributorUser::factory()->withRole(DistributorUserTypeEnum::STAFF)->create([
            'first_name' => 'John',
            'last_name' => 'Wick',
        ]);
        $this->distributor->users()->save($user);

        $this->getJson(route('admin.distributors.users.index', [
            'distributor' => $this->distributor->id,
            'filter' => [
                'name' => $user->first_name . ' ' . $user->last_name,
            ],
        ]))->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_successfully_updates_the_organization_owner()
    {
        Notification::fake();

        $oldOwner = $this->distributor->owner;
        $newOwner = DistributorUser::factory()->create();

        $this->distributor->users()->save($newOwner);

        $this->postJson(route('admin.distributors.users.change-owner', [
            'distributor' => $this->distributor->id,
            'user' => $newOwner->id,
        ]))->assertSuccessful();


        $this->assertDatabaseHas('distributor_user', [
            'user_id' => $oldOwner->id,
            'distributor_id' => $this->distributor->id,
            'owner' => false,
        ]);

        $this->assertDatabaseHas('distributor_user', [
            'user_id' => $newOwner->id,
            'distributor_id' => $this->distributor->id,
            'owner' => true,
        ]);

        Notification::assertSentTo(
            $newOwner,
            AccountOwnerChangedMail::class,
        );
    }

    #[Test]
    public function it_can_resend_a_user_invitation()
    {
        $user = DistributorUser::factory(['password' => null])->create();
        $this->distributor->users()->attach($user);

        $this->mock(SendUserInvitationAction::class)
            ->shouldReceive('execute')
            ->once()
            ->with($this->withShallowObjectMock($user), $this->withShallowObjectMock($this->distributor));

        $this->postJson(
            route('admin.distributors.users.resend-invitation', [$this->distributor, $user]),
        )->assertSuccessful();
    }
}
