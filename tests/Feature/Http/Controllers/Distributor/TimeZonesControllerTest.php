<?php

namespace Tests\Feature\Http\Controllers\Distributor;

use App\Enums\PatientTimezoneEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TimeZonesControllerTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    public function setUp(): void
    {
        parent::setUp();
        $this->initializeDistributorOrganization();
        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);
    }

    #[Test]
    public function it_lists_all_the_timezones()
    {
        $timeZonesCount = count(PatientTimezoneEnum::values());
        $route = route('distributors.timezone.list', $this->distributor);
        $response = $this->getJson($route)
            ->assertSuccessful()
            ->assertJsonCount($timeZonesCount, 'data');;
    }
}
