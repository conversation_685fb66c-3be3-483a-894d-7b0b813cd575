<?php

namespace Tests\Feature\Http\Controllers\Distributor;

use App\Actions\ShuttleHealth\CreateDistributorUser;
use App\Actions\ShuttleHealth\DestroyDistributorUser;
use App\Actions\ShuttleHealth\SendUserInvitationAction;
use App\Actions\ShuttleHealth\UpdateDistributorUser;
use App\Enums\DistributorUserTypeEnum;
use App\Enums\UserPermissionEnum;
use App\Models\DistributorUser;
use App\Models\Manufacturer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeDistributorOrganization();

        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);
    }

    #[Test]
    public function it_can_return_all_paginated_distributor_users()
    {
        //add 5 to the existing 3 users for a total of 8
        $this->distributor->users()
            ->saveMany(
                DistributorUser::factory()
                    ->count(5)
                    ->create(),
            );

        $response = $this->getJson(route('distributors.users.index', ['distributor' => $this->distributor, 'per_page' => 5]));

        // We should only receive the first 5 records back, but also have access to the total record count
        $response->assertJsonCount(5, 'data');
        $response->assertJson(['meta' => ['total' => 8]]);

        // Page two should only contain a 5 record
        $this->getJson($response->json('links.next'))
            ->assertJsonCount(3, 'data');
    }

    #[Test]
    public function it_returns_filtered_user_by_name()
    {
        $this->distributor->users()->save(
            DistributorUser::factory([
                'first_name' => 'First',
                'middle_name' => 'Middle',
                'last_name' => 'Last',
            ])->create(),
        );

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'name' => 'First Last',
        ]))->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_returns_filtered_user_by_email()
    {
        $this->distributor->users()->save(
            DistributorUser::factory([
                'email' => '<EMAIL>',
            ])->create(),
        );

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'email' => '<EMAIL>',
        ]))->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_returns_filtered_user_by_role()
    {
        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'role' => DistributorUserTypeEnum::ADMINISTRATOR->value,
        ]))->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_returns_filtered_user_by_roles()
    {
        $manufacturer = Manufacturer::factory()->create();
        $externalUser = DistributorUser::factory()->create();
        $externalUser->updateUserRole(DistributorUserTypeEnum::EXTERNAL_USER->value, $manufacturer->id);
        $this->distributor->users()->save($externalUser);

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'include' => 'roles',
            'roles' => [
                DistributorUserTypeEnum::ADMINISTRATOR->value,
                DistributorUserTypeEnum::EXTERNAL_USER->value,
            ],
        ]))->assertJsonCount(2, 'data');
    }

    #[Test]
    public function it_returns_filtered_user_by_npi()
    {
        $this->distributor->owner->npiRecord()->create([
            'npi' => 111111111,
        ]);

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'npi' => 111111111,
        ]))->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_returns_filtered_users_by_created_at_or_updated_at()
    {
        $timestamp1 = Carbon::now()->subDays(10);
        $this->distributor->users()->attach(
            DistributorUser::factory(['created_at' => $timestamp1])->count(2)->create(),
        );

        $timestamp2 = Carbon::now()->subDays(5);
        $this->distributor->users()->attach(
            DistributorUser::factory(['created_at' => $timestamp2])->count(3)->create(),
        );

        $timestamp3 = Carbon::now()->subDays(2);
        $this->distributor->users()->attach(
            DistributorUser::factory(['updated_at' => $timestamp3])->create(),
        );

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'created_from' => date_format($timestamp2, 'Y-m-d'), // NOTE: 5 from init, 3+1 from test
        ]))->assertJsonCount(7, 'data');

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'created_to' => date_format($timestamp2, 'Y-m-d'), // NOTE: 0 from init, 3+2 from test
        ]))->assertJsonCount(5, 'data');

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'updated_from' => date_format($timestamp3->addDay(), 'Y-m-d'),
        ]))->assertJsonCount(8, 'data'); // NOTE: 5 from init, 3+2 from test

        $this->getJson(route('distributors.users.index', [
            $this->distributor,
            'updated_to' => date_format($timestamp3, 'Y-m-d'),
        ]))->assertJsonCount(1, 'data'); // NOTE: 0 from init, 1 from test
    }

    #[Test]
    public function it_can_create_a_distributor_user()
    {
        $userData = [
            'first_name' => 'Test Distributor User F',
            'middle_name' => 'Test Distributor User M',
            'last_name' => 'Test Distributor User L',
            'email' => '<EMAIL>',
            'mobile' => **********,
            'phone' => **********,
        ];

        $npiRecordData = [
            'npi' => **********, // random valid Registered Nurse NPI
            'attested_on' => '2022-01-01 00:00:00',
        ];

        $this->mock(CreateDistributorUser::class)
            ->shouldReceive('execute')->once()
            ->with($this->withShallowObjectMock($this->distributor), $userData, $npiRecordData, DistributorUserTypeEnum::ADMINISTRATOR->value)
            ->andReturn(DistributorUser::factory()->create());

        $this->postJson(route('distributors.users.store', $this->distributor), [
            ...$userData,
            'npi_record' => $npiRecordData,
            'role' => DistributorUserTypeEnum::ADMINISTRATOR->value,
        ])->assertSuccessful();
    }

    #[Test]
    public function it_fails_to_create_a_distributor_user_when_npi_is_linked_to_another_user()
    {
        $userData = [
            'first_name' => 'Test Distributor User F',
            'middle_name' => 'Test Distributor User M',
            'last_name' => 'Test Distributor User L',
            'email' => '<EMAIL>',
            'mobile' => **********,
            'phone' => **********,
        ];

        $npiRecordData = [
            'npi' => **********, // random valid Registered Nurse NPI
            'attested_on' => '2022-01-01 00:00:00',
        ];

        DistributorUser::factory()->withNpi($npiRecordData['npi'])->create();

        $this->postJson(route('distributors.users.store', $this->distributor), [
            ...$userData,
            'npi_record' => $npiRecordData,
            'role' => DistributorUserTypeEnum::ADMINISTRATOR->value,
        ])->assertUnprocessable();
    }

    #[Test]
    public function it_can_return_a_distributor_user()
    {
        $user = $this->distributor->users->last();

        $this->getJson(route('distributors.users.show', [$this->distributor, $user]))
            ->assertJsonFragment([
                'id' => $user->id,
                'first_name' => $user->first_name,
                'middle_name' => $user->middle_name,
                'last_name' => $user->last_name,
            ]);
    }

    #[Test]
    public function it_can_update_a_distributor_user()
    {
        $user = $this->distributor->users->last();

        $userData = [
            'first_name' => 'Test Distributor User F',
            'middle_name' => 'Test Distributor User M',
            'last_name' => 'Test Distributor User L',
            'mobile' => **********,
            'phone' => **********,
            'fax' => **********,
        ];

        $npiRecordData = [
            'npi' => **********, // random valid Registered Nurse NPI
            'attested_on' => '2022-01-01 00:00:00',
        ];

        $userMock = $this->withShallowObjectMock($user);

        $this->mock(UpdateDistributorUser::class)
            ->shouldReceive('execute')->once()
            ->with($userMock, $userData, $npiRecordData, DistributorUserTypeEnum::ADMINISTRATOR->value)
            ->andReturn(DistributorUser::factory()->create());

        $response = $this->putJson(
            route('distributors.users.update', [$this->distributor, $user]),
            array_merge(
                $userData,
                ['npi_record' => $npiRecordData],
                ['role' => DistributorUserTypeEnum::ADMINISTRATOR->value],
            ),
        );
        $response->assertSuccessful();
    }

    #[Test]
    public function it_can_destory_a_distributor_user()
    {
        $user = $this->distributor->users->last();

        $this->mock(DestroyDistributorUser::class)
            ->shouldReceive('execute')->once()
            ->with($this->withShallowObjectMock($this->distributor), $this->withShallowObjectMock($user));

        $this->deleteJson(route('distributors.users.destroy', [$this->distributor, $user]))
            ->assertSuccessful();
    }

    #[Test]
    public function it_can_restore_a_distributor_user()
    {
        /** @var User $user */
        $user = $this->distributor->users->last();
        $user->delete();

        $this->assertTrue($user->trashed());

        $this->putJson(route('distributors.users.activate', [$this->distributor, $user]))
            ->assertSuccessful();
        $user->refresh();

        $this->assertFalse($user->trashed());
    }

    #[Test]
    public function it_can_resend_a_user_invitation()
    {
        $user = DistributorUser::factory(['password' => null])->create();
        $this->distributor->users()->attach($user);

        $this->mock(SendUserInvitationAction::class)
            ->shouldReceive('execute')->once()
            ->with($this->withShallowObjectMock($user), $this->withShallowObjectMock($this->distributor));

        $this->postJson(
            route('distributors.users.resend-invitation', [$this->distributor, $user]),
        )->assertSuccessful();
    }

    #[Test]
    public function it_returns_user_permissions()
    {
        $user = $this->distributor->users->last();
        $user->syncRoles([DistributorUserTypeEnum::STAFF]);
        $user->syncPermissions([UserPermissionEnum::EDIT_DISTRICT]);

        $this->getJson(route('distributors.users.permissions.index', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]))->assertStatus(200)
            ->assertJson(['data' => [
                'direct_permissions' => [UserPermissionEnum::EDIT_DISTRICT->value],
                'permissions_via_roles' => [],
                'all_permissions' => [UserPermissionEnum::EDIT_DISTRICT->value],
            ]]);

        $role = Role::firstWhere('name', DistributorUserTypeEnum::STAFF);
        $role->syncPermissions([UserPermissionEnum::READ_ALL_DISTRICTS]);

        $this->getJson(route('distributors.users.permissions.index', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]))->assertStatus(200)
            ->assertJson(['data' => [
                'direct_permissions' => [
                    UserPermissionEnum::EDIT_DISTRICT->value,
                ],
                'permissions_via_roles' => [
                    UserPermissionEnum::READ_ALL_DISTRICTS->value,
                ],
                'all_permissions' => [
                    UserPermissionEnum::EDIT_DISTRICT->value,
                    UserPermissionEnum::READ_ALL_DISTRICTS->value,
                ],
            ]]);
    }

    #[Test]
    public function it_assigns_permissions_to_user()
    {
        $user = $this->distributor->users->last();
        $user->syncRoles([DistributorUserTypeEnum::STAFF]);
        $user->syncPermissions([UserPermissionEnum::EDIT_DISTRICT]);

        $this->postJson(route('distributors.users.permissions.update', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]), [
            'permissions' => [],
        ])->assertStatus(200)
            ->assertJson(['data' => [
                'direct_permissions' => [],
                'permissions_via_roles' => [],
                'all_permissions' => [],
            ]]);

        $this->postJson(route('distributors.users.permissions.update', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]), [
            'permissions' => [
                UserPermissionEnum::EDIT_DISTRICT->value,
                UserPermissionEnum::READ_ALL_DISTRICTS->value,
            ],
        ])->assertStatus(200)
            ->assertJson(['data' => [
                'direct_permissions' => [
                    UserPermissionEnum::EDIT_DISTRICT->value,
                    UserPermissionEnum::READ_ALL_DISTRICTS->value,
                ],
                'permissions_via_roles' => [],
                'all_permissions' => [
                    UserPermissionEnum::EDIT_DISTRICT->value,
                    UserPermissionEnum::READ_ALL_DISTRICTS->value,
                ],
            ]]);

        $this->assertTrue($user->hasPermissionTo(UserPermissionEnum::EDIT_DISTRICT->value));
        $this->assertTrue($user->hasPermissionTo(UserPermissionEnum::READ_ALL_DISTRICTS->value));
    }

    #[Test]
    public function it_can_assign_all_permissions_to_user()
    {
        $user = $this->distributor->users->last();

        $user->syncPermissions(UserPermissionEnum::getValues());

        // without role
        $user->syncRoles([]);
        $allPermissions = $user->getAllPermissions()->pluck('name');

        $this->postJson(route('distributors.users.permissions.update', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]), [
            'permissions' => $allPermissions,
        ])->assertStatus(200)
            ->assertJsonCount($allPermissions->count(), 'data.all_permissions');

        // with staff role
        $user->assignRole(DistributorUserTypeEnum::STAFF);
        $allPermissions = $user->getAllPermissions()->pluck('name');

        $this->postJson(route('distributors.users.permissions.update', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]), [
            'permissions' => $allPermissions,
        ])->assertStatus(200)
            ->assertJsonCount($allPermissions->count(), 'data.all_permissions');

        // with admin role
        $user->assignRole(DistributorUserTypeEnum::ADMINISTRATOR);
        $allPermissions = $user->getAllPermissions()->pluck('name');

        $this->postJson(route('distributors.users.permissions.update', [
            'distributor' => $this->distributor->id,
            'user' => $user->id,
        ]), [
            'permissions' => $allPermissions,
        ])->assertStatus(200)
            ->assertJsonCount($allPermissions->count(), 'data.all_permissions');
    }
}
