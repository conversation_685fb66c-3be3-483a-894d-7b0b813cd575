<?php

namespace Tests\Feature\Http\Controllers\Distributor\Order;

use App\Actions\ShuttleHealth\AttachDetachDocumentToOrderAction;
use App\Actions\ShuttleHealth\CreateDocumentsFromFax;
use App\Actions\ShuttleHealth\UploadDocument;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\FaxDirectionEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Models\DocumentRequest;
use App\Models\DocumentRequestHistory;
use App\Models\Fax;
use App\Models\File;
use App\Models\Order;
use App\Models\PatientDocument;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class DocumentControllerTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION);
        $this->order->distributor()->associate($this->distributor)->save();

        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);
    }

    #[Test]
    public function it_can_get_documents_for_order()
    {
        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::MPF->value,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::LAB->value,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        $response = $this->getJson(route('distributors.orders.documents.index', [
            'distributor' => $this->distributor,
            'orderId' => $this->order->id,
        ]));

        $response->assertSuccessful()->assertJsonCount(2, 'data');
    }

    #[Test]
    public function it_can_filter_documents_for_order_which_do_not_belong_to_document_request()
    {
        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::MPF->value,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::LAB->value,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::LAB->value,
            'document_request_id' => null,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        $response = $this->getJson(route('distributors.orders.documents.index', [
            'distributor' => $this->distributor,
            'orderId' => $this->order->id,
            'without_document_request' => true,
        ]));

        $response->assertSuccessful()->assertJsonCount(1, 'data');

        $response = $this->getJson(route('distributors.orders.documents.index', [
            'distributor' => $this->distributor,
            'orderId' => $this->order->id,
            'without_document_request' => false,
        ]));

        $response->assertSuccessful()->assertJsonCount(2, 'data');
    }

    #[Test]
    public function it_can_filter_documents_for_order_by_uploaded_user()
    {
        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::MPF->value,
        ])->for($this->order->provider, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::LAB->value,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::LAB->value,
            'uploaded_by' => $this->distributor->owner->id,
        ])->for($this->distributor, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        $response = $this->getJson(route('distributors.orders.documents.index', [
            'distributor' => $this->distributor,
            'orderId' => $this->order->id,
            'uploaded_by' => $this->distributor->owner->id,
        ]));

        $response->assertSuccessful()->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_can_retrieve_a_document()
    {
        $document = PatientDocument::factory([
            'order_id' => $this->order,
            'type' => OrderDocumentTypeEnum::MPF->value,
        ])->for($this->order->provider, 'createdByOrganization')->withFile()->withOrderPivot()->create();

        File::factory()->create([
            'relation_id' => $document->id,
            'type' => FileTypeEnum::ORDER_DOCUMENT,
            'extension' => 'pdf',
        ]);

        $this->getJson(route('distributors.orders.documents.show', [
            'distributor' => $this->distributor,
            'orderId' => $this->order,
            'documentId' => $document->id,
            'include' => 'files',
        ]))
            ->assertSuccessful()
            ->assertJsonFragment([
                'order_id' => $this->order->id,
                'type' => $document->type,
                'title' => $document->title,
            ])->assertJsonCount(2, 'data.files');
    }

    #[DataProvider('documentProvider')]
    #[Test]
    public function it_can_upload_document($uploadedFile, $mimeType, $title, $type)
    {
        $details = ['message' => 'some message'];
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;
        $orderDocument = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->withFile()
            ->create(['type' => $type, 'title' => $title]);

        $documentFile = $orderDocument->files()->first();

        $this->mock(UploadDocument::class)
            ->shouldReceive('execute')->once()
            ->withArgs(function (
                UploadedFile|File $argFile,
                $user,
                OrderDocumentTypeEnum $argType,
                OrderDocumentSourceEnum $argSource,
                ?Order $order,
                ?DocumentRequest $documentRequest,
                ?string $argTitle,
                ?array $argDetails,
                ?Carbon $signatureDate,
                bool $ltp,
                ?Carbon $appointmentConfirmationDate,
                ?Carbon $expirationDate,
            ) use ($uploadedFile, $title, $type) {
                return $argFile->hashName() === $uploadedFile->hashName()
                    && $user->is($this->distributor->owner) // ✅ No type mismatch here
                    && $argType === $type
                    && $argSource === OrderDocumentSourceEnum::DISTRIBUTOR_USER
                    && $order->is($this->order)
                    && $argTitle === $title
                    && $argDetails === ['message' => 'some message']
                    && $signatureDate === null
                    && $ltp === false;
            })
            ->andReturn(['orderDocument' => $orderDocument, 'createdFile' => $documentFile]);

        $response = $this->postJson(
            route(
                'distributors.orders.documents.upload',
                [$this->distributor, $this->order],
            ),
            [
                'type' => $type->value,
                'file' => $uploadedFile,
                'details' => $details,
                'title' => 'some lab request title',
            ],
        );

        $response->assertJsonCount(1, 'data.files')
            ->assertJsonFragment([
                'id' => $orderDocument->id,
                'title' => 'some lab request title',
                'type' => $type->value,
            ]);
    }

    public static function documentProvider()
    {
        return [
            [UploadedFile::fake()->create('lab.doc', 256, 'application/vnd.openxmlformat'), 'application/vnd.openxmlformat', 'some lab request title', OrderDocumentTypeEnum::LAB],
            [UploadedFile::fake()->image('lab.png'), 'image/png', 'some lab request title', OrderDocumentTypeEnum::LAB],
            [UploadedFile::fake()->image('lab.jpg'), 'image/jpeg', 'some lab request title', OrderDocumentTypeEnum::LAB],
            [UploadedFile::fake()->image('lab.jpeg'), 'image/jpeg', 'some lab request title', OrderDocumentTypeEnum::LAB],
        ];
    }

    #[Test]
    public function it_can_upload_document_and_link_to_related_request_by_type()
    {
        $uploadedFile = UploadedFile::fake()->create(
            'lab.doc',
            256,
            'application/vnd.openxmlformat',
        );

        $title = 'some lab request title';
        $details = ['message' => 'some message'];
        $type = OrderDocumentTypeEnum::LAB;
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;
        File::factory()->create(['type' => FileTypeEnum::ORDER_DOCUMENT]);
        $orderDocument = PatientDocument::factory()
            ->for($this->order)
            ->for($this->distributor, 'createdByOrganization')
            ->withFile()
            ->withOrderPivot()
            ->create(['type' => $type, 'title' => $title]);

        $documentFile = $orderDocument->files()->first();

        $orderDocumentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::LAB]);

        $this->mock(UploadDocument::class)
            ->shouldReceive('execute')->once()
            ->withArgs(function (
                UploadedFile|File $argFile,
                $user,
                OrderDocumentTypeEnum $argType,
                OrderDocumentSourceEnum $argSource,
                ?Order $order,
                ?DocumentRequest $documentRequest,
                ?string $argTitle,
                ?array $argDetails,
                ?Carbon $signatureDate,
                bool $ltp,
                ?Carbon $appointmentConfirmationDate,
                ?Carbon $expirationDate,
            ) use ($uploadedFile, $title, $type, $orderDocumentRequest) {
                return $argFile->hashName() === $uploadedFile->hashName() // compare file by hashName
                    && $user->is($this->distributor->owner)
                    && $argType === $type
                    && $argSource === OrderDocumentSourceEnum::DISTRIBUTOR_USER
                    && $order->is($this->order)
                    && $documentRequest->is($orderDocumentRequest)
                    && $argTitle === $title
                    && $argDetails === ['message' => 'some message']
                    && $signatureDate === null
                    && $ltp === false;
            })
            ->andReturn(['orderDocument' => $orderDocument, 'createdFile' => $documentFile]);

        $response = $this->postJson(
            route(
                'distributors.orders.documents.upload',
                [$this->distributor, $this->order],
            ),
            [
                'type' => $type->value,
                'file' => $uploadedFile,
                'details' => $details,
                'title' => 'some lab request title',
            ],
        );

        $response->assertJsonCount(1, 'data.files')
            ->assertJsonFragment(
                [
                    'id' => $orderDocument->id,
                    'title' => 'some lab request title',
                    'type' => OrderDocumentTypeEnum::LAB->value,
                ],
            );
    }

    #[Test]
    public function it_can_create_document_from_fax()
    {
        $fax = Fax::factory()->for($this->distributor)->create([
            'direction' => FaxDirectionEnum::INBOUND,
        ]);

        $typeLab = DocumentRequestTypeEnum::LAB;
        $documentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create([
                'type' => $typeLab,
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
            ]);

        $documentRequestsData = [
            [
                'order_document_request_id' => $documentRequest->id,
                'type' => $typeLab->value,
                'details' => [
                    'lab_names' => [
                        'lab1',
                        'lab2',
                    ],
                ],
            ],
        ];

        $documentsData = [
            [
                'type' => OrderDocumentTypeEnum::OTHER->value,
                'title' => 'some title',
            ],
        ];

        $this->mock(CreateDocumentsFromFax::class)
            ->shouldReceive('execute')->once()
            ->withArgs(function (
                Order $argOrder,
                Fax $argFax,
                User $argUser,
                array $argDocumentRequestsData,
                array $argDocumentsData,
            ) use ($fax, $documentRequestsData, $documentsData) {
                return $argOrder->is($this->order) // compare Order model instance
                    && $argFax->is($fax) // compare Fax model instance
                    && $argUser->is($this->distributor->owner) // compare User
                    && $argDocumentRequestsData === $documentRequestsData // shallow array compare
                    && $argDocumentsData === $documentsData; // shallow array compare
            })->andReturn();

        $this->postJson(
            route(
                'distributors.orders.documents.store.from-fax',
                [$this->distributor, $this->order],
            ),
            [
                'fax_id' => $fax->id,
                'order_document_requests' => $documentRequestsData,
                'order_documents' => $documentsData,
            ],
        )
            ->assertSuccessful()
            ->assertStatus(Response::HTTP_NO_CONTENT);
    }

    #[Test]
    public function it_can_create_document_for_chart_notes_from_fax()
    {
        $fax = Fax::factory()->for($this->distributor)->create([
            'direction' => FaxDirectionEnum::INBOUND,
        ]);

        $requestType = DocumentRequestTypeEnum::CHART_NOTES;
        $documentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create([
                'type' => $requestType,
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
            ]);

        $documentRequestsData = [
            [
                'order_document_request_id' => $documentRequest->id,
                'type' => $requestType->value,
                'appointment_confirmation_date' => Carbon::now()->format('Y-m-d'),
            ],
        ];

        $documentsData = [
            [
                'type' => OrderDocumentTypeEnum::OTHER->value,
                'title' => 'some title',
            ],
        ];

        $this->mock(CreateDocumentsFromFax::class)
            ->shouldReceive('execute')->once()
            ->withArgs(function (
                Order $argOrder,
                Fax $argFax,
                User $argUser,
                array $argDocumentRequestsData,
                array $argDocumentsData,
            ) use ($fax, $documentRequestsData, $documentsData) {
                return $argOrder->is($this->order) // compare Order model
                    && $argFax->is($fax) // compare Fax model
                    && $argUser->is($this->distributor->owner) // compare User
                    && $argDocumentRequestsData === $documentRequestsData // compare array
                    && $argDocumentsData === $documentsData; // compare array
            })
            ->andReturn();

        $this->postJson(
            route(
                'distributors.orders.documents.store.from-fax',
                [$this->distributor, $this->order],
            ),
            [
                'fax_id' => $fax->id,
                'order_document_requests' => $documentRequestsData,
                'order_documents' => $documentsData,
            ],
        )
            ->assertSuccessful()
            ->assertStatus(Response::HTTP_NO_CONTENT);
    }

    #[Test]
    public function it_can_successfully_delete_an_order_document_file()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        /** @var PatientDocument $orderDocument */
        $orderDocument = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->for($this->order)
            ->withFile()
            ->withOrderPivot()
            ->create([
                'uploaded_by' => $this->distributor->owner->id,
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
            ]);
        /** @var File $file */
        $file = $orderDocument->files()->first();
        /** @var File $fileTwo */
        $fileTwo = File::factory()->create([
            'type' => FileTypeEnum::ORDER_DOCUMENT,
            'relation_id' => $orderDocument->id,
            'extension' => 'pdf',
        ]);

        Storage::disk($documentationDisk)->put('order-documents/' . $file->uuid . '.pdf', 'smth1');
        Storage::disk($documentationDisk)->put('order-documents/' . $fileTwo->uuid . '.pdf', 'smth2');

        $this->deleteJson(
            route(
                'distributors.orders.documents.files.delete',
                [$this->distributor, $this->order->id, $orderDocument->id, $fileTwo->id],
            ),
        )->assertNoContent();

        $this->assertDatabaseHas('patient_documents', [
            'id' => $orderDocument->id,
        ]);

        $this->assertDatabaseMissing('files', [
            'id' => $fileTwo->id,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
        ]);

        Storage::disk($documentationDisk)->assertMissing('order-documents/' . $fileTwo->uuid . '.pdf');
        Storage::disk($documentationDisk)->assertExists('order-documents/' . $file->uuid . '.pdf');
    }

    #[Test]
    public function it_can_delete_an_document_and_related_history()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $orderDocumentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create();
        /** @var PatientDocument $orderDocument */
        $orderDocument = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->for($this->order)
            ->withFile()
            ->withOrderPivot()
            ->create([
                'uploaded_by' => $this->distributor->owner->id,
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'document_request_id' => $orderDocumentRequest->id,
            ]);
        /** @var File $file */
        $file = $orderDocument->files()->first();

        Storage::disk($documentationDisk)->put('order-documents/' . $file->uuid . '.pdf', 'smth1');

        $history = DocumentRequestHistory::factory()->for($orderDocumentRequest)->create([
            'order_document_id' => $orderDocument->id,
            'user_id' => $this->distributor->owner->id,
        ]);

        $this->deleteJson(
            route(
                'distributors.orders.documents.delete',
                [$this->distributor, $this->order->id, $orderDocument->id],
            ),
            [
                'reason' => 'Some reason',
            ],
        )->assertNoContent();

        $this->assertSoftDeleted('patient_documents', [
            'id' => $orderDocument->id,
        ]);

        $this->assertDatabaseMissing('document_request_history', [
            'id' => $history->id,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
        ]);

        Storage::disk($documentationDisk)->assertExists('order-documents/' . $file->uuid . '.pdf');
    }

    #[Test]
    public function it_can_attach_document_to_order()
    {
        /** @var PatientDocument $document */
        $document = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->withFile()
            ->create([
                'order_id' => null, // Document not attached to any order initially
                'global_patient_id' => $this->order->global_patient_id,
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'uploaded_by' => $this->distributor->owner->id,
            ]);

        $this->mock(AttachDetachDocumentToOrderAction::class)
            ->shouldReceive('attachDocumentToOrder')->once()
            ->withArgs(function (
                Order $argOrder,
                int $documentId,
            ) use ($document) {
                return $argOrder->is($this->order)
                    && $documentId === $document->id;
            });

        $response = $this->postJson(
            route(
                'distributors.orders.documents.attach',
                [$this->distributor, $this->order, $document->id],
            ),
        );

        $response->assertNoContent();
    }

    #[Test]
    public function it_can_detach_document_from_order()
    {
        /** @var PatientDocument $document */
        $document = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->for($this->order)
            ->withFile()
            ->withOrderPivot()
            ->create([
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'uploaded_by' => $this->distributor->owner->id,
            ]);

        $this->mock(AttachDetachDocumentToOrderAction::class)
            ->shouldReceive('detachDocumentFromPatient')->once()
            ->withArgs(function (
                Order $argOrder,
                int $documentId,
            ) use ($document) {
                return $argOrder->is($this->order)
                    && $documentId === $document->id;
            });

        $response = $this->postJson(
            route(
                'distributors.orders.documents.detach',
                [$this->distributor, $this->order, $document->id],
            ),
        );

        $response->assertNoContent();
    }

    #[Test]
    public function it_cannot_attach_document_to_order_that_does_not_belong_to_distributor()
    {
        // Create an order that belongs to a different distributor
        $otherDistributor = \App\Models\Distributor::factory()->create();
        $otherOrder = Order::factory()
            ->for($otherDistributor)
            ->create();

        /** @var PatientDocument $document */
        $document = PatientDocument::factory()
            ->for($this->distributor, 'createdByOrganization')
            ->withFile()
            ->create([
                'order_id' => null,
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'uploaded_by' => $this->distributor->owner->id,
            ]);

        $response = $this->postJson(
            route(
                'distributors.orders.documents.attach',
                [$this->distributor, $otherOrder->id, $document->id],
            ),
        );

        $response->assertNotFound();
    }

    #[Test]
    public function it_cannot_detach_document_from_order_that_does_not_belong_to_distributor()
    {
        // Create an order that belongs to a different distributor
        $otherDistributor = \App\Models\Distributor::factory()->create();
        $otherOrder = Order::factory()
            ->for($otherDistributor)
            ->create();

        /** @var PatientDocument $document */
        $document = PatientDocument::factory()
            ->for($otherOrder)
            ->for($this->distributor, 'createdByOrganization')
            ->withFile()
            ->withOrderPivot()
            ->create([
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'uploaded_by' => $this->distributor->owner->id,
            ]);

        $response = $this->postJson(
            route(
                'distributors.orders.documents.detach',
                [$this->distributor, $otherOrder->id, $document->id],
            ),
        );

        $response->assertNotFound();
    }
}
