<?php

namespace Tests\Feature\Http\Controllers\Distributor\Campaign;

use App\Actions\ShuttleHealth\GetOrCreateProviderUserFromNpi;
use App\Enums\ActivityLogTypeEnum;
use App\Enums\AddressTypeEnum;
use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\LeadCanceledReasonEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PatientGenderEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Jobs\Brightree\CreateBtPatientJob;
use App\Models\CustomField;
use App\Models\Distributor;
use App\Models\DistributorCampaign;
use App\Models\Facility;
use App\Models\Language;
use App\Models\Lead;
use App\Models\LeadCustomField;
use App\Models\MessageTemplate;
use App\Models\Order;
use App\Models\Patient;
use App\Models\ProviderUser;
use App\Notifications\Sms\CommunicationStart;
use App\Notifications\Sms\CommunicationStop;
use App\Notifications\Sms\PendingInitialOrderReview;
use App\Notifications\Sms\Sms;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class LeadControllerTest extends TestCase
{
    use RefreshDatabase;

    private DistributorCampaign $campaign;

    private Lead $lead;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeDistributorOrganization();
        $this->campaign = DistributorCampaign::factory([
            'status' => DistributorCampaignStatusEnum::ACTIVE,
        ])
            ->for($this->distributor)
            ->create();

        $this->lead = Lead::factory()
            ->for($this->campaign)
            ->withActivityLog([
                'type' => ActivityLogTypeEnum::CUSTOM,
                'metadata' => [
                    'from' => now()->toDateString(),
                    'to' => now()->addDay()->toDateString(),
                ],
            ])
            ->withActivityLog([
                'type' => ActivityLogTypeEnum::UPDATE_QUALIFIED_DATE,
                'metadata' => [
                    'from' => now()->toDateString(),
                    'to' => now()->addDay()->toDateString(),
                ],
            ])->createQuietly(['status' => LeadStatusEnum::OPEN]);

        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);
    }

    #[Test]
    public function it_returns_campaign_leads()
    {
        $response = $this->getJson(route('distributors.campaigns.leads.index', [$this->distributor, $this->campaign]))
            ->assertSuccessful();

        $response->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_can_create_a_lead()
    {
        Notification::fake();

        $leadData = [
            'first_name' => 'test',
            'last_name' => 'test',
            'phone_cell' => **********,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'communications_text' => 'test',
        ];
        $response = $this->postJson(
            route('distributors.campaigns.leads.store', [$this->distributor, $this->campaign]),
            $leadData,
        )
            ->assertSuccessful();

        $response->assertJsonFragment($leadData);
        $leadData['mobile'] = $leadData['phone_cell'];
        unset($leadData['phone_cell']);

        $this->assertDatabaseHas('leads', $leadData);

        Notification::assertSentTimes(CommunicationStart::class, 1);
    }

    /** @test */
    public function it_creates_a_lead_with_timezone()
    {
        $leadData = [
            'first_name' => 'test',
            'last_name' => 'test',
            'phone_cell' => **********,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'communications_text' => 'test',
            'timezone' => 'America/New_York',
        ];
        $response = $this->postJson(
            route('distributors.campaigns.leads.store', [$this->distributor, $this->campaign]),
            $leadData,
        );

        $response->assertStatus(201)
            ->assertJsonFragment(['timezone' => 'America/New_York']);

        $this->assertDatabaseHas('leads', [
            'first_name' => 'test',
            'last_name' => 'test',
            'timezone' => 'America/New_York',
        ]);
    }

    /** @test */
    public function it_creates_a_lead_without_timezone()
    {
        $leadData = [
            'first_name' => 'without',
            'last_name' => 'timezone',
            'phone_cell' => **********,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'communications_text' => 'test',
        ];
        $response = $this->postJson(
            route('distributors.campaigns.leads.store', [$this->distributor, $this->campaign]),
            $leadData,
        );

        $response->assertStatus(201);
        $this->assertDatabaseHas('leads', [
            'first_name' => 'without',
            'last_name' => 'timezone',
            'timezone' => null,
        ]);
    }

    /** @test */
    public function it_updates_a_lead_with_timezone()
    {

        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'timezone' => 'America/New_York',
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'timezone' => 'America/New_York',
        ]);
    }

    #[Test]
    public function it_updates_a_lead_and_enables_communications()
    {
        Notification::fake();

        $this->lead->updateQuietly([
            'sms_enabled' => false,
            'email_enabled' => false,
            'call_enabled' => false,
            'status' => LeadStatusEnum::CANCELED,
            'source' => 'test',
            'source_url' => '<EMAIL>',
            'marketing_id' => '27Wr6Stest',
        ]);
        $newQualifiedDate = now()->addDay()->toDateString();
        $notes = [
            ['question' => 'DexcomPatientID', 'response' => '120'],
            ['question' => 'BrightreePatientIDs', 'response' => '120'],
        ];
        $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'qualified_date' => $newQualifiedDate,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'notes' => $notes,
            'phone_cell' => **********,
        ])
            ->assertSuccessful();

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'qualified_date' => $newQualifiedDate,
            'mobile' => **********,

        ]);

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->id,
            'activityable_type' => Lead::class,
            'type' => ActivityLogTypeEnum::CUSTOM,
        ]);

        $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'qualified_date' => null,
            'phone_cell' => **********,
        ])
            ->assertSuccessful();

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'qualified_date' => null,
        ]);

        Notification::assertSentTimes(CommunicationStart::class, 1);
    }
    #[Test]
    public function it_updates_is_unsubscribed_status()
    {
        $this->lead->updateQuietly([
            'email' => '<EMAIL>',
            'email_enabled' => false,
        ]);

        $payload = [
            'email_enabled' => true,
            'is_unsubscribed' => false,
        ];

        // When
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $payload);

        // Then
        $response->assertStatus(200);

        $this->assertDatabaseHas('unsubscribe_to_email', [
            'email' => '<EMAIL>',
            'unsubscribed' => false,
        ]);
    }

    #[Test]
    public function it_disables_communications()
    {
        Notification::fake();

        $this->campaign->update(['opt_out_text' => 'test']);
        $this->lead->updateQuietly([
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
        ]);

        $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'sms_enabled' => false,
            'email_enabled' => false,
            'call_enabled' => false,
            'phone_cell' => **********,

        ])
            ->assertSuccessful();

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'sms_enabled' => false,
            'email_enabled' => false,
            'call_enabled' => false,
            'mobile' => **********,

        ]);

        Notification::assertSentTimes(CommunicationStop::class, 1);
    }

    #[Test]
    public function it_prevents_reopening_a_canceled_lead_in_same_campaign()
    {
        $this->lead->update([
            'distributor_campaign_id' => $this->campaign->id,
        ]);

        $this->lead->refresh();
        $this->lead->update([
            'sms_enabled' => false,
            'email_enabled' => false,
            'call_enabled' => false,
            'status' => LeadStatusEnum::CANCELED,
            'canceled_date' => todayTz(),
            'canceled_reason' => LeadCanceledReasonEnum::FAILED_TO_CONNECT_WITH_THE_CUSTOMER,
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->id,
            'activityable_type' => Lead::class,
            'type' => ActivityLogTypeEnum::UPDATE_STATUS,
            'metadata->from' => LeadStatusEnum::OPEN,
            'metadata->to' => LeadStatusEnum::CANCELED,
        ]);

        // Verify cancellation history was created
        $this->assertDatabaseHas('lead_campaign_cancellation_history', [
            'lead_id' => $this->lead->id,
            'distributor_campaign_id' => $this->campaign->id,
        ]);

        // Attempt to reopen in the same campaign should fail
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'status' => LeadStatusEnum::OPEN,
            'phone_cell' => **********,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['status'])
            ->assertJson([
                'errors' => [
                    'status' => ['This lead was previously cancelled from this campaign. Please assign a different campaign to reopen.'],
                ],
            ]);

        // Lead should remain canceled
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'status' => LeadStatusEnum::CANCELED->value,
        ]);
    }

    #[Test]
    public function it_reopens_a_canceled_lead_with_different_campaign()
    {
        Notification::fake();

        // Create a new campaign for reopening
        $newCampaign = DistributorCampaign::factory([
            'status' => DistributorCampaignStatusEnum::ACTIVE,
        ])
            ->for($this->distributor)
            ->create();

        $this->lead->update([
            'sms_enabled' => false,
            'email_enabled' => false,
            'call_enabled' => false,
            'status' => LeadStatusEnum::CANCELED,
            'canceled_date' => todayTz(),
            'canceled_reason' => LeadCanceledReasonEnum::FAILED_TO_CONNECT_WITH_THE_CUSTOMER,
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->id,
            'activityable_type' => Lead::class,
            'type' => ActivityLogTypeEnum::UPDATE_STATUS,
            'metadata->from' => LeadStatusEnum::OPEN,
            'metadata->to' => LeadStatusEnum::CANCELED,
        ]);

        // Verify cancellation history was created for original campaign
        $this->assertDatabaseHas('lead_campaign_cancellation_history', [
            'lead_id' => $this->lead->id,
            'distributor_campaign_id' => $this->campaign->id,
        ]);

        // Reopen with different campaign should succeed - use original campaign route but change campaign in payload
        $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'status' => LeadStatusEnum::OPEN,
            'distributor_campaign_id' => $newCampaign->id,
            'phone_cell' => **********,
        ])
            ->assertSuccessful();

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'status' => LeadStatusEnum::OPEN->value,
            'distributor_campaign_id' => $newCampaign->id,
            'canceled_date' => null,
            'canceled_reason' => null,
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->id,
            'activityable_type' => Lead::class,
            'type' => ActivityLogTypeEnum::UPDATE_STATUS,
            'metadata->from' => LeadStatusEnum::CANCELED,
            'metadata->to' => LeadStatusEnum::OPEN,
        ]);
    }

    #[Test]
    public function it_can_return_filtered_campaign_leads_count()
    {
        $secondCampaign = DistributorCampaign::factory()->for($this->distributor)->create();
        Lead::factory()->for($secondCampaign)->count(2)->createQuietly(['status' => LeadStatusEnum::OPEN]);
        Lead::factory()->for($secondCampaign)->createQuietly(['status' => LeadStatusEnum::CONVERTED]);
        Lead::factory()->for($secondCampaign)->createQuietly(['status' => LeadStatusEnum::CANCELED]);

        $distributor = Distributor::factory()->create();
        $campaign = DistributorCampaign::factory()->for($distributor)->create(['status' => DistributorCampaignStatusEnum::ACTIVE]);
        Lead::factory()->for($campaign)->count(2)->createQuietly();

        $response = $this->getJson(route('distributors.campaigns.leads.counts', [
            'distributor' => $this->distributor,
            'campaign' => $secondCampaign,
            'status' => [
                LeadStatusEnum::OPEN->value,
                LeadStatusEnum::CONVERTED->value,
            ],
        ]))->assertSuccessful();

        $response->assertJson(['count' => 3]);
    }

    #[Test]
    public function it_can_assign_provider_user_from_npi_to_lead()
    {
        $npi = **********;
        $providerUser = ProviderUser::factory()->create();

        $this->mock(GetOrCreateProviderUserFromNpi::class)
            ->shouldReceive('execute')->once()->with($npi)
            ->andReturn($providerUser);

        $this->assertDatabaseMissing('leads', [
            'provider_user_id' => $providerUser->id,
        ]);

        $route = route('distributors.campaigns.leads.assign-provider-npi-user', [
            $this->distributor,
            $this->campaign,
            $this->lead,
        ]);
        $this->putJson($route, [
            'provider_user_npi' => $npi,
        ])->assertSuccessful();

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'provider_user_id' => $providerUser->id,
        ]);
    }

    #[Test]
    public function it_can_clear_provider_user_and_facility_from_lead()
    {
        // Arrange: Set a provider and facility on the lead
        $providerUser = ProviderUser::factory()->create();
        $facility = Facility::factory()->create();
        $this->lead->update([
            'provider_user_id' => $providerUser->id,
            'facility_id' => $facility->id, // Use any valid facility ID or mock as needed
        ]);

        $route = route('distributors.campaigns.leads.assign-provider-npi-user', [
            $this->distributor,
            $this->campaign,
            $this->lead,
        ]);

        // Act: Send request with nulls to clear provider and facility
        $this->putJson($route, [
            'provider_user_npi' => null,
            'facility_id' => null,
        ])->assertSuccessful();

        // Assert: Both fields are now null in the database
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'provider_user_id' => null,
            'facility_id' => null,
        ]);
    }

    #[Test]
    public function it_can_convert_lead_to_order_without_patient()
    {
        Notification::fake();
        Queue::fake();

        $distributorUser = $this->distributor->users()->first();
        $providerUser = ProviderUser::factory()->withNpiRecord()->create();
        $this->actingAs($distributorUser);
        $lead = Lead::factory([
            'provider_user_id' => $providerUser->id,
            'assigned_user_id' => $distributorUser->id,
            'mobile' => **********,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'follow_up_at' => now(),
            'gender' => PatientGenderEnum::FEMALE->value,
            'follow_up_at' => now(),
        ])
            ->withAddress([
                'address_line_1' => 'test convert',
            ])
            ->withPrimaryPayer([], [
                'policy_number' => 121212,
            ])
            ->withProduct([], ['measure_count' => 12345])
            ->withActivityLog([
                'type' => ActivityLogTypeEnum::CUSTOM,
                'metadata' => [
                    'from' => now()->toDateString(),
                    'to' => now()->addDay()->toDateString(),
                ],
            ])
            ->for($this->campaign)
            ->createQuietly();

        $response = $this->postJson(route('distributors.campaigns.leads.convert-to-order', [
            $this->distributor->id,
            $this->campaign->id,
            $lead,
            'create_bt_patient' => true,
        ]));

        $response->assertSuccessful();

        $this->assertDatabaseHas('patients', [
            'first_name' => $lead->first_name,
            'last_name' => $lead->last_name,
            'date_of_birth' => $lead->date_of_birth,
            'gender' => $lead->gender,
            'email' => $lead->email,
            'mobile' => $lead->mobile,
            'sms_enabled' => $lead->sms_enabled,
        ]);
        $this->assertDatabaseHas('addresses', ['address_line_1' => 'test convert']);
        $this->assertDatabaseHas('lead_payer', ['policy_number' => 121212]);
        $this->assertDatabaseHas('lead_product', ['measure_count' => 12345]);
        $this->assertDatabaseHas('orders', [
            'type' => OrderTypeEnum::NEW_PRESCRIPTION,
            'status' => OrderStatusEnum::PENDING_INITIAL_ORDER_REVIEW,
            'distributor_id' => $this->distributor->id,
            'created_by' => $distributorUser->id,
            'provider_user_id' => $providerUser->id,
        ]);

        $lead->refresh();

        Queue::assertPushed(CreateBtPatientJob::class, 1);
        Notification::assertSentToTimes($lead->patient, PendingInitialOrderReview::class, 1);
        Notification::assertSentTimes(CommunicationStart::class, 0);
    }

    #[Test]
    public function it_can_convert_lead_to_order_with_patient()
    {
        $patient = $this->initializeValidPatientFor($this->distributor);
        $patientsCount = DB::table('patients')->count();

        $this->lead->update([
            'notes' => [
                'test question' => 'test answer',
            ],
        ]);
        $this->postJson(route('distributors.campaigns.leads.convert-to-order', [
            $this->distributor,
            $this->campaign,
            $this->lead,
        ]), [
            'patient_id' => $patient->id,
        ])->assertSuccessful();

        $this->lead->refresh();

        $this->assertEquals($patientsCount, DB::table('patients')->count());

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->order_id,
            'activityable_type' => Order::class,
            'type' => ActivityLogTypeEnum::CUSTOM,
        ]);
        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->lead->order_id,
            'activityable_type' => Order::class,
            'type' => ActivityLogTypeEnum::FORM_NOTES,
        ]);
        $this->assertDatabaseMissing('activity_logs', [
            'activityable_id' => $this->lead->order_id,
            'activityable_type' => Order::class,
            'type' => ActivityLogTypeEnum::UPDATE_QUALIFIED_DATE,
        ]);
        $this->assertDatabaseHas('orders', ['global_patient_id' => $patient->global_patient_id]);
    }

    #[Test]
    public function it_can_convert_lead_to_order_and_update_patient()
    {
        $patient = $this->initializeValidPatientFor($this->distributor);
        $patientsCount = DB::table('patients')->count();
        $lead = Lead::factory([
            'status' => LeadStatusEnum::OPEN,
        ])
            ->withAddress([
                'address_line_1' => 'test convert',
            ])
            ->withPrimaryPayer()
            ->for($this->campaign)
            ->create();

        $expectedPrimaryPayer = $lead->payers()->first();

        $this->postJson(route('distributors.campaigns.leads.convert-to-order', [
            $this->distributor,
            $this->campaign,
            $lead,
        ]), [
            'patient_id' => $patient->id,
            'copy_to_patient' => 1,
        ])->assertSuccessful();

        $this->assertEquals($patientsCount, DB::table('patients')->count());
        $this->assertDatabaseHas('orders', ['global_patient_id' => $patient->global_patient_id]);
        $this->assertDatabaseHas('addresses', [
            'address_line_1' => 'test convert',
            'addressable_type' => Patient::class,
            'addressable_id' => $patient->id,
        ]);
        $this->assertDatabaseHas('patient_payer', [
            'patient_id' => $patient->id,
            'payer_id' => $expectedPrimaryPayer->id,
            'policy_number' => $expectedPrimaryPayer->pivot->policy_number,
            'user_defined_payer_type' => $expectedPrimaryPayer->pivot->user_defined_payer_type,
            'priority' => PatientPayerPriorityEnum::PRIMARY,
        ]);
    }

    #[Test]
    public function it_can_convert_lead_to_order_with_not_exist_patient()
    {
        $response = $this->postJson(route('distributors.campaigns.leads.convert-to-order', [
            $this->distributor,
            $this->campaign,
            $this->lead,
        ]), [
            'patient_id' => 123,
        ])->assertUnprocessable();

        $response->assertJsonValidationErrors(['patient_id']);
    }

    #[Test]
    public function it_error_when_converted_lead()
    {
        $lead = Lead::factory([
            'converted_date' => now(),
        ])->for($this->campaign)->create();

        $response = $this->postJson(route('distributors.campaigns.leads.convert-to-order', [
            $this->distributor,
            $this->campaign,
            $lead,
        ]))->assertUnprocessable();

        $this->assertEquals(HttpException::class, get_class($response->exception));
    }

    #[Test]
    public function it_sends_ad_hoc_message_to_lead()
    {
        Notification::fake();

        $this->lead->updateQuietly([
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
        ]);

        $adHocMessageTemplate = MessageTemplate::factory()->for($this->campaign)->create([
            'is_active' => true,
            'status' => MessageTemplateStatusEnum::APPROVED,
            'type' => MessageTemplateTypeEnum::AD_HOC,
        ]);

        $this->postJson(route(
            'distributors.campaigns.leads.notify',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'template_id' => $adHocMessageTemplate->id,
        ])
            ->assertSuccessful();

        Notification::assertSentTimes(Sms::class, 1);
    }

    #[Test]
    public function it_fails_to_send_digital_journey_message_to_lead()
    {
        Notification::fake();

        $this->lead->updateQuietly([
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
        ]);

        $adHocMessageTemplate = MessageTemplate::factory()->for($this->campaign)->create([
            'is_active' => true,
            'status' => MessageTemplateStatusEnum::APPROVED,
            'type' => MessageTemplateTypeEnum::DIGITAL_JOURNEY,
        ]);

        $this->postJson(route(
            'distributors.campaigns.leads.notify',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'template_id' => $adHocMessageTemplate->id,
        ])
            ->assertUnprocessable();

        $this->assertDatabaseMissing('activity_logs', [
            'activityable_id' => $this->lead->id,
            'activityable_type' => Lead::class,
            'type' => ActivityLogTypeEnum::MESSAGE,
        ]);

        Notification::assertNothingSent();
    }

    #[Test]
    public function it_can_import_leads()
    {
        Notification::fake();


        $file = UploadedFile::fake()->createWithContent(
            'leads.csv',
            $this->getMockContent('LeadsImport/leads_import.csv'),
        );
        $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        $this->assertDatabaseCount('leads', 3); // one from setup and two from import

        Notification::assertSentTimes(CommunicationStart::class, 2);
    }

    #[Test]
    public function it_updates_lead_with_payer_details()
    {
        // Arrange: Create a lead with initial data
        $this->lead->updateQuietly([
            'payer_type' => null,
            'group_number' => null,
            'policy_number' => null,
            'phone_cell' => **********,
        ]);

        $payload = [
            'payer_type' => 'commercial',
            'group_number' => '456456456',
            'policy_number' => '98798798',
            'phone_cell' => **********,

        ];

        // Act: Send a PUT request to update the lead
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $payload);

        // Assert: Verify the response and database changes
        $response->assertSuccessful()
            ->assertJsonFragment($payload);

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'payer_type' => 'commercial',
            'group_number' => '456456456',
            'policy_number' => '98798798',
            'mobile' => **********,

        ]);
    }
    #[Test]
    public function it_imports_leads_with_created_date()
    {
        $file = UploadedFile::fake()->createWithContent(
            'leads_with_created_date.csv',
            $this->getMockContent('LeadsImport/new_leads_import.csv'),
        );

        $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify lead with created date was imported correctly
        $this->assertDatabaseHas('leads', [
            'first_name' => 'Hansan',
            'last_name' => 'Hanson',
            'email' => '<EMAIL>',
            'zip' => '84088-3212',
        ]);

        $lead = Lead::where('first_name', 'Hansan')->first();
        $this->assertNotNull($lead->created_date);
        // Verify that the datetime from CSV is properly parsed (08/27/2024 12:00:00 AM = 2024-08-27 00:00:00)
        $this->assertEquals('2024-08-27 00:00:00', $lead->created_date->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function it_imports_leads_with_datetime_timestamp()
    {
        $file = UploadedFile::fake()->createWithContent(
            'leads_with_datetime.csv',
            "FirstName,LastName,EmailAddress,City,State,Zipcode,Address1,MobilePhone,CreatedDate\n" .
            "John,Doe,<EMAIL>,Salt Lake City,UT,84101,123 Main St,5551234567,08/27/2024 02:35:00 PM\n",
        );

        $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify lead with datetime was imported with preserved time
        $lead = Lead::where('first_name', 'John')->where('last_name', 'Doe')->first();
        $this->assertNotNull($lead);
        $this->assertNotNull($lead->created_date);
        // Verify that the full datetime is preserved (08/27/2024 02:35:00 PM = 2024-08-27 14:35:00)
        $this->assertEquals('2024-08-27 14:35:00', $lead->created_date->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function it_imports_leads_with_date_only_sets_to_midnight()
    {
        $file = UploadedFile::fake()->createWithContent(
            'leads_with_date_only.csv',
            "FirstName,LastName,EmailAddress,City,State,Zipcode,Address1,MobilePhone,CreatedDate\n" .
            "Jane,Smith,<EMAIL>,Provo,UT,84601,456 Oak Ave,5559876543,07/24/2025\n",
        );

        $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify lead with date-only is set to midnight
        $lead = Lead::where('first_name', 'Jane')->where('last_name', 'Smith')->first();
        $this->assertNotNull($lead);
        $this->assertNotNull($lead->created_date);
        // Verify that date-only is set to 00:00:00 (07/24/2025 = 2025-07-24 00:00:00)
        $this->assertEquals('2025-07-24 00:00:00', $lead->created_date->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function it_stores_full_timestamp_for_manual_lead_creation()
    {
        $response = $this->postJson(
            route('distributors.campaigns.leads.store', [$this->distributor, $this->campaign]),
            [
                'first_name' => 'Manual',
                'last_name' => 'Creation',
                'phone_cell' => '5551234567',
                'sms_enabled' => true,
                'email_enabled' => true,
                'call_enabled' => true,
                'communications_text' => 'Test communications',
                'email' => '<EMAIL>',
            ],
        );

        $response->assertStatus(201);
        $leadData = $response->json('data');

        $lead = Lead::find($leadData['id']);
        $this->assertNotNull($lead);
        $this->assertNotNull($lead->created_date);

        // Verify it's not just midnight (manual creation should capture current time)
        $this->assertNotEquals('00:00:00', $lead->created_date->format('H:i:s'));

        // Verify it's today
        $this->assertEquals(todayTz()->format('Y-m-d'), $lead->created_date->format('Y-m-d'));
    }

    #[Test]
    public function it_imports_leads_without_created_date()
    {
        $file = UploadedFile::fake()->createWithContent(
            'leads_without_created_date.csv',
            $this->getMockContent('LeadsImport/new_leads_import.csv'),
        );

        $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify leads were created
        $this->assertDatabaseHas('leads', [
            'first_name' => 'Hansan',
            'last_name' => 'Hanson',
            'email' => '<EMAIL>',
            'zip' => '84088-3212',
        ]);

        $this->assertDatabaseHas('leads', [
            'first_name' => 'First Name 1',
            'last_name' => 'Last Name 2',
            'email' => '<EMAIL>',
            'zip' => '99580',
        ]);

        // Verify created_date is set correctly
        $leadWithDate = Lead::where('first_name', 'Hansan')->first();
        $this->assertNotNull($leadWithDate->created_date);
        $this->assertEquals('2024-08-27 00:00:00', $leadWithDate->created_date->format('Y-m-d H:i:s'));

        $leadWithoutDate = Lead::where('first_name', 'First Name 1')->first();
        $this->assertNotNull($leadWithoutDate->created_date);
        $this->assertTrue($leadWithoutDate->created_date->isToday());
    }

    #[Test]
    public function it_includes_address_when_include_all_is_provided()
    {
        // Step 1: Create an address for the lead
        $this->lead->addresses()->create([
            'address_line_1' => '123 Test St',
            'address_line_2' => 'Apt 4B',
            'city' => 'Testville',
            'state' => 'CA',
            'zip' => '90001',
            'type' => AddressTypeEnum::MAILING->value,
        ]);

        // Step 2: Send GET request with include=all
        $response = $this->getJson(route(
            'distributors.campaigns.leads.show',
            [$this->distributor, $this->campaign, $this->lead, 'include' => 'all'],
        ));

        // Step 3: Assert the response includes the address object
        $response->assertSuccessful()
            ->assertJsonFragment([
                'address_line_1' => '123 Test St',
                'address_line_2' => 'Apt 4B',
                'city' => 'Testville',
                'state' => 'CA',
                'zip' => '90001',
                'type' => AddressTypeEnum::MAILING->value,
            ]);
    }

    #[Test]
    public function it_does_not_include_address_when_include_all_is_not_provided()
    {
        // Step 1: Create an address for the lead
        $this->lead->addresses()->create([
            'address_line_1' => '123 Test St',
            'address_line_2' => 'Apt 4B',
            'city' => 'Testville',
            'state' => 'CA',
            'zip' => '90001',
            'type' => AddressTypeEnum::MAILING->value,
        ]);

        // Step 2: Send GET request without include=all
        $response = $this->getJson(route(
            'distributors.campaigns.leads.show',
            [$this->distributor, $this->campaign, $this->lead],
        ));

        // Step 3: Assert the response does not include the address object
        $response->assertSuccessful()
            ->assertJsonMissing([
                'address_line_1' => '123 Test St',
                'address_line_2' => 'Apt 4B',
                'city' => 'Testville',
                'state' => 'CA',
                'zip' => '90001',
            ]);
    }
    public function it_updates_lead_without_address()
    {
        $payload = [
            'first_name' => 'Updated Name',
            'last_name' => 'Updated Last Name',
        ];

        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $payload);

        $response->assertSuccessful()
            ->assertJsonFragment(['first_name' => 'Updated Name', 'last_name' => 'Updated Last Name']);

        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'first_name' => 'Updated Name',
            'last_name' => 'Updated Last Name',
        ]);
    }

    #[Test]
    public function it_updates_lead_with_multiple_addresses()
    {
        // Step 1: Create initial addresses for the lead
        $this->lead->addresses()->createMany([
            [
                'address_line_1' => '123 Initial St',
                'address_line_2' => 'Apt 1',
                'city' => 'Initial City',
                'state' => 'CA',
                'zip' => '90001',
                'type' => AddressTypeEnum::MAILING->value,
            ],
            [
                'address_line_1' => '456 Initial St',
                'address_line_2' => null,
                'city' => 'Initial City 2',
                'state' => 'NY',
                'zip' => '10001',
                'type' => AddressTypeEnum::BILLING->value,
            ],
        ]);

        // Step 2: Prepare payload to update the lead and addresses
        $payload = [
            'first_name' => 'Updated Name',
            'last_name' => 'Updated Last Name',
            'address' => [
                [
                    'id' => $this->lead->addresses[0]->id, // Update the first address
                    'address_line_1' => '123 Updated St',
                    'address_line_2' => 'Apt 4B',
                    'city' => 'Updated City',
                    'state' => 'CA',
                    'zip' => '90001',
                ],
                [
                    'id' => $this->lead->addresses[1]->id, // Update the second address
                    'address_line_1' => '456 Updated St',
                    'address_line_2' => 'Suite 200',
                    'city' => 'Updated City 2',
                    'state' => 'NY',
                    'zip' => '10001',
                ],
            ],
        ];

        // Step 3: Send PUT request to update the lead and addresses
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $payload);

        // Step 4: Assert the response and database changes
        $response->assertSuccessful()
            ->assertJsonFragment(['first_name' => 'Updated Name', 'last_name' => 'Updated Last Name']);

        // Assert lead details are updated
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'first_name' => 'Updated Name',
            'last_name' => 'Updated Last Name',
        ]);

        // Assert the first address is updated
        $this->assertDatabaseHas('addresses', [
            'id' => $this->lead->addresses[0]->id,
            'address_line_1' => '123 Updated St',
            'address_line_2' => 'Apt 4B',
            'city' => 'Updated City',
            'state' => 'CA',
            'zip' => '90001',
        ]);

        // Assert the second address is updated
        $this->assertDatabaseHas('addresses', [
            'id' => $this->lead->addresses[1]->id,
            'address_line_1' => '456 Updated St',
            'address_line_2' => 'Suite 200',
            'city' => 'Updated City 2',
            'state' => 'NY',
            'zip' => '10001',
        ]);
    }

    #[Test]
    public function it_returns_error_for_invalid_address()
    {
        // Step 1: Create initial addresses for the lead
        $this->lead->addresses()->createMany([
            [
                'address_line_1' => '123 Initial St',
                'address_line_2' => 'Apt 1',
                'city' => 'Initial City',
                'state' => 'CA',
                'zip' => '90001',
                'type' => AddressTypeEnum::MAILING->value,
            ],
            [
                'address_line_1' => '456 Initial St',
                'address_line_2' => null,
                'city' => 'Initial City 2',
                'state' => 'NY',
                'zip' => '10001',
                'type' => AddressTypeEnum::BILLING->value,
            ],
        ]);

        // Step 2: Prepare payload with invalid address data
        $payload = [
            'first_name' => 'Updated Name',
            'address' => [
                [
                    'address_line_1' => '', // Invalid: Empty address line
                    'city' => 'Testville',
                    'state' => 'InvalidState', // Invalid: State not in enum
                    'zip' => '123', // Invalid: Zip code format
                ],
            ],
        ];

        // Step 3: Send PUT request to update the lead with invalid address
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $payload);

        // Step 4: Assert validation errors
        $response->assertStatus(422) // Expect 422 Unprocessable Entity
            ->assertJsonValidationErrors([
                'address.0.address_line_1', // Correct key for the first address
                'address.0.state', // Correct key for the first address
                'address.0.zip', // Correct key for the first address
                'address.0.type', // Correct key for the first address
            ]);
    }

    // ============================================
    // Custom Fields Tests
    // ============================================

    #[Test]
    public function it_creates_lead_with_custom_fields()
    {
        Notification::fake();

        // Create custom fields for this campaign
        $customField1 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Patient Priority', 'system_name' => 'patient_priority', 'field_type' => 'text']);

        $customField2 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Follow Up Days', 'system_name' => 'follow_up_days', 'field_type' => 'number']);

        $customFields = collect([$customField1, $customField2]);

        $leadData = [
            'first_name' => 'test',
            'last_name' => 'test',
            'phone_cell' => **********,
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'communications_text' => 'test',
            'custom_fields' => [
                [
                    'id' => $customFields[0]->id,
                    'value' => 'High Priority',
                ],
                [
                    'id' => $customFields[1]->id,
                    'value' => '30',
                ],
            ],
        ];

        $response = $this->postJson(
            route('distributors.campaigns.leads.store', [$this->distributor, $this->campaign]),
            $leadData,
        )->assertSuccessful();

        $lead = Lead::where('first_name', 'test')->where('last_name', 'test')->first();

        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $lead->id,
            'custom_field_id' => $customFields[0]->id,
            'value' => 'High Priority',
        ]);

        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $lead->id,
            'custom_field_id' => $customFields[1]->id,
            'value' => '30',
        ]);

        Notification::assertSentTimes(CommunicationStart::class, 1);
    }

    #[Test]
    public function it_updates_lead_with_custom_fields()
    {
        // Create custom fields for this campaign
        $customFields = CustomField::factory()
            ->count(2)
            ->for($this->campaign)
            ->create();

        // Create initial custom field values
        foreach ($customFields as $customField) {
            $this->lead->leadCustomFields()->create([
                'custom_field_id' => $customField->id,
                'value' => 'initial value',
            ]);
        }

        $updateData = [
            'first_name' => 'Updated',
            'custom_fields' => [
                [
                    'id' => $customFields[0]->id,
                    'value' => 'Updated Value 1',
                ],
                [
                    'id' => $customFields[1]->id,
                    'value' => 'Updated Value 2',
                ],
            ],
        ];

        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), $updateData)->assertSuccessful();

        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $this->lead->id,
            'custom_field_id' => $customFields[0]->id,
            'value' => 'Updated Value 1',
        ]);

        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $this->lead->id,
            'custom_field_id' => $customFields[1]->id,
            'value' => 'Updated Value 2',
        ]);
    }

    #[Test]
    public function it_shows_lead_with_custom_fields()
    {
        // Create lead with custom fields using factory
        $lead = Lead::factory()
            ->for($this->campaign)
            ->withSpecificCustomFields([
                [
                    'display_name' => 'Patient Priority',
                    'system_name' => 'patient_priority',
                    'field_type' => 'text',
                    'value' => 'High Priority',
                ],
                [
                    'display_name' => 'Follow Up Days',
                    'system_name' => 'follow_up_days',
                    'field_type' => 'number',
                    'value' => '30',
                ],
            ])
            ->create();

        $response = $this->getJson(route(
            'distributors.campaigns.leads.show',
            [$this->distributor, $this->campaign, $lead, 'include' => 'custom_fields'],
        ))->assertSuccessful();

        $response->assertJsonStructure([
            'data' => [
                'id',
                'first_name',
                'last_name',
                'custom_fields' => [
                    '*' => [
                        'id',
                        'display_name',
                        'system_name',
                        'field_type',
                        'value',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ],
        ]);

        $customFields = $response->json('data.custom_fields');
        $this->assertCount(2, $customFields);

        $priorityField = collect($customFields)->firstWhere('system_name', 'patient_priority');
        $this->assertEquals('High Priority', $priorityField['value']);

        $followUpField = collect($customFields)->firstWhere('system_name', 'follow_up_days');
        $this->assertEquals('30', $followUpField['value']);
    }

    #[Test]
    public function it_handles_campaign_change_with_matching_custom_fields()
    {
        // Create custom fields for original campaign
        $originalCustomField1 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Patient Priority', 'system_name' => 'patient_priority', 'field_type' => 'text']);

        $originalCustomField2 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Follow Up Days', 'system_name' => 'follow_up_days', 'field_type' => 'number']);

        $originalCustomFields = collect([$originalCustomField1, $originalCustomField2]);

        // Create lead with custom field values
        foreach ($originalCustomFields as $customField) {
            $this->lead->leadCustomFields()->create([
                'custom_field_id' => $customField->id,
                'value' => 'original value ' . $customField->id,
            ]);
        }

        // Create new campaign with matching custom fields
        $newCampaign = DistributorCampaign::factory()
            ->for($this->distributor)
            ->create();

        $newCustomField1 = CustomField::factory()
            ->for($newCampaign)
            ->create(['display_name' => 'Different Display', 'system_name' => 'patient_priority', 'field_type' => 'text']); // same system_name

        $newCustomField2 = CustomField::factory()
            ->for($newCampaign)
            ->create(['display_name' => 'Follow Up Days', 'system_name' => 'different_system', 'field_type' => 'date']); // same display_name

        $newCustomFields = collect([$newCustomField1, $newCustomField2]);

        // Update lead to new campaign
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'distributor_campaign_id' => $newCampaign->id,
        ])->assertSuccessful();

        // Verify lead was moved to new campaign
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'distributor_campaign_id' => $newCampaign->id,
        ]);

        // Verify custom fields were remapped
        // system_name match takes priority
        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $this->lead->id,
            'custom_field_id' => $newCustomFields[0]->id,
            'value' => 'original value ' . $originalCustomFields[0]->id,
        ]);

        // display_name match (since system_name didn't match)
        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $this->lead->id,
            'custom_field_id' => $newCustomFields[1]->id,
            'value' => 'original value ' . $originalCustomFields[1]->id,
        ]);
    }

    #[Test]
    public function it_handles_campaign_change_with_non_matching_custom_fields()
    {
        // Create custom fields for original campaign
        $originalCustomField1 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Unique Field 1', 'system_name' => 'unique_field_1', 'field_type' => 'text']);

        $originalCustomField2 = CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Unique Field 2', 'system_name' => 'unique_field_2', 'field_type' => 'number']);

        $originalCustomFields = collect([$originalCustomField1, $originalCustomField2]);

        // Create lead with custom field values
        foreach ($originalCustomFields as $customField) {
            $this->lead->leadCustomFields()->create([
                'custom_field_id' => $customField->id,
                'value' => 'unique value ' . $customField->id,
            ]);
        }

        // Create new campaign with completely different custom fields
        $newCampaign = DistributorCampaign::factory()
            ->for($this->distributor)
            ->create();

        CustomField::factory()
            ->for($newCampaign)
            ->create(['display_name' => 'Different Field 1', 'system_name' => 'different_field_1', 'field_type' => 'text']);

        CustomField::factory()
            ->for($newCampaign)
            ->create(['display_name' => 'Different Field 2', 'system_name' => 'different_field_2', 'field_type' => 'date']);

        // Store original notes
        $originalNotes = $this->lead->notes ?? [];

        // Update lead to new campaign
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'distributor_campaign_id' => $newCampaign->id,
        ])->assertSuccessful();

        // Verify lead was moved to new campaign
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'distributor_campaign_id' => $newCampaign->id,
        ]);

        // Verify no new custom field values were created
        $this->assertEquals(0, LeadCustomField::where('lead_id', $this->lead->id)->count());

        // Verify unmapped fields were added to notes
        $this->lead->refresh();
        $notes = $this->lead->notes;

        $this->assertNotEquals($originalNotes, $notes);
        $this->assertNotEmpty($notes);
    }

    #[Test]
    public function it_moves_unmapped_fields_to_notes_on_campaign_change()
    {
        // Create custom field for original campaign
        $originalCustomField = CustomField::factory()
            ->for($this->campaign)
            ->create([
                'display_name' => 'Special Instructions',
                'system_name' => 'special_instructions',
                'field_type' => 'text',
            ]);

        // Create lead with custom field value
        $this->lead->leadCustomFields()->create([
            'custom_field_id' => $originalCustomField->id,
            'value' => 'Handle with care',
        ]);

        // Create new campaign with no custom fields
        $newCampaign = DistributorCampaign::factory()
            ->for($this->distributor)
            ->create();

        // Update lead to new campaign
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'distributor_campaign_id' => $newCampaign->id,
        ])->assertSuccessful();

        // Verify lead was moved
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'distributor_campaign_id' => $newCampaign->id,
        ]);

        // Verify custom field was moved to notes
        $this->lead->refresh();
        $notes = $this->lead->notes;

        $this->assertNotEmpty($notes);

        // Check for the expected note format
        $expectedNote = [
            'question' => 'Special Instructions (' . $this->campaign->name . ' Campaign)',
            'response' => 'Handle with care',
        ];

        $this->assertContains($expectedNote, $notes);
    }

    #[Test]
    public function it_imports_leads_with_custom_field_mapping()
    {
        Notification::fake();

        // Create custom fields that match CSV headers
        CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Priority Level', 'system_name' => 'priority_level', 'field_type' => 'text']);

        CustomField::factory()
            ->for($this->campaign)
            ->create(['display_name' => 'Follow Up Days', 'system_name' => 'follow_up_days', 'field_type' => 'number']);

        // Create CSV content with custom field columns
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,priority_level,follow_up_days,extra_column\n";
        $csvContent .= "John,Doe,<EMAIL>,1234567890,123 Main St,Test City,CA,90210,High,30,extra_data\n";
        $csvContent .= "Jane,Smith,<EMAIL>,0987654321,456 Oak Ave,Another City,NY,10001,Medium,14,more_data\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_custom_fields.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify leads were created
        $this->assertDatabaseCount('leads', 3); // 1 from setup + 2 from import

        // Verify custom fields were mapped
        $johnLead = Lead::where('first_name', 'John')->where('last_name', 'Doe')->first();
        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $johnLead->id,
            'value' => 'High',
        ]);

        $janeLead = Lead::where('first_name', 'Jane')->where('last_name', 'Smith')->first();
        $this->assertDatabaseHas('lead_custom_fields', [
            'lead_id' => $janeLead->id,
            'value' => 'Medium',
        ]);

        Notification::assertSentTimes(CommunicationStart::class, 2);
    }

    #[Test]
    public function it_imports_leads_with_unmapped_custom_fields_to_notes()
    {
        // Create custom field that doesn't match any CSV headers
        CustomField::factory()
            ->for($this->campaign)
            ->create([
                'display_name' => 'Internal Notes',
                'system_name' => 'internal_notes',
                'field_type' => 'text',
            ]);

        // Create CSV with unmapped columns
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,unmapped_column,another_unmapped\n";
        $csvContent .= "Test,User,<EMAIL>,1234567890,789 Test St,Test City,TX,77001,unmapped_value,another_value\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_unmapped.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        )->assertNoContent();

        // Verify lead was created
        $lead = Lead::where('first_name', 'Test')->where('last_name', 'User')->first();
        $this->assertNotNull($lead);

        // Verify unmapped columns were added to notes
        $notes = $lead->notes;
        $this->assertNotEmpty($notes);

        // Check that unmapped data appears in notes
        $notesArray = is_array($notes) ? $notes : json_decode($notes, true);
        $this->assertNotEmpty($notesArray);
    }

    #[Test]
    public function it_validates_contact_method_requirement_on_import()
    {
        // Create CSV with no contact methods (no phone or email)
        $csvContent = "FirstName,LastName,Address1,City,State,Zipcode\n";
        $csvContent .= "John,Doe,123 Main St,Test City,CA,90210\n";

        $file = UploadedFile::fake()->createWithContent('leads_without_contact.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // Should return validation error
        $response->assertStatus(422);
        $response->assertJsonFragment([
            'message' => 'Row 2: Please provide at least one contact method (Mobile, Home, Work phone or Email).',
        ]);

        // Verify no lead was created
        $this->assertDatabaseCount('leads', 1); // Only the one from setup
    }

    #[Test]
    public function it_validates_created_date_format_on_import()
    {
        // Test with valid datetime format (m/d/Y h:i:s A)
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,CreatedDate\n";
        $csvContent .= "John,Doe,<EMAIL>,1234567890,123 Main St,Test City,CA,90210,08/27/2024 12:00:00 AM\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_valid_date.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // Should succeed with valid format
        $response->assertNoContent();

        // Verify lead was created with correct date
        $this->assertDatabaseHas('leads', [
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $lead = Lead::where('first_name', 'John')->where('last_name', 'Doe')->first();
        $this->assertEquals('2024-08-27 00:00:00', $lead->created_date->format('Y-m-d H:i:s'));

        // Test with valid date-only format (m/d/Y)
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,CreatedDate\n";
        $csvContent .= "Jane,Smith,<EMAIL>,0987654321,456 Oak Ave,Another City,NY,10001,08/27/2024\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_valid_date_only.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // Should succeed with valid date-only format
        $response->assertNoContent();

        // Verify lead was created with start of day
        $this->assertDatabaseHas('leads', [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
        ]);

        $lead = Lead::where('first_name', 'Jane')->where('last_name', 'Smith')->first();
        $this->assertEquals('2024-08-27 00:00:00', $lead->created_date->format('Y-m-d H:i:s'));

        // Test with invalid datetime format (should still succeed due to fallback)
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,CreatedDate\n";
        $csvContent .= "Bob,Johnson,<EMAIL>,1112223333,789 Pine St,Third City,TX,77001,2024-08-27 12:00:00\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_invalid_date.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // Should succeed with fallback to today's date
        $response->assertNoContent();

        // Verify lead was created with today's date
        $this->assertDatabaseHas('leads', [
            'first_name' => 'Bob',
            'last_name' => 'Johnson',
        ]);

        $lead = Lead::where('first_name', 'Bob')->where('last_name', 'Johnson')->first();
        $this->assertTrue($lead->created_date->isToday());
    }

    #[Test]
    public function it_rejects_invalid_date_formats_on_import()
    {
        // Test various invalid date formats that should fall back to today's date
        $invalidFormats = [
            'invalid-date', // Completely invalid
            'not-a-date', // Completely invalid
            '2024-13-45', // Invalid month and day
        ];

        foreach ($invalidFormats as $index => $invalidFormat) {
            $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,CreatedDate\n";
            $csvContent .= "Test{$index},User{$index},test{$index}@example.com,1234567890,123 Test St,Test City,TX,77001,{$invalidFormat}\n";

            $file = UploadedFile::fake()->createWithContent("leads_with_invalid_date_{$index}.csv", $csvContent);

            $response = $this->postJson(
                route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
                ['file' => $file],
            );

            // Should succeed with fallback to today's date
            $response->assertNoContent();

            // Verify lead was created
            $lead = Lead::where('first_name', "Test{$index}")->where('last_name', "User{$index}")->first();
            $this->assertNotNull($lead);

            // Check that the created_date is set and defaults to today for invalid formats
            $this->assertNotNull($lead->created_date);
            $this->assertTrue($lead->created_date->isToday());
        }
    }

    #[Test]
    public function it_handles_empty_created_date_on_import()
    {
        // Test with empty CreatedDate field
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode,CreatedDate\n";
        $csvContent .= "Test,User,<EMAIL>,1234567890,123 Test St,Test City,TX,77001,\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_empty_date.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // This should succeed since CreatedDate is empty and defaults to today
        $response->assertNoContent();

        // Verify lead was created with today's date
        $lead = Lead::where('first_name', 'Test')->where('last_name', 'User')->first();
        $this->assertNotNull($lead);
        $this->assertTrue($lead->created_date->isToday());
    }

    #[Test]
    public function it_handles_null_created_date_on_import()
    {
        // Test with null CreatedDate field
        $csvContent = "FirstName,LastName,EmailAddress,MobilePhone,Address1,City,State,Zipcode\n";
        $csvContent .= "Test,User,<EMAIL>,1234567890,123 Test St,Test City,TX,77001\n";

        $file = UploadedFile::fake()->createWithContent('leads_with_null_date.csv', $csvContent);

        $response = $this->postJson(
            route('distributors.campaigns.leads.import', [$this->distributor, $this->campaign]),
            ['file' => $file],
        );

        // This should succeed since CreatedDate is null and defaults to today
        $response->assertNoContent();

        // Verify lead was created with today's date
        $lead = Lead::where('first_name', 'Test')->where('last_name', 'User')->first();
        $this->assertNotNull($lead);
        $this->assertTrue($lead->created_date->isToday());
    }

    #[Test]
    public function it_validates_unique_phone_numbers_on_update()
    {
        // Setup a lead
        $lead = Lead::factory()
            ->for($this->campaign)
            ->createQuietly(['status' => LeadStatusEnum::OPEN]);

        // Try to update with duplicate phone numbers
        $duplicatePhoneNumber = '1234567890';
        $payload = [
            'phone_home' => $duplicatePhoneNumber,
            'phone_cell' => $duplicatePhoneNumber,
        ];

        // Send update request with duplicate phone numbers
        $response = $this->putJson(
            route('distributors.campaigns.leads.update', [$this->distributor, $this->campaign, $lead]),
            $payload,
        );

        // Assert validation fails
        $response->assertStatus(422);

        // Assert error message is as expected
        $response->assertJsonValidationErrors([
            'phone_numbers' => 'Phone numbers must be unique across all phone fields (home, work, cell).',
        ]);

        // Verify lead wasn't updated
        $lead->refresh();
        $this->assertNotEquals($duplicatePhoneNumber, $lead->phone_home);
        $this->assertNotEquals($duplicatePhoneNumber, $lead->phone_cell);
    }

    #[Test]
    public function it_updates_a_lead_with_preferred_language()
    {
        $language = Language::factory()->create();

        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'preferred_language_id' => $language->id,
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'preferred_language_id' => $language->id,
        ]);
    }

    #[Test]
    public function it_unassigns_a_lead_when_assigned_user_id_is_null()
    {
        // Arrange: Set up lead with an assigned user
        $assignedUser = $this->distributor->users()->first();
        $this->lead->update(['assigned_user_id' => $assignedUser->id]);

        // Act: Update lead with null assigned_user_id to unassign
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'assigned_user_id' => null,
        ]);

        // Assert: Response is successful
        $response->assertStatus(200);

        // Assert: Lead is unassigned in database
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'assigned_user_id' => null,
        ]);
    }

    #[Test]
    public function it_does_not_auto_assign_lead_when_explicitly_unassigned()
    {
        // Arrange: Set up lead with an assigned user
        $assignedUser = $this->distributor->users()->first();
        $this->lead->update(['assigned_user_id' => $assignedUser->id]);

        // Act: Update lead status to CANCELED with null assigned_user_id
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'assigned_user_id' => null,
            'status' => LeadStatusEnum::CANCELED->value,
            'canceled_reason' => LeadCanceledReasonEnum::FAILED_TO_CONNECT_WITH_THE_CUSTOMER->value,
        ]);

        // Assert: Response is successful
        $response->assertStatus(200);

        // Assert: Lead remains unassigned (not auto-assigned to current user)
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'assigned_user_id' => null,
            'status' => LeadStatusEnum::CANCELED->value,
        ]);
    }

    #[Test]
    public function it_assigns_new_user_when_assigned_user_id_is_explicitly_changed()
    {
        // Arrange: Set up lead with an assigned user
        $oldAssignedUser = $this->distributor->users()->first();
        $newAssignedUser = $this->distributor->users()->skip(1)->first();
        $this->lead->update(['assigned_user_id' => $oldAssignedUser->id]);

        // Act: Update lead with a different assigned user
        $response = $this->putJson(route(
            'distributors.campaigns.leads.update',
            [$this->distributor, $this->campaign, $this->lead],
        ), [
            'assigned_user_id' => $newAssignedUser->id,
        ]);

        // Assert: Response is successful
        $response->assertStatus(200);

        // Assert: Lead is assigned to a new user
        $this->assertDatabaseHas('leads', [
            'id' => $this->lead->id,
            'assigned_user_id' => $newAssignedUser->id,
        ]);
    }
}
