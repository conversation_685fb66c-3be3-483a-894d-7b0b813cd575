<?php

namespace Tests\Feature\Http\Controllers\Manufacturer;

use App\Enums\OrganizationSettingNameEnum;
use App\Enums\ValueTypeEnum;
use App\Models\OrganizationSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class OrganizationSettingControllerTest extends TestCase
{
    use RefreshDatabase;

    private OrganizationSetting $organizationSetting;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeManufacturerOrganization();

        $this->token = $this->fakeCognitoToken(
            ['int-mfr-scope'],
            $this->manufacturer->owner->cognito_id ?? null,
        );

        $this->organizationSetting = OrganizationSetting::factory()
            ->for($this->manufacturer, 'organization')
            ->create();

        $publicDisk = config('filesystems.public');
        Storage::fake(disk: $publicDisk);
    }

    #[Test]
    public function it_returns_all_paginated_global_settings()
    {
        $response = $this->getJson(route('manufacturers.organization-settings.index', [$this->manufacturer]));
        $response->assertJsonCount(1, 'data');
        $response->assertJson(['meta' => ['total' => 1]]);
    }

    #[Test]
    public function it_can_return_a_global_setting()
    {
        $response = $this->getJson(route('manufacturers.organization-settings.show', [
            $this->manufacturer,
            $this->organizationSetting->id,
        ]))->assertSuccessful();
        $this->assertArrayHasKey('name', $response['data']);
        $this->assertArrayHasKey('value_type', $response['data']);
        $this->assertArrayHasKey('value', $response['data']);
    }

    #[Test]
    public function it_can_update_a_global_setting()
    {
        $organizationSetting = OrganizationSetting::factory()
            ->for($this->manufacturer, 'organization')
            ->create([
                'name' => OrganizationSettingNameEnum::BUSINESS_DAYS_TO_RESEND_FAXES,
                'value_type' => ValueTypeEnum::INTEGER,
                'value' => 777,
            ]);

        $response = $this->putJson(route('manufacturers.organization-settings.update', [$this->manufacturer, $organizationSetting->id]), ['value' => 777]);
        $response->assertSuccessful();
        $this->assertNotEmpty($response['data']['value']);
        $this->assertEquals(ValueTypeEnum::INTEGER->value, $response['data']['value_type']);
        $this->assertEquals(777, $response['data']['value']);
    }

    #[Test]
    public function it_return_wrong_validation_by_value_type()
    {
        $response = $this->putJson(
            route('manufacturers.organization-settings.update', [
                $this->manufacturer,
                $this->organizationSetting->id,
            ]),
            ['value' => [1, 2, 3]],
        );
        $response->assertUnprocessable();
    }

    #[Test]
    public function it_can_return_all_settings()
    {
        $response = $this->getJson(route('manufacturers.organization-settings.all', [
            $this->manufacturer,
        ]))->assertSuccessful();


        $this->assertArrayHasKey('general', $response['data']);
        $this->assertArrayHasKey('communication', $response['data']);
        $this->assertArrayHasKey('session', $response['data']);
        $this->assertArrayHasKey('branding', $response['data']);
        $this->assertArrayHasKey('primary_color', $response['data']['branding']);
        $this->assertArrayHasKey('secondary_color', $response['data']['branding']);
        $this->assertArrayHasKey('logo_url', $response['data']['branding']);
        $this->assertArrayHasKey('portal_session_timeout', $response['data']['session']);
        $this->assertArrayHasKey('communication_days_monday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_tuesday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_wednesday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_thursday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_friday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_saturday', $response['data']['communication']);
        $this->assertArrayHasKey('communication_days_sunday', $response['data']['communication']);
        $this->assertArrayHasKey('name', $response['data']['general']['details']);
        $this->assertArrayHasKey('npi_number', $response['data']['general']['details']);
        $this->assertArrayHasKey('address_line_1', $response['data']['general']['organization_address']);
        $this->assertArrayHasKey('address_line_2', $response['data']['general']['organization_address']);
        $this->assertArrayHasKey('city', $response['data']['general']['organization_address']);
        $this->assertArrayHasKey('state', $response['data']['general']['organization_address']);
        $this->assertArrayHasKey('zip', $response['data']['general']['organization_address']);

        $this->assertArrayHasKey('first_name', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('last_name', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('email', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_id', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_update_id', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_status', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_sent_at', $response['data']['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_expires_at', $response['data']['general']['account_owner']);


    }

    #[Test]
    public function it_can_bulk_update_settings()
    {

        // 🚫 Prevent actual mails
        Mail::fake();

        $oldOwnerId = $this->manufacturer->owner->id;
        $newOwnerId = $this->manufacturer
            ->users()
            ->whereNot('id', $oldOwnerId)
            ->first()
            ->id;

        $payload = [
            'unallowed_section' => [
                'language' => 'fr',
            ],
            'session' => [
                'portal_session_timeout' => 45,
                'unallowed_setting_key',
            ],
            'branding' => [
                'primary_color' => '#d06cff',
                'secondary_color' => '#e6163f',
                'logo' => UploadedFile::fake()->image('logo.png'),
            ],
            'communication' => [
                'communication_start_time' => '08:15',
                'communication_end_time' => '19:45',
                'communication_days_monday' => 'false',
                'communication_days_tuesday' => 'true',
                'communication_days_wednesday' => 'true',
                'communication_days_thursday' => 'true',
                'communication_days_friday' => 'true',
                'communication_days_saturday' => 'true',
                'communication_days_sunday' => 'true',
            ],
            'general' => [
                'details' => [
                    'name' => 'My New Name',
                    'npi_number' => **********,
                ],
                'organization_address' => [
                    'address_line_1' => 'Dummy',
                    'address_line_2' => 'Address',
                    'city' => 'Los Angeles',
                    'state' => 'la',
                    'zip' => '1',
                ],
                'account_owner' => [
                    'account_owner_id' => $newOwnerId,
                ],
            ],

        ];

        $response = $this->patchJson(route('manufacturers.organization-settings.bulk-update', [
            $this->manufacturer,
        ]), $payload)->assertSuccessful();

        $data = $response['data'];

        $this->assertArrayHasKey('general', $data);
        $this->assertArrayHasKey('communication', $data);
        $this->assertArrayHasKey('session', $data);
        $this->assertArrayHasKey('branding', $data);


        $this->assertEquals('#d06cff', $response['data']['branding']['primary_color']);
        $this->assertEquals('#e6163f', $response['data']['branding']['secondary_color']);
        $this->assertNotEmpty($data['branding']['logo_url']);

        $this->assertEquals(45, $data['session']['portal_session_timeout']);

        // Communication checks
        $this->assertEquals('08:15', $data['communication']['communication_start_time']);
        $this->assertEquals('19:45', $data['communication']['communication_end_time']);
        $this->assertFalse($data['communication']['communication_days_monday']);
        $this->assertTrue($data['communication']['communication_days_tuesday']);
        $this->assertTrue($data['communication']['communication_days_wednesday']);
        $this->assertTrue($data['communication']['communication_days_thursday']);
        $this->assertTrue($data['communication']['communication_days_friday']);
        $this->assertTrue($data['communication']['communication_days_saturday']);
        $this->assertTrue($data['communication']['communication_days_sunday']);

        // 🚫 Unallowed section is not present
        $this->assertArrayNotHasKey('unallowed_section', $data);

        // 🚫 Unallowed setting key is not present inside screen_settings
        $this->assertArrayNotHasKey('unallowed_setting_key', $data['session']);

        $this->assertArrayHasKey('name', $data['general']['details']);
        $this->assertEquals($payload['general']['details']['name'], $data['general']['details']['name']);
        $this->assertArrayHasKey('npi_number', $data['general']['details']);
        $this->assertEquals($payload['general']['details']['npi_number'], $data['general']['details']['npi_number']);

        // Organization address structure + values
        foreach ($payload['general']['organization_address'] as $key => $value) {
            $this->assertArrayHasKey($key, $data['general']['organization_address']);
            $this->assertEquals($value, $data['general']['organization_address'][$key]);
        }

        // Account owner structure + values
        $this->assertArrayHasKey('first_name', $data['general']['account_owner']);
        $this->assertArrayHasKey('last_name', $data['general']['account_owner']);
        $this->assertArrayHasKey('email', $data['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_id', $data['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_update_id', $data['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_status', $data['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_sent_at', $data['general']['account_owner']);
        $this->assertArrayHasKey('account_owner_invite_expires_at', $data['general']['account_owner']);


    }

}
