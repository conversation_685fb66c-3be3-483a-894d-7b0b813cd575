<?php

namespace Tests\Feature\Http\Controllers\Manufacturer\Order;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductDurationUnitEnum;
use App\Enums\ProductMeasureUnitEnum;
use App\Enums\ProductNarrativeUnitEnum;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class NotesControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $products;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeManufacturerOrganization();
        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION, OrderStatusEnum::PENDING_CLINICAL_DOCUMENTATION, linkToDistributor: true);
        $this->token = $this->fakeCognitoToken(
            ['int-mfr-scope'],
            $this->manufacturer->owner->cognito_id ?? null,
        );
    }

    #[Test]
    public function it_can_create_a_note_for_an_order()
    {
        Notification::fake();
        $product = Product::factory()->for($this->manufacturer)->create();

        // Attach product to order
        $this->order->products()->attach($product->id, [
            'measure_count' => 1,
            'measure_unit' => ProductMeasureUnitEnum::EACHES,
            'duration_count' => 1,
            'duration_unit' => ProductDurationUnitEnum::DAYS,
            'narrative_measure_count' => 1,
            'narrative_unit' => ProductNarrativeUnitEnum::CHANGE_EVERY,
            'narrative_measure_unit' => ProductDurationUnitEnum::DAYS,
            'serial_number' => 'ABC123',
        ]);

        $payload = [
            'order_note' => 'Test note from manufacturer.',
        ];

        $response = $this->postJson(route('manufacturers.orders.notes.create-note', [
            'manufacturer' => $this->manufacturer->id,
            'order' => $this->order->id,
        ]), $payload);

        $response->assertStatus(201);

        $this->assertDatabaseHas('activity_logs', [
            'activityable_id' => $this->order->id,
            'type' => ActivityLogTypeEnum::ORDER_NOTE->value,
            'metadata->note_text' => 'Test note from manufacturer.',
            'user_id' => $this->manufacturer->owner->id,
        ]);
    }

    #[Test]
    public function it_sends_email_to_assigned_distributor_user_when_manufacturer_creates_note()
    {
        // skip
        $this->markTestSkipped('Scheduled to be fixed as an hotfix');

        Notification::fake();

        // Create a distributor user and assign to order
        $distributorUser = \App\Models\DistributorUser::factory()->create();
        $this->order->update(['distributor_user_id' => $distributorUser->id]);

        $payload = [
            'order_note' => 'Test note from manufacturer.',
        ];

        $response = $this->postJson(route('manufacturers.orders.notes.create-note', [
            'manufacturer' => $this->manufacturer->id,
            'order' => $this->order->id,
        ]), $payload);

        $response->assertStatus(201);

        // Verify notification was sent to the assigned distributor user
        Notification::assertSentTo(
            $distributorUser,
            \App\Notifications\Mail\Order\Notes\NoteAdded::class,
        );
    }

    #[Test]
    public function it_sends_email_to_distributor_owner_when_no_assigned_user()
    {
        Notification::fake();

        // Ensure order has no assigned distributor user
        $this->order->update(['distributor_user_id' => null]);

        // Refresh the order to get updated relationships
        $this->order->refresh();

        // Debug: Check if distributor and owner exist
        $this->assertNotNull($this->order->distributor, 'Order should have a distributor');
        $this->assertNotNull($this->order->distributor->owner, 'Distributor should have an owner');

        $payload = [
            'order_note' => 'Test note from manufacturer.',
        ];

        $response = $this->postJson(route('manufacturers.orders.notes.create-note', [
            'manufacturer' => $this->manufacturer->id,
            'order' => $this->order->id,
        ]), $payload);

        $response->assertStatus(201);

        // Verify notification was sent to the distributor owner
        Notification::assertSentTo(
            $this->order->distributor->owner,
            \App\Notifications\Mail\Order\Notes\NoteAdded::class,
        );
    }

    #[Test]
    public function it_does_not_send_email_when_no_assigned_user_and_no_distributor_owner()
    {
        Notification::fake();

        // Ensure order has no assigned distributor user
        $this->order->update(['distributor_user_id' => null]);

        // Refresh the order to get updated relationships
        $this->order->refresh();

        // Remove the distributor owner (simulate a distributor without an owner)
        $this->order->distributor->users()->updateExistingPivot($this->order->distributor->owner->id, ['owner' => false]);

        // Refresh distributor to get updated owner relationship
        $this->order->distributor->refresh();

        $payload = [
            'order_note' => 'Test note from manufacturer.',
        ];

        $response = $this->postJson(route('manufacturers.orders.notes.create-note', [
            'manufacturer' => $this->manufacturer->id,
            'order' => $this->order->id,
        ]), $payload);

        $response->assertStatus(201);

        // Verify no notification was sent
        Notification::assertNothingSent();
    }
}
