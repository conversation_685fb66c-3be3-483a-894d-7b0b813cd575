<?php

namespace Tests\Feature\Http\Controllers;

use App\Actions\ShuttleHealth\CreateQuicksightUserAction;
use App\Actions\ShuttleHealth\GenerateQuicksightEmbedUrlAction;
use App\Enums\DistributorUserTypeEnum;
use App\Enums\QuicksightEnum;
use App\Enums\UserPermissionEnum;
use App\Http\Middleware\ThrottleQuicksightRequests;
use App\Models\Distributor;
use App\Models\DistributorUser;
use App\Models\QuicksightUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

/**
 * Class QuicksightControllerTest
 *
 * Comprehensive test suite for QuickSight controller endpoints covering:
 * - Authentication and authorization (policies, permissions, roles)
 * - Request validation (valid/invalid inputs, edge cases)
 * - Business logic (embed URL generation, user creation)
 * - Rate limiting (middleware behavior)
 * - Error handling (exceptions, AWS failures)
 * - Response formats (API contract compliance)
 * - Integration (database, caching, mocking)
 */
class QuicksightControllerTest extends TestCase
{
    use RefreshDatabase;

    protected ?Distributor $distributor = null;

    protected ?QuicksightUser $quicksightUser = null;

    protected ?DistributorUser $adminUser = null;

    protected ?DistributorUser $staffUser = null;

    protected ?DistributorUser $staffUserWithPermission = null;

    protected ?DistributorUser $externalUser = null;

    protected string $adminToken;

    protected string $staffToken;

    protected string $staffWithPermissionToken;

    protected string $externalUserToken;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable throttling middleware during tests unless specifically testing it
        $this->withoutMiddleware(ThrottleQuicksightRequests::class);

        $this->initializeDistributorOrganization();
        $this->createTestUsers();
        $this->createQuicksightTestData();
        $this->generateTokens();
    }

    /**
     * Create different types of users for comprehensive testing
     */
    protected function createTestUsers(): void
    {
        // Admin user (already created via initializeDistributorOrganization)
        $this->adminUser = $this->distributor->owner;

        // Staff user without special permissions
        $this->staffUser = DistributorUser::factory([
            'cognito_id' => 'test-staff-cognito-id',
            'notifications' => true,
        ])->withRole(DistributorUserTypeEnum::STAFF)->create();
        $this->distributor->users()->attach($this->staffUser);

        // Staff user with explicit QuickSight permissions
        $this->staffUserWithPermission = DistributorUser::factory([
            'cognito_id' => 'test-staff-with-permission-cognito-id',
            'notifications' => true,
        ])->withRole(DistributorUserTypeEnum::STAFF)->create();
        $this->distributor->users()->attach($this->staffUserWithPermission);

        // Grant explicit permissions to this staff user
        $embedPermission = Permission::firstOrCreate(['name' => UserPermissionEnum::GENERATE_ANALYTICS_EMBED_URL->value]);
        $createUserPermission = Permission::firstOrCreate(['name' => UserPermissionEnum::CREATE_QUICK_SIGHT_USER->value]);
        $this->staffUserWithPermission->givePermissionTo([$embedPermission, $createUserPermission]);

        // External user (limited permissions)
        $this->externalUser = DistributorUser::factory([
            'cognito_id' => 'test-external-cognito-id',
            'notifications' => true,
        ])->withRole(DistributorUserTypeEnum::EXTERNAL_USER)->create();
        $this->distributor->users()->attach($this->externalUser);
    }

    /**
     * Create QuickSight test data
     */
    protected function createQuicksightTestData(): void
    {
        $this->quicksightUser = QuicksightUser::factory()->create([
            'distributor_id' => $this->distributor->id,
            'arn' => 'arn:aws:quicksight:us-east-1:123456789012:user/default/test-user',
            'username' => 'test-user',
            'role' => QuicksightEnum::READER,
            'is_active' => true,
        ]);
    }

    /**
     * Generate authentication tokens for different user types
     */
    protected function generateTokens(): void
    {
        $this->adminToken = $this->fakeCognitoToken(['int-dme-scope'], $this->adminUser->cognito_id);
        $this->staffToken = $this->fakeCognitoToken(['int-dme-scope'], $this->staffUser->cognito_id);
        $this->staffWithPermissionToken = $this->fakeCognitoToken(['int-dme-scope'], $this->staffUserWithPermission->cognito_id);
        $this->externalUserToken = $this->fakeCognitoToken(['int-dme-scope'], $this->externalUser->cognito_id);
    }

    #[Test]
    public function test_get_embed_url_requires_authentication()
    {
        $response = $this->postJson(route('distributors.quicksight.embed-url', [
            'distributor' => $this->distributor->id,
            'dashboard_id' => 'test-dashboard-123',
        ]));

        $response->assertStatus(401);
    }

    #[Test]
    public function test_create_user_requires_authentication()
    {
        $response = $this->postJson(route('distributors.quicksight.create-user', [
            'distributor' => $this->distributor->id,
        ]), [
            'username' => 'test_user',
            'email' => '<EMAIL>',
            'role' => QuicksightEnum::READER->value,
        ]);

        $response->assertStatus(401);
    }

    #[Test]
    public function test_admin_user_can_access_embed_url()
    {
        $this->mockGenerateEmbedUrlAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => ['embed_url', 'expires_at'],
            ]);
    }

    #[Test]
    public function test_admin_user_can_create_quicksight_user()
    {
        $this->mockCreateUserAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), []);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => ['id', 'distributor_id', 'user_id', 'username', 'role', 'is_active'],
                'message',
                'status',
                'aws_user_id',
            ]);
    }

    #[Test]
    public function test_staff_user_without_permission_cannot_access_embed_url()
    {
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->staffToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(403)
            ->assertJsonFragment([
                'message' => 'You do not have permission to generate analytics embed URLs. Contact your administrator for access.',
            ]);
    }

    #[Test]
    public function test_staff_user_without_permission_cannot_create_quicksight_user()
    {
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->staffToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
            ]);

        $response->assertStatus(403)
            ->assertJsonFragment([
                'message' => 'You do not have permission to create QuickSight users.',
            ]);
    }

    #[Test]
    public function test_staff_user_with_explicit_permission_can_access_embed_url()
    {
        $this->mockGenerateEmbedUrlAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->staffWithPermissionToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(200);
    }

    #[Test]
    public function test_staff_user_with_explicit_permission_can_create_quicksight_user()
    {
        $this->mockCreateUserAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->staffWithPermissionToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
            ]);

        $response->assertStatus(201);
    }

    #[Test]
    public function test_external_user_cannot_access_embed_url()
    {
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->externalUserToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(403);
    }

    #[Test]
    public function test_external_user_cannot_create_quicksight_user()
    {
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->externalUserToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
            ]);

        $response->assertStatus(403);
    }

    #[Test]
    public function test_user_cannot_access_other_distributor_analytics()
    {
        $otherDistributor = $this->createDistributor();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $otherDistributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(403)
            ->assertJsonFragment([
                'message' => 'You do not belong to this organization and cannot access its analytics.',
            ]);
    }

    #[Test]
    public function test_get_embed_url_requires_dashboard_id_parameter()
    {
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
            ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['dashboard_id'])
            ->assertJsonFragment([
                'message' => 'The dashboard id field is required.',
            ]);
    }

    #[Test]
    public function test_get_embed_url_validates_dashboard_id_format()
    {
        $testCases = [
            ['dashboard_id' => '', 'expected_error' => 'required'],
            ['dashboard_id' => null, 'expected_error' => 'required'],
            ['dashboard_id' => str_repeat('x', 256), 'expected_error' => 'max'],
        ];

        foreach ($testCases as $testCase) {
            $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}", 'accept' => 'application/json'])
                ->postJson(route('distributors.quicksight.embed-url', [
                    'distributor' => $this->distributor->id,
                    'dashboard_id' => $testCase['dashboard_id'],
                ]));

            $response->assertStatus(422)
                ->assertJsonValidationErrors(['dashboard_id']);
        }
    }

    #[Test]
    public function test_create_user_creates_quicksight_user_for_authenticated_user()
    {
        $this->mockCreateUserAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), []);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'username',
                    'role',
                    'is_active',
                ],
                'message',
                'status',
                'aws_user_id',
            ]);
    }

    #[Test]
    public function test_create_user_prevents_duplicate_quicksight_user_for_same_distributor()
    {
        // Mock the action to succeed once, then fail on second call
        $mockAction = Mockery::mock(CreateQuicksightUserAction::class);

        // First call succeeds
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Distributor::class))
            ->andReturn([
                'quicksight_user' => [
                    'id' => 999,
                    'username' => 'test_user',
                    'email' => '<EMAIL>',
                    'role' => 'READER',
                    'is_active' => true,
                ],
                'aws_response' => ['User' => ['UserId' => 'aws-test-id']],
            ]);

        // Second call throws exception
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Distributor::class))
            ->andThrow(new \App\Exceptions\QuicksightException(
                'QuickSight user already exists for this distributor and user.',
                500,
                null,
                'registerUser',
                'ConflictException',
            ));

        $this->app->instance(CreateQuicksightUserAction::class, $mockAction);

        // First call should succeed
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), []);

        $response->assertStatus(201);

        // Second call should fail with exception
        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), []);

        $response->assertStatus(500);
    }

    #[Test]
    public function test_get_embed_url_returns_successful_response_with_new_generation()
    {
        $expectedUrl = 'https://quicksight.aws.amazon.com/embed/test-new';
        $expectedExpiry = now()->addMinutes(10)->toIso8601String();

        $mockAction = Mockery::mock(GenerateQuicksightEmbedUrlAction::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(
                Mockery::on(fn ($distributor) => $distributor->id === $this->distributor->id),
                'test-dashboard-123',
                Mockery::on(fn ($user) => $user->id === $this->adminUser->id),
                Mockery::type('string'), // IP address
            )
            ->andReturn([
                'embed_url' => $expectedUrl,
                'expires_at' => $expectedExpiry,
            ]);

        $this->app->instance(GenerateQuicksightEmbedUrlAction::class, $mockAction);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'embed_url',
                    'expires_at',
                ],
            ])
            ->assertJson([
                'data' => [
                    'embed_url' => $expectedUrl,
                    'expires_at' => $expectedExpiry,
                ],
            ]);
    }

    #[Test]
    public function test_create_user_returns_successful_response()
    {
        $this->mockCreateUserAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), []);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'distributor_id',
                    'user_id',
                    'username',
                    'role',
                    'is_active',
                ],
                'message',
                'status',
                'aws_user_id',
            ])
            ->assertJson([
                'message' => 'QuickSight user created successfully',
                'status' => 'success',
                'aws_user_id' => 'aws-test-id',
            ]);
    }

    #[Test]
    public function test_create_user_with_optional_parameters()
    {
        // Since the action now only takes distributor parameter and ignores
        // request data (creates READER user for authenticated user),
        // this test verifies the controller ignores optional parameters gracefully
        $this->mockCreateUserAction();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'ignored_user', // These parameters are ignored
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
                'session_name' => 'IgnoredSession',
                'custom_permissions_name' => 'IgnoredPerms',
                'namespace' => 'ignored_namespace',
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => ['id', 'distributor_id', 'user_id', 'username', 'role', 'is_active'],
                'message',
                'status',
                'aws_user_id',
            ]);
    }

    #[Test]
    public function test_get_embed_url_handles_general_exception()
    {
        $mockAction = Mockery::mock(GenerateQuicksightEmbedUrlAction::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->andThrow(new \Exception('AWS API Connection Failed'));

        $this->app->instance(GenerateQuicksightEmbedUrlAction::class, $mockAction);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(500) // Controller returns 404 for exceptions
            ->assertJson([
                'message' => 'AWS API Connection Failed',
            ]);
    }

    #[Test]
    public function test_get_embed_url_handles_quicksight_user_not_found_exception()
    {
        $mockAction = Mockery::mock(GenerateQuicksightEmbedUrlAction::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->andThrow(ValidationException::withMessages([
                'quicksight_user' => ['No QuickSight user found for given distributor.'],
            ]));

        $this->app->instance(GenerateQuicksightEmbedUrlAction::class, $mockAction);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $this->distributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['quicksight_user'])
            ->assertJsonFragment([
                'quicksight_user' => ['No QuickSight user found for given distributor.'],
            ]);
    }

    #[Test]
    public function test_create_user_handles_general_exception()
    {
        $mockAction = Mockery::mock(CreateQuicksightUserAction::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->andThrow(new \Exception('AWS API Error'));

        $this->app->instance(CreateQuicksightUserAction::class, $mockAction);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
            ]);

        $response->assertStatus(500)
            ->assertJson([
                'message' => 'AWS API Error',
            ]);
    }

    #[Test]
    public function test_create_user_persists_to_database()
    {
        $mockAction = Mockery::mock(CreateQuicksightUserAction::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->andReturn([
                'quicksight_user' => [
                    'id' => 999,
                    'distributor_id' => $this->distributor->id,
                    'username' => 'db_test_user',
                    'email' => '<EMAIL>',
                    'role' => 'READER',
                    'arn' => 'arn:aws:quicksight:us-east-1:123456789012:user/default/db_test_user',
                    'is_active' => true,
                ],
                'aws_response' => ['User' => ['UserId' => 'aws-db-test-id']],
            ]);

        $this->app->instance(CreateQuicksightUserAction::class, $mockAction);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.create-user', [
                'distributor' => $this->distributor->id,
            ]), [
                'username' => 'db_test_user',
                'email' => '<EMAIL>',
                'role' => QuicksightEnum::READER->value,
            ]);

        $response->assertStatus(201);

        // Verify the action was called (which would handle database persistence)
        // In a real integration test, you might verify database state here
    }

    #[Test]
    public function test_distributor_relationship_is_enforced()
    {
        $otherDistributor = $this->createDistributor();

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->adminToken}"])
            ->postJson(route('distributors.quicksight.embed-url', [
                'distributor' => $otherDistributor->id,
                'dashboard_id' => 'test-dashboard-123',
            ]));

        $response->assertStatus(403)
            ->assertJsonFragment([
                'message' => 'You do not belong to this organization and cannot access its analytics.',
            ]);
    }

    /**
     * Mock the GenerateQuicksightEmbedUrlAction for successful responses
     */
    protected function mockGenerateEmbedUrlAction(): void
    {
        $mockAction = Mockery::mock(GenerateQuicksightEmbedUrlAction::class);
        $mockAction->shouldReceive('execute')
            ->andReturn([
                'embed_url' => 'https://quicksight.aws.amazon.com/embed/test',
                'expires_at' => now()->addMinutes(10)->toIso8601String(),
            ]);

        $this->app->instance(GenerateQuicksightEmbedUrlAction::class, $mockAction);
    }

    /**
     * Mock the CreateQuicksightUserAction for successful responses
     */
    protected function mockCreateUserAction(): void
    {
        $mockAction = Mockery::mock(CreateQuicksightUserAction::class);
        $mockAction->shouldReceive('execute')
            ->with(Mockery::type(Distributor::class))
            ->andReturn([
                'quicksight_user' => [
                    'id' => 999,
                    'distributor_id' => 1,
                    'user_id' => 1,
                    'username' => 'test_user',
                    'role' => 'READER',
                    'is_active' => true,
                ],
                'aws_response' => ['User' => ['UserId' => 'aws-test-id']],
            ]);

        $this->app->instance(CreateQuicksightUserAction::class, $mockAction);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
