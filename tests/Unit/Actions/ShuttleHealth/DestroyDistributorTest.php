<?php

namespace Tests\Unit\Actions\ShuttleHealth;

use App\Actions\ShuttleHealth\DestroyDistributor;
use App\Models\Distributor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class DestroyDistributorTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();
    }

    #[Test]
    public function it_can_successfully_delete_a_distributor()
    {
        $this->initializeDistributorOrganization();

        $users = $this->distributor->users;

        $this->expectExceptionMessage('Distributor destroying is not allowed. You have to deactivate it instead.');
        app(DestroyDistributor::class)->execute(
            distributor: $this->distributor,
        );

        $this->assertDatabaseMissing('npi_records', [
            'npiable_type' => Distributor::class,
            'npiable_id' => $this->distributor->id,
        ]);

        foreach ($users as $user) {
            $this->assertDatabaseMissing('distributor_user', [
                'distributor_id' => $this->distributor->id,
                'user_id' => $user->id,
            ]);
            $this->assertDatabaseHas('users', [
                'id' => $user->id,
            ]);
        }

        $this->assertDatabaseMissing('distributors', [
            'id' => $this->distributor->id,
        ]);
    }
}
