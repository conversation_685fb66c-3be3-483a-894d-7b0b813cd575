<?php

namespace Tests\Unit\Actions\ShuttleHealth;

use App\Actions\ShuttleHealth\UploadDocument;
use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Jobs\Documents\OrderDocumentUploadedJob;
use App\Models\DocumentRequest;
use App\Services\ShuttleHealth\DocumentUploadsCollectorService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UploadDocumentTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION, OrderStatusEnum::PENDING_PHYSICIAN_SIGNATURE);
        $this->setOrganizationContext($this->distributor);
    }

    #[Test]
    public function it_can_successfully_upload_document_for_lab_request()
    {
        Queue::fake([
            OrderDocumentUploadedJob::class,
        ]);

        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $uploadedFile = UploadedFile::fake()->create(
            'lab.pdf',
            256,
            'application/pdf',
        );

        $title = 'some lab request title';
        $details = ['lab1, lab2'];
        $type = OrderDocumentTypeEnum::LAB;
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;
        $expirationDate = Carbon::now()->addDays(30);

        $orderDocumentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create([
                'type' => DocumentRequestTypeEnum::LAB,
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
            ]);

        /** @var UploadOrderDocument $action */
        $action = app(UploadDocument::class);
        $result = $action->execute(
            file: $uploadedFile,
            user: $this->distributor->owner,
            type: $type,
            source: $source,
            order: $this->order,
            documentRequest: $orderDocumentRequest,
            title: $title,
            details: $details,
            signatureDate: null,
            ltp: false,
            expirationDate: $expirationDate,
        );

        $document = $result['orderDocument'];
        $file = $result['createdFile'];

        $this->assertEquals($title, $document->title);

        $this->assertDatabaseHas('patient_documents', [
            'id' => $document->id,
            'title' => $title,
            'type' => $type->value,
            'source' => $source->value,
            'uploaded_by' => $this->distributor->owner->id,
            'document_request_id' => $orderDocumentRequest->id,
            'expiration_date' => $expirationDate,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
            'type' => FileTypeEnum::PATIENT_DOCUMENT->value,
            'relation_id' => $document->id,
        ]);
        $this->assertDatabaseHas('document_request_history', [
            'order_document_id' => $document->id,
            'type' => DocumentRequestHistoryTypeEnum::UPLOADED->value,
            'user_id' => $this->distributor->owner->id,
            'document_request_id' => $orderDocumentRequest->id,
        ]);
        $this->assertDatabaseHas('document_requests', [
            'id' => $orderDocumentRequest->id,
            'type' => $type->value,
            'status' => DocumentRequestStatusEnum::RECEIVED->value,
        ]);

        // Verify the file record was created correctly
        $this->assertNotNull($file);
        $this->assertEquals(FileTypeEnum::PATIENT_DOCUMENT, $file->type);

        // Note: In test environment with fake storage, we verify the file path is correct
        // but don't assert file existence since it's handled by the storage service mock
        $expectedPath = 'patient-documents/' . $file->uuid . '.pdf';
        $this->assertEquals($expectedPath, $file->type->getFilePath($file->uuid, $file->extension));

        // test that OrderDocumentUploadedJob was dispatched with a 1 minute delay
        Queue::assertPushed(OrderDocumentUploadedJob::class);

        $uploadedDocumentsCollector = Cache::get(DocumentUploadsCollectorService::makeCacheKey($this->order->id));
        $this->assertNotEmpty($uploadedDocumentsCollector);
        $this->assertCount(1, $uploadedDocumentsCollector['documents']);
        $this->assertEquals($document->id, $uploadedDocumentsCollector['documents'][0]);
    }

    #[Test]
    public function it_can_successfully_upload_document_for_lab_request_without_order()
    {
        Queue::fake([
            OrderDocumentUploadedJob::class,
        ]);

        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $uploadedFile = UploadedFile::fake()->create(
            'lab.pdf',
            256,
            'application/pdf',
        );

        $title = 'some lab request title';
        $details = ['lab1, lab2'];
        $type = OrderDocumentTypeEnum::LAB;
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;

        $documentRequest = DocumentRequest::factory()
            ->for($this->distributor)
            ->create([
                'type' => DocumentRequestTypeEnum::LAB,
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
            ]);

        /** @var UploadOrderDocument $action */
        $action = app(UploadDocument::class);
        $result = $action->execute(
            file: $uploadedFile,
            user: $this->distributor->owner,
            type: $type,
            source: $source,
            documentRequest: $documentRequest,
            title: $title,
            details: $details,
            signatureDate: null,
            ltp: false,
        );

        $document = $result['orderDocument'];
        $file = $result['createdFile'];

        $this->assertEquals($title, $document->title);

        $this->assertDatabaseHas('patient_documents', [
            'id' => $document->id,
            'order_id' => null,
            'title' => $title,
            'type' => $type->value,
            'source' => $source->value,
            'uploaded_by' => $this->distributor->owner->id,
            'document_request_id' => $documentRequest->id,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
            'type' => FileTypeEnum::PATIENT_DOCUMENT->value,
            'relation_id' => $document->id,
        ]);
        $this->assertDatabaseHas('document_request_history', [
            'order_document_id' => $document->id,
            'type' => DocumentRequestHistoryTypeEnum::UPLOADED->value,
            'user_id' => $this->distributor->owner->id,
            'document_request_id' => $documentRequest->id,
        ]);
        $this->assertDatabaseHas('document_requests', [
            'id' => $documentRequest->id,
            'type' => $type->value,
            'status' => DocumentRequestStatusEnum::RECEIVED->value,
        ]);

        // Verify the file record was created correctly
        $this->assertNotNull($file);
        $this->assertEquals(FileTypeEnum::PATIENT_DOCUMENT, $file->type);
        $expectedPath = 'patient-documents/' . $file->uuid . '.pdf';
        $this->assertEquals($expectedPath, $file->type->getFilePath($file->uuid, $file->extension));

        // test that OrderDocumentUploadedJob was not dispatched if order is not provided
        Queue::assertNotPushed(OrderDocumentUploadedJob::class);
    }

    #[Test]
    public function it_can_successfully_upload_document_for_chart_notes_request_linked_to_order()
    {
        Queue::fake([
            OrderDocumentUploadedJob::class,
        ]);

        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $uploadedFile = UploadedFile::fake()->create(
            'chart-notes.pdf',
            256,
            'application/pdf',
        );

        $title = 'some chart notes request title';
        $type = OrderDocumentTypeEnum::CHART_NOTES;
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;

        $orderDocumentRequest = DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create([
                'type' => DocumentRequestTypeEnum::CHART_NOTES,
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
            ]);

        /** @var UploadOrderDocument $action */
        $action = app(UploadDocument::class);
        $result = $action->execute(
            file: $uploadedFile,
            user: $this->distributor->owner,
            type: $type,
            source: $source,
            documentRequest: $orderDocumentRequest,
            title: $title,
            appointmentConfirmationDate: Carbon::now(),
        );

        $document = $result['orderDocument'];
        $file = $result['createdFile'];

        $this->assertEquals($title, $document->title);

        $this->assertDatabaseHas('patient_documents', [
            'id' => $document->id,
            'title' => $title,
            'type' => $type->value,
            'source' => $source->value,
            'uploaded_by' => $this->distributor->owner->id,
            'document_request_id' => $orderDocumentRequest->id,
            'order_id' => $this->order->id,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
            'type' => FileTypeEnum::PATIENT_DOCUMENT->value,
            'relation_id' => $document->id,
        ]);
        $this->assertDatabaseHas('document_request_history', [
            'order_document_id' => $document->id,
            'type' => DocumentRequestHistoryTypeEnum::UPLOADED->value,
            'user_id' => $this->distributor->owner->id,
            'document_request_id' => $orderDocumentRequest->id,
        ]);
        $this->assertDatabaseHas('document_requests', [
            'id' => $orderDocumentRequest->id,
            'type' => $type->value,
            'status' => DocumentRequestStatusEnum::RECEIVED->value,
            'appointment_confirmation_date' => Carbon::now()->format('Y-m-d'),
        ]);

        // Verify the file record was created correctly
        $this->assertNotNull($file);
        $this->assertEquals(FileTypeEnum::PATIENT_DOCUMENT, $file->type);
        $expectedPath = 'patient-documents/' . $file->uuid . '.pdf';
        $this->assertEquals($expectedPath, $file->type->getFilePath($file->uuid, $file->extension));

        // test that OrderDocumentUploadedJob was dispatched with a 1 minute delay
        Queue::assertPushed(OrderDocumentUploadedJob::class);
    }

    #[Test]
    public function it_can_successfully_upload_document_for_patient_without_order()
    {
        Queue::fake([
            OrderDocumentUploadedJob::class,
        ]);

        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $uploadedFile = UploadedFile::fake()->create(
            'patient-doc.pdf',
            256,
            'application/pdf',
        );

        $title = 'Patient Document';
        $details = ['note' => 'patient document note'];
        $type = OrderDocumentTypeEnum::OTHER;
        $source = OrderDocumentSourceEnum::DISTRIBUTOR_USER;
        $expirationDate = Carbon::now()->addDays(30);

        $patient = $this->order->patient;

        /** @var UploadDocument $action */
        $action = app(UploadDocument::class);
        $result = $action->execute(
            file: $uploadedFile,
            user: $this->distributor->owner,
            type: $type,
            source: $source,
            title: $title,
            details: $details,
            signatureDate: null,
            ltp: false,
            expirationDate: $expirationDate,
            patient: $patient,
        );

        $document = $result['orderDocument'];
        $file = $result['createdFile'];

        $this->assertEquals($title, $document->title);

        $this->assertDatabaseHas('patient_documents', [
            'id' => $document->id,
            'title' => $title,
            'type' => $type->value,
            'source' => $source->value,
            'uploaded_by' => $this->distributor->owner->id,
            'order_id' => null,
            'document_request_id' => null,
            'global_patient_id' => $patient->global_patient_id,
            'expiration_date' => $expirationDate,
        ]);
        $this->assertDatabaseHas('files', [
            'id' => $file->id,
            'type' => FileTypeEnum::PATIENT_DOCUMENT->value,
            'relation_id' => $document->id,
        ]);

        // Verify the file record was created correctly
        $this->assertNotNull($file);
        $this->assertEquals(FileTypeEnum::PATIENT_DOCUMENT, $file->type);
        $expectedPath = 'patient-documents/' . $file->uuid . '.pdf';
        $this->assertEquals($expectedPath, $file->type->getFilePath($file->uuid, $file->extension));

        // test that OrderDocumentUploadedJob was not dispatched if order is not provided
        Queue::assertNotPushed(OrderDocumentUploadedJob::class);
    }

    #[Test]
    public function it_throws_exception_when_no_order_document_request_or_patient_provided()
    {
        $uploadedFile = UploadedFile::fake()->create(
            'test.pdf',
            256,
            'application/pdf',
        );

        /** @var UploadDocument $action */
        $action = app(UploadDocument::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Order, document request or patient is required');

        $action->execute(
            file: $uploadedFile,
            user: $this->distributor->owner,
            type: OrderDocumentTypeEnum::OTHER,
            source: OrderDocumentSourceEnum::DISTRIBUTOR_USER,
        );
    }
}
