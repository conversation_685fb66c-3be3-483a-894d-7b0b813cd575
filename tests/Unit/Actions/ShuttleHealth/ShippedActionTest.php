<?php

namespace Tests\Unit\Actions\ShuttleHealth;

use App\Actions\ShuttleHealth\ShippedAction;
use App\Enums\OrderTypeEnum;
use App\Models\OrderShipping;
use App\Notifications\Sms\OrderShipped;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ShippedActionTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION, linkToDistributor: true);

        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);

        OrderShipping::factory()->create(['order_id' => $this->order->id]);

        $this->setOrganizationContext($this->distributor);
    }

    #[Test]
    public function it_will_change_order_transition_to_shipped()
    {
        Notification::fake();
        app(ShippedAction::class)->execute($this->order);
        Notification::assertSentTimes(OrderShipped::class, 1);
    }
}
