<?php

namespace Tests\Unit\Actions\ShuttleHealth;

use App\Actions\ShuttleHealth\MakeCmnPDF;
use App\Contracts\FileStorageServiceInterface;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Models\CMSHCPC;
use App\Models\DocumentRequest;
use App\Models\MedicalPolicyForm;
use App\Models\MedicalPolicyResponse;
use App\Models\Patient;
use App\Models\Signature;
use App\Models\User;
use Barryvdh\DomPDF\PDF as DomPDFPDF;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class MakeCmnPDFTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION, OrderStatusEnum::PENDING_PHYSICIAN_SIGNATURE);

        // without signature
        $this->assertNull($this->provider->owner->signature);
        $this->setOrganizationContext($this->provider);
    }

    private function createMedicalPolicyFormResponseForOrder()
    {
        $patient = $this->order->globalPatient->getPatient($this->provider);

        $formSchema = [
            [
                'id' => null,
                'index' => 0,
                'label' => 'Text edit field',
                'type' => 'text-field',
                'props' => [
                    'value' => null,
                    'placeholder' => 'type here',
                ],
            ],
            [
                'id' => null,
                'index' => 1,
                'label' => null,
                'type' => 'wysiwyg',
                'props' => [
                    'html' => '<p><strong>test</strong></p>',
                ],
            ],
            [
                'id' => null,
                'index' => 2,
                'label' => null,
                'type' => 'file-upload',
            ],
            [
                'id' => null,
                'index' => 3,
                'label' => null,
                'type' => 'icd10',
            ],
        ];

        $form = MedicalPolicyForm::factory()
            ->hasAttached($this->order->products()->first()->commercialHcpcs()->first(), [], 'cmsHcpcs')
            ->hasAttached($patient->primaryPayer)
            ->create([
                'order_type' => OrderTypeEnum::NEW_PRESCRIPTION,
                'payer_type' => $patient->primaryPayer->pivot->user_defined_payer_type,
                'form_schema' => $formSchema,
            ]);

        $formData = [
            [
                'index' => 0,
                'propKey' => 'value',
                'value' => 'some text',
            ],
            [
                'index' => 2,
                'propKey' => 'value',
                'value' => [[]],
            ],
            [
                'index' => 3,
                'propKey' => 'selectedOptions',
                'value' => [
                    [
                        'id' => 3,
                        'code' => 'A00.1',
                    ],
                    [
                        'id' => 1,
                        'code' => 'A00',
                    ],
                ],
            ],
        ];

        MedicalPolicyResponse::create([
            'medical_policy_form_id' => $form->id,
            'order_id' => $this->order->id,
            'form_data' => $formData,
        ]);
    }

    #[Test]
    public function it_can_successfully_make_signed_documentation_pdf()
    {
        $this->provider->owner->npiRecord()->create(['npi' => **********]);
        $signature = Signature::factory()
            ->withFile()
            ->create([
                'full_name' => 'user signer',
                'signable_id' => $this->provider->owner->id,
                'signable_type' => User::class,
            ]);
        $this->provider->owner->load('signature');
        // assign provider user to order
        $this->order->providerUser()->associate($this->provider->owner);

        $this->setOrganizationContext($this->provider);
        $this->createMedicalPolicyFormResponseForOrder();

        $this->mock(FileStorageServiceInterface::class)
            ->shouldReceive('getStoredFile')->once()
            ->with($this->withShallowObjectMock($signature->imageFile))
            ->andReturn('http://example.com');

        $pdf = app(MakeCmnPDF::class)->execute(
            distributor: $this->order->distributor,
            providerUser: $this->order->providerUser,
            patient: $this->order->globalPatient->getPatient($this->provider),
            products: $this->order->products,
            provider: $this->provider,
            mpfResponses: $this->order->medicalPolicyResponses,
            signed: true,
            icd10Codes: $this->order->diagnosisCodes,
        );

        $this->assertInstanceOf(DomPDFPDF::class, $pdf);
    }

    #[Test]
    public function it_can_successfully_make_cmn_pdf_preview_without_order()
    {
        $patient = Patient::factory()->for($this->provider, 'organization')->create();
        $documentRequest = DocumentRequest::factory()
            ->for($this->distributor)
            ->for($patient->globalPatient)
            ->create([
                'type' => DocumentRequestTypeEnum::CMN,
                'provider_id' => $this->provider->id,
                'provider_user_id' => $this->provider->owner->id,
            ]);

        $signature = Signature::factory()
            ->withFile()
            ->for($this->provider->owner, 'signable')
            ->create(['full_name' => 'user signer', ]);

        $this->setOrganizationContext($this->provider);

        $this->mock(FileStorageServiceInterface::class)
            ->shouldReceive('getStoredFile')->once()
            ->with($this->withShallowObjectMock($signature->imageFile))
            ->andReturn('http://example.com');

        $pdf = app(MakeCmnPDF::class)->execute(
            distributor: $documentRequest->distributor,
            providerUser: $documentRequest->providerUser,
            patient: $patient,
            products: $documentRequest->products,
            provider: $this->provider,
            signed: true,
            documentRequestGlobalId: $documentRequest->getGlobalId(),
        );

        $this->assertInstanceOf(DomPDFPDF::class, $pdf);
    }

    #[Test]
    public function it_can_successfully_make_cmn_pdf_preview_without_order_with_hcpcs()
    {
        $patient = Patient::factory()->for($this->provider, 'organization')->create();
        $documentRequest = DocumentRequest::factory()
            ->for($this->distributor)
            ->for($patient->globalPatient)
            ->hasAttached(CMSHCPC::factory()->count(2), [], 'cmsHcpcs')
            ->create([
                'type' => DocumentRequestTypeEnum::CMN,
                'provider_id' => $this->provider->id,
                'provider_user_id' => $this->provider->owner->id,
            ]);

        $signature = Signature::factory()
            ->withFile()
            ->for($this->provider->owner, 'signable')
            ->create(['full_name' => 'user signer', ]);

        $this->setOrganizationContext($this->provider);

        $this->mock(FileStorageServiceInterface::class)
            ->shouldReceive('getStoredFile')->once()
            ->with($this->withShallowObjectMock($signature->imageFile))
            ->andReturn('http://example.com');

        $pdf = app(MakeCmnPDF::class)->execute(
            distributor: $documentRequest->distributor,
            providerUser: $documentRequest->providerUser,
            patient: $patient,
            products: $documentRequest->products,
            hcpcs: $documentRequest->cmsHcpcs,
            provider: $this->provider,
            signed: true,
            documentRequestGlobalId: $documentRequest->getGlobalId(),
        );

        $this->assertInstanceOf(DomPDFPDF::class, $pdf);
    }

    #[Test]
    public function it_can_successfully_make_preview_documentation_pdf()
    {
        $this->setOrganizationContext($this->distributor);

        $pdf = app(MakeCmnPDF::class)->execute(
            distributor: $this->order->distributor,
            providerUser: $this->order->providerUser,
            patient: $this->order->globalPatient->getPatient($this->provider),
            products: $this->order->products,
            provider: $this->provider,
            mpfResponses: $this->order->medicalPolicyResponses,
            signed: false,
        );

        $this->assertInstanceOf(DomPDFPDF::class, $pdf);
    }

    #[Test]
    public function it_can_successfully_make_preview_documentation_pdf_when_assigned_user_without_npi()
    {
        // test preview when assigned provider user does not have NPI
        $this->provider->owner->signature()->create(['full_name' => 'user signer']);

        $this->setOrganizationContext($this->distributor);

        $pdf = app(MakeCmnPDF::class)->execute(
            distributor: $this->order->distributor,
            providerUser: $this->order->providerUser,
            patient: $this->order->globalPatient->getPatient($this->provider),
            products: $this->order->products,
            provider: $this->provider,
            mpfResponses: $this->order->medicalPolicyResponses,
            signed: false,
        );

        $this->assertInstanceOf(DomPDFPDF::class, $pdf);
    }
}
