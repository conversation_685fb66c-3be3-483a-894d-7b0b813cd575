<?php

namespace Tests\Unit\Models;

use App\Models\Territory;
use App\Models\ZipCode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TerritoryTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_returns_all_territory_zip_codes()
    {
        $territory = Territory::factory()->withZipCodes(2)->create();
        $controlZipCode = ZipCode::factory()->create();

        Territory::factory()->create()->zipCodes()->attach($controlZipCode->id);

        $zipCodes = $territory->getTerritoriesZipCodes();

        $this->assertCount(2, $zipCodes);
        $this->assertTrue(in_array(Str::substr($territory->zipCodes[0]->zip_code, 0, 5), $zipCodes));
        $this->assertTrue(in_array(Str::substr($territory->zipCodes[1]->zip_code, 0, 5), $zipCodes));
        $this->assertFalse(in_array(Str::substr($controlZipCode->zip_code, 0, 5), $zipCodes));
    }
}
