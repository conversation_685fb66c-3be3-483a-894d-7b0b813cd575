<?php

namespace Tests\Unit\Console\Commands;

use App\Enums\DigitalJourneyTemplateTypeEnum;
use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\DistributorCampaignTypeEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Models\DigitalJourney;
use App\Models\DistributorCampaign;
use App\Models\EmailTemplate;
use App\Models\Lead;
use App\Models\MessageTemplate;
use App\Notifications\Mail\DigitalJourneyEmail;
use App\Notifications\Sms\Sms;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SendDigitalJourneyCommunicationsTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->distributor = $this->createDistributor();
    }

    #[Test]
    public function it_can_execute_command_with_message_and_email()
    {
        Notification::fake();
        Carbon::setTestNow(Carbon::parse('2025-08-18 10:00:00', config('app.sh_timezone'))); // Monday 10:00


        $daysFromCreatedDate = 2;
        $distributorCampaign = DistributorCampaign::factory()->for($this->distributor)->create([
            'type' => DistributorCampaignTypeEnum::LEAD,
            'status' => DistributorCampaignStatusEnum::ACTIVE,
        ]);
        $messageTemplate = MessageTemplate::factory()->for($distributorCampaign)->create([
            'is_active' => true,
            'status' => MessageTemplateStatusEnum::APPROVED,
            'type' => MessageTemplateTypeEnum::DIGITAL_JOURNEY,
            'body' => 'some text',
        ]);
        DigitalJourney::factory()->for($distributorCampaign)->for($messageTemplate, 'template')->create([
            'template_type' => DigitalJourneyTemplateTypeEnum::MESSAGE,
            'days_from_created_date' => $daysFromCreatedDate,
        ]);
        // Create DistributorEmailTemplate and use it for EmailTemplate
        $distributorEmailTemplate = \App\Models\DistributorEmailTemplate::factory()->create([
            'distributor_id' => $this->distributor->id,
            'created_by' => $this->distributor->owner->id,
        ]);
        $emailTemplate = EmailTemplate::factory()->for($distributorCampaign)->create([
            'is_active' => true,
            'distributor_template_id' => $distributorEmailTemplate->id,
        ]);
        DigitalJourney::factory()->for($distributorCampaign)->for($emailTemplate, 'template')->create([
            'template_type' => DigitalJourneyTemplateTypeEnum::EMAIL,
            'days_from_created_date' => $daysFromCreatedDate,
        ]);
        $correctLeadParams = [
            'created_date' => Carbon::now('UTC')->subDays($daysFromCreatedDate)->toDateString(),
            'sms_enabled' => true,
            'email_enabled' => true,
            'status' => LeadStatusEnum::OPEN,
        ];
        $correctLead = Lead::factory()->for($distributorCampaign)->createQuietly($correctLeadParams);
        $randomLeadWithCorrectParams = Lead::factory()->createQuietly($correctLeadParams);

        // canceled lead
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'status' => LeadStatusEnum::CANCELED,
        ]);
        // converted lead
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'status' => LeadStatusEnum::CONVERTED,
        ]);
        // too old lead
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'created_date' => Carbon::now('UTC')->subDays($daysFromCreatedDate + 1)->toDateString(),
        ]);
        // todays lead
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'created_date' => Carbon::now('UTC')->toDateString(),
        ]);
        // opted out lead
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'sms_enabled' => false,
            'email_enabled' => false,
        ]);


        // Default org settings
        $this->distributor->organizationSettings()->createMany([
            ['name' => 'communication_days_monday', 'value' => 'true', 'value_type' => 'string'],
            ['name' => 'communication_start_time', 'value' => '09:00', 'value_type' => 'time'],
            ['name' => 'communication_end_time', 'value' => '17:00', 'value_type' => 'time'],
        ]);

        Cache::forget("organization:{$this->distributor->id}:settings");

        $this->artisan('cron:send-digital-journey-communications')->assertSuccessful();

        Notification::assertSentTimes(
            Sms::class,
            1,
        );
        Notification::assertSentTo(
            [$correctLead],
            Sms::class,
        );
        Notification::assertNotSentTo(
            [$randomLeadWithCorrectParams],
            Sms::class,
        );

        Notification::assertSentTimes(
            DigitalJourneyEmail::class,
            1,
        );
        Notification::assertSentTo(
            [$correctLead],
            DigitalJourneyEmail::class,
        );
        Notification::assertNotSentTo(
            [$randomLeadWithCorrectParams],
            DigitalJourneyEmail::class,
        );
    }

    #[Test]
    public function it_can_execute_command_with_message()
    {
        Notification::fake();
        Carbon::setTestNow(Carbon::parse('2025-08-18 10:00:00', config('app.sh_timezone'))); // Monday 10:00



        $daysFromCreatedDate = 2;
        $distributorCampaign = DistributorCampaign::factory()->for($this->distributor)->create([
            'type' => DistributorCampaignTypeEnum::LEAD,
            'status' => DistributorCampaignStatusEnum::ACTIVE,
        ]);
        $messageTemplate = MessageTemplate::factory()->for($distributorCampaign)->create([
            'is_active' => true,
            'status' => MessageTemplateStatusEnum::APPROVED,
            'type' => MessageTemplateTypeEnum::DIGITAL_JOURNEY,
            'body' => 'some text',
        ]);
        DigitalJourney::factory()->for($distributorCampaign)->for($messageTemplate, 'template')->create([
            'template_type' => DigitalJourneyTemplateTypeEnum::MESSAGE,
            'days_from_created_date' => $daysFromCreatedDate,
        ]);
        $correctLeadParams = [
            'created_date' => Carbon::now('UTC')->subDays($daysFromCreatedDate)->toDateString(),
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'status' => LeadStatusEnum::OPEN,
        ];
        $correctLead = Lead::factory()->for($distributorCampaign)->createQuietly($correctLeadParams);
        $wronglead = Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'created_date' => Carbon::now('UTC')->toDateString(),
        ]);


        // Default org settings
        $this->distributor->organizationSettings()->createMany([
            ['name' => 'communication_days_monday', 'value' => 'true', 'value_type' => 'string'],
            ['name' => 'communication_start_time', 'value' => '09:00', 'value_type' => 'time'],
            ['name' => 'communication_end_time', 'value' => '17:00', 'value_type' => 'time'],
        ]);

        Cache::forget("organization:{$this->distributor->id}:settings");

        $this->artisan('cron:send-digital-journey-communications')->assertSuccessful();

        Notification::assertSentTimes(
            Sms::class,
            1,
        );
        Notification::assertSentTo(
            [$correctLead],
            Sms::class,
        );

        Notification::assertSentTimes(
            DigitalJourneyEmail::class,
            0,
        );
        Notification::assertNotSentTo(
            [$correctLead],
            DigitalJourneyEmail::class,
        );
    }

    #[Test]
    public function it_can_execute_command_with_email()
    {
        Notification::fake();
        Carbon::setTestNow(Carbon::parse('2025-08-18 10:00:00', config('app.sh_timezone'))); // Monday 10:00

        $daysFromCreatedDate = 2;
        $distributorCampaign = DistributorCampaign::factory()->for($this->distributor)->create([
            'type' => DistributorCampaignTypeEnum::LEAD,
            'status' => DistributorCampaignStatusEnum::ACTIVE,
        ]);
        // Create DistributorEmailTemplate and use it for EmailTemplate
        $distributorEmailTemplate = \App\Models\DistributorEmailTemplate::factory()->create([
            'distributor_id' => $this->distributor->id,
            'created_by' => $this->distributor->owner->id,
        ]);
        $emailTemplate = EmailTemplate::factory()->for($distributorCampaign)->create([
            'is_active' => true,
            'distributor_template_id' => $distributorEmailTemplate->id,
        ]);
        DigitalJourney::factory()->for($distributorCampaign)->for($emailTemplate, 'template')->create([
            'template_type' => DigitalJourneyTemplateTypeEnum::EMAIL,
            'days_from_created_date' => $daysFromCreatedDate,
        ]);
        $correctLeadParams = [
            'created_date' => Carbon::now('UTC')->subDays($daysFromCreatedDate)->toDateString(),
            'sms_enabled' => true,
            'email_enabled' => true,
            'call_enabled' => true,
            'email_enabled' => true,
            'status' => LeadStatusEnum::OPEN,
        ];
        $correctLead = Lead::factory()->for($distributorCampaign)->createQuietly($correctLeadParams);
        Lead::factory()->for($distributorCampaign)->createQuietly([
            ...$correctLeadParams,
            'created_date' => Carbon::now('UTC')->toDateString(),
        ]);

        // Default org settings
        $this->distributor->organizationSettings()->createMany([
            ['name' => 'communication_days_monday', 'value' => 'true', 'value_type' => 'string'],
            ['name' => 'communication_start_time', 'value' => '09:00', 'value_type' => 'time'],
            ['name' => 'communication_end_time', 'value' => '17:00', 'value_type' => 'time'],
        ]);

        Cache::forget("organization:{$this->distributor->id}:settings");

        $this->artisan('cron:send-digital-journey-communications')->assertSuccessful();
        Notification::assertSentTimes(
            Sms::class,
            0,
        );
        Notification::assertNotSentTo(
            [$correctLead],
            Sms::class,
        );

        Notification::assertSentTimes(
            DigitalJourneyEmail::class,
            1,
        );
        Notification::assertSentTo(
            [$correctLead],
            DigitalJourneyEmail::class,
        );
        $this->assertNotNull($emailTemplate->distributorTemplate, 'EmailTemplate is missing distributorTemplate relation');
        $this->assertNotNull($emailTemplate->distributorTemplate->html_content, 'DistributorEmailTemplate html_content is null');
        $this->assertNotEmpty($emailTemplate->distributorTemplate->html_content, 'DistributorEmailTemplate html_content is empty');

    }
}
