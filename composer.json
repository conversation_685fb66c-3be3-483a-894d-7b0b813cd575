{"name": "laravel/laravel", "type": "project", "version": "0.1.2", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "ext-imagick": "*", "ext-soap": "*", "ext-zip": "*", "aws/aws-sdk-php": "*", "barryvdh/laravel-dompdf": "^3.0", "ezyang/htmlpurifier": "^4.16", "fakerphp/faker": "^1.23", "firebase/php-jwt": "^6.10", "googleads/google-ads-php": "^29.0", "guzzlehttp/guzzle": "^7.8", "huddledigital/zendesk-laravel": "^3.9", "iio/libmergepdf": "^4.0", "intervention/image": "^3.5", "lanin/laravel-api-debugger": "*", "laravel/framework": "^11.20", "laravel/horizon": "^5.23", "laravel/passport": "^12.0", "laravel/slack-notification-channel": "^v3.2", "league/flysystem-aws-s3-v3": "^3.24", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^13.6", "pdphilip/opensearch": "^2.0", "sentry/sentry-laravel": "^4.4", "setasign/fpdi-tcpdf": "^2.3", "signalwire-community/signalwire": "^1.1", "spatie/laravel-permission": "^6.4", "synio/laravel-gmail-service-account-mail-driver": "^0.2.0", "thiagoalessio/tesseract_ocr": "^2.13", "tightenco/parental": "^1.4"}, "require-dev": {"laravel/pint": "^1.14", "laravel/tinker": "^2.9", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11", "spatie/laravel-ignition": "^2.5", "theanik/laravel-more-command": "^1.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Extensions/CustomHelpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "prefer-stable": true}