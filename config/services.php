<?php


$services = [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    /* Third Party Services' Configurations */
    'primary' => [
        'communications' => env('PRIMARY_COMMUNICATIONS_SERVICE'),
    ],

    /* Third Party Services' Configurations */
    'change-healthcare' => [
        'url' => env('CHANGE_HEALTHCARE_URL', 'https://apigw.changehealthcare.com'),
        'client-id' => env('CHANGE_HEALTHCARE_CLIENT_ID'),
        'secret' => env('CHANGE_HEALTHCARE_SECRET'),
    ],

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'npi-registry' => [
        'uri' => env('NPI_REGISTRY_API_URI', 'https://npiregistry.cms.hhs.gov/api'),
        'version' => env('NPI_REGISTRY_API_VERSION', '2.1'),
        'limit' => env('NPI_REGISTRY_API_LIMIT', 10),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
        'account_id' => env('AWS_ACCOUNT_ID'),
    ],

    'quicksight' => [
        'region' => env('AWS_DEFAULT_REGION'),
        'account_id' => env('AWS_ACCOUNT_ID'),
    ],

    'signal-wire' => [
        'fax-number' => env('SIGNALWIRE_FAX_NUMBER'),
        'project-id' => env('SIGNALWIRE_PROJECT_ID'),
        'space-url' => env('SIGNALWIRE_SPACE_URL'),
        'text-number' => env('SIGNALWIRE_TEXT_NUMBER'),
        'token' => env('SIGNALWIRE_TOKEN'),
        'signature' => env('SIGNALWIRE_SIGNATURE'),
        'fax-ttl' => (int) env('SIGNALWIRE_FAX_LINK_TTL', 7200),
        'log_from_number' => explode(',', env('SIGNALWIRE_LOG_FROM_NUMBERS', '')),
    ],

    'twilio' => [
        'account-sid' => env('TWILIO_ACCOUNT_SID'),
        'messaging-sid' => env('TWILIO_MESSAGING_SID'),
        'token' => env('TWILIO_AUTH_TOKEN'),
    ],

    'p-verify' => [
        'url' => env('P_VERIFY_URL', 'https://api.pverify.com'),
        'client-id' => env('P_VERIFY_CLIENT_ID'),
        'client-secret' => env('P_VERIFY_CLIENT_SECRET'),
    ],

    'edi-parser' => [
        'url' => env('EDI_PARSER_URL', 'https://api-preprod.shuttle.health/api'),
        'in-plan-network-values' => 'Y',
        'service-type-code' => '30,DM',
    ],

    'google' => [
        'api_key' => env('GOOGLE_API_KEY'),
        'maps_base_url' => env('GOOGLE_MAPS_API_BASE_URL', 'https://maps.googleapis.com/maps/api'),
    ],

    'cognito' => [
        'region' => env('COGNITO_CLIENT_REGION', 'us-east-1'),
        'user_pool_id' => env('COGNITO_USER_POOL_ID'),
        'client_id' => env('COGNITO_CLIENT_ID'),
        'client_secret' => env('COGNITO_CLIENT_SECRET'),
        'default_admin_password' => env('COGNITO_DEFAULT_ADMIN_PASSWORD'),
    ],

    'fax' => [
        'bucket_name' => env('FAX_BUCKET_NAME', 'shuttle-faxes-prv'),
        'prefix_unprocessed' => env('FAX_PREFIX_UNPROCESSED', 'unprocessed-faxes/'),
        'prefix_identified' => env('FAX_PREFIX_IDENTIFIED', 'identified-faxes/'),
        'prefix_unidentified' => env('FAX_PREFIX_UNIDENTIFIED', 'unidentified-faxes/'),
        'kms_key_id' => env('FAX_KMS_KEY_ID', null),
    ],
];

return $services;
