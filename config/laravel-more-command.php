<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Repository classes namespace
    |--------------------------------------------------------------------------
    |
    | This value defines the default namespace for created Repository classes.
    | For example if the value is 'App/Http', it will create repository classes
    | inside 'App/Http/Repositories' and class namespace will
    | 'App/Http/Repositories/{ClassName}'.
    |
    */
    'repository-namespace' => 'App',

    /*
    |--------------------------------------------------------------------------
    | Service classes namespace
    |--------------------------------------------------------------------------
    |
    | This value defines the default namespace for created Service classes.
    | For example if the value is 'App/Http', it will create repository classes
    | inside 'App/Http/Services' and class namespace will
    | 'App/Http/Services/{ClassName}'.
    |
    */
    'service-namespace' => 'App',
];
