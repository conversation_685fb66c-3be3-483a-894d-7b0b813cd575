<?php

use App\Extensions\RdsAuthHelper;
use App\Extensions\RdsAvailabilityHelper;
use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'pgsql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'write' => [
                'host' => [
                    env('DB_HOST', '127.0.0.1'),
                ],
            ],
            'read' => [
                'host' => [
                    RdsAvailabilityHelper::isHostAvailable(
                        env('DB_HOST_READONLY', '127.0.0.1'),
                        env('DB_PORT', '5432'),
                    )
                    ? env('DB_HOST_READONLY', '127.0.0.1')
                    : env('DB_HOST', '127.0.0.1'),
                ],
            ],
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_USE_IAM_AUTH', false)
                ? RdsAuthHelper::generateAuthToken(
                    env('DB_HOST', '127.0.0.1'),
                    env('DB_PORT', '5432'),
                    env('DB_USERNAME', ''),
                    env('AWS_DEFAULT_REGION', 'us-east-1'),
                )
                : env('DB_PASSWORD', ''), // Use IAM auth if enabled
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => env('DB_USE_IAM_AUTH', false) ? 'require' : 'prefer', // Use SSL if IAM auth is enabled
            'sticky' => true,
            'options' => [
                PDO::ATTR_PERSISTENT => true, // Enable persistent connections
            ],
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'anonymization' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_ANONYMIZATION_HOST', '127.0.0.1'),
            'port' => env('DB_ANONYMIZATION_PORT', '5432'),
            'database' => env('DB_ANONYMIZATION_DATABASE', 'forge'),
            'username' => env('DB_ANONYMIZATION_USERNAME', 'forge'),
            'password' => env('DB_ANONYMIZATION_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'opensearch' => [
            'driver' => 'opensearch',
            'hosts' => explode(',', env('OS_HOSTS', 'http://localhost:9200')),
            'basic_auth' => [
                'username' => env('OS_USERNAME', ''),
                'password' => env('OS_PASSWORD', ''),
            ],
            'sig_v4' => [
                'provider' => env('OS_SIG_V4_PROVIDER'),
                'region' => env('OS_SIG_V4_REGION'),
                'service' => env('OS_SIG_V4_SERVICE'),
            ],
            'ssl' => [
                'cert' => env('OS_SSL_CERT', ''),
                'cert_password' => env('OS_SSL_CERT_PASSWORD', ''),
                'key' => env('OS_SSL_KEY', ''),
                'key_password' => env('OS_SSL_KEY_PASSWORD', ''),
            ],
            'index_prefix' => env('OS_INDEX_PREFIX', false),
            'options' => [
                'ssl_verification' => env('OS_OPT_VERIFY_SSL', true),
                'retires' => env('OS_OPT_RETRIES'),
                'sniff_on_start' => env('OS_OPT_SNIFF_ON_START'),
                'port_in_host_header' => env('OS_OPT_PORT_HOST_HEADERS'),
            ],
            'query_log' => [
                'index' => false, //Or provide a name for the logging index ex: 'laravel_query_logs'
                'error_only' => true, //If false, then all queries are logged if the query_log index is set
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'context' => [
                'stream' => [
                    'verify_peer' => env('REDIS_SSL_VERIFY_PEER', false),
                ],
            ],
            'persistent' => true,
            // 'compression' => Redis::COMPRESSION_LZ4, // Potential option for implementation
        ],

        'default' => [
            'scheme' => env('REDIS_SCHEME', 'tcp'),
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'), // ElastiCache in a Cluster mode only supports DB 0
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_default_database_'), // Add a prefix for default connection
        ],

        'cache' => [
            'scheme' => env('REDIS_SCHEME', 'tcp'),
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '0'), // ElastiCache in a Cluster mode only supports DB 0
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_cache_database_'), // Add a prefix for cache connection
        ],

        'patient_communication' => [
            'scheme' => env('REDIS_SCHEME', 'tcp'),
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_PATIENT_COMMUNICATION_DB', '1'), // ElastiCache in a Cluster mode only supports DB 0
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_patient_communication_db_'), // Add a prefix for cache connection
        ],

    ],

    'hide_raw_sql_in_exceptions' => env('LOG_HIDE_RAW_SQL_IN_EXCEPTIONS', true),

];
