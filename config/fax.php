<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Fax Processing Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the fax identification and processing system.
    |
    */

    'bucket_name' => env('FAX_BUCKET_NAME', 'shuttle-faxes-prv'),

    'prefix_unprocessed' => env('FAX_PREFIX_UNPROCESSED', 'unprocessed-faxes/'),
    'prefix_identified' => env('FAX_PREFIX_IDENTIFIED', 'identified-faxes/'),
    'prefix_unidentified' => env('FAX_PREFIX_UNIDENTIFIED', 'unidentified-faxes/'),

    'kms_key_id' => env('FAX_KMS_KEY_ID', null),

    /*
    |--------------------------------------------------------------------------
    | Patient Matching Configuration
    |--------------------------------------------------------------------------
    */

    'patient_matching' => [
        'fuzzy_threshold' => 0.80, // Minimum weighted score for fuzzy match acceptance
        'name_similarity_threshold' => 0.3, // Minimum trigram similarity for name matching
        'max_fuzzy_candidates' => 5, // Maximum number of fuzzy match candidates to consider

        // Weighted scoring components (must sum to 1.0)
        'weights' => [
            'dob_exact' => 0.55,
            'name_similarity' => 0.30,
            'phone_match' => 0.05,
            'address_similarity' => 0.05,
            'insurance_similarity' => 0.05,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Order Identification Configuration
    |--------------------------------------------------------------------------
    */

    'order_identification' => [
        'open_order_statuses' => ['OPEN', 'IN_PROGRESS'],
        'product_match_threshold' => 0.5, // Minimum score for product matching
        'lab_test_match_threshold' => 0.3, // Minimum score for lab test matching
        'string_similarity_threshold' => 0.8, // Minimum similarity for string matching

        // Document request statuses that are considered "pending"
        'pending_document_request_statuses' => [
            'PENDING_DIGITAL',
            'PENDING_ANALOG',
            'SCHEDULED',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Document Types
    |--------------------------------------------------------------------------
    | These are the enum values that are stored in the database after mapping
    | from the external AI request values in FaxIdentificationController
    */

    'supported_document_types' => [
        App\Enums\DocumentRequestTypeEnum::CMN->value,
        App\Enums\DocumentRequestTypeEnum::CUSTOM->value,
        App\Enums\DocumentRequestTypeEnum::LAB->value,
        App\Enums\DocumentRequestTypeEnum::CHART_NOTES->value,
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Configuration
    |--------------------------------------------------------------------------
    */

    'job' => [
        'queue' => env('FAX_IDENTIFICATION_QUEUE', 'default'),
        'timeout' => 600, // 10 minutes
        'retry_delay' => [30, 120, 300], // 30 seconds, 2 minutes, 5 minutes
        'max_attempts' => 3,
    ],

    /*
    |--------------------------------------------------------------------------
    | POC Configuration
    |--------------------------------------------------------------------------
    */

    'poc' => [
        'enabled' => env('FAX_POC_ENABLED', false),
        'queue' => env('FAX_POC_QUEUE', 'poc-fax-processing'),
        'delay_seconds' => env('FAX_POC_DELAY_SECONDS', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Cleanup Configuration
    |--------------------------------------------------------------------------
    */

    'cleanup' => [
        'retention_days' => 90, // Days to keep files before cleanup
        'enabled' => env('FAX_CLEANUP_ENABLED', true),
    ],
];
