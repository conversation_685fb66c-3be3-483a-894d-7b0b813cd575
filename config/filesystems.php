<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    /* this filesystem is utilized for storing public images within the application (product and category images) */
    'public' => env('PUBLIC_CONTENT_DISK', 'local-public-content'),

    /* this filesystem is utilized for files with restricted access (user profile pictures) */
    'private' => env('PRIVATE_CONTENT_DISK', 'local-private-content'),

    /* this filesystem is utilized for documentation files with restricted access (CMN documentation) */
    'documentation' => env('DOCUMENTATION_DISK', 'local-documentation'),

    'temporary' => env('TMP_DISK', 'local-tmp-content'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [
        'local-tmp-content' => [
            'driver' => 'local',
            'root' => storage_path('app/tmp'),
            'throw' => true,
        ],

        'local-public-content' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL') . '/storage/public/',
            'visibility' => 'public',
            'throw' => true,
        ],

        'local-private-content' => [
            'driver' => 'local',
            'root' => storage_path('app/private'),
            'url' => env('APP_URL') . '/storage/private/',
            'visibility' => 'private',
            'throw' => true,
        ],

        'local-documentation' => [
            'driver' => 'local',
            'root' => storage_path('app/documentation'),
            'url' => env('APP_URL') . '/storage/documentation/',
            'visibility' => 'private',
            'throw' => true,
        ],

        /** S3 BUCKETS */
        's3-public-content' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID', null),
            'secret' => env('AWS_SECRET_ACCESS_KEY', null),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'bucket' => env('AWS_BUCKET_PUBLIC_CONTENT'),
            'url' => env('CDN_URL'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'visibility' => 'private',
            'throw' => true,
        ],

        's3-private-content' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID', null),
            'secret' => env('AWS_SECRET_ACCESS_KEY', null),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'bucket' => env('AWS_BUCKET_PRIVATE_CONTENT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'visibility' => 'private',
            'throw' => true,
        ],

        's3-documentation' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID', null),
            'secret' => env('AWS_SECRET_ACCESS_KEY', null),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'bucket' => env('AWS_BUCKET_DOCUMENTATION'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'visibility' => 'private',
            'throw' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom configs
    |--------------------------------------------------------------------------
    */

    'cdn_url' => env('CDN_URL'),
];
