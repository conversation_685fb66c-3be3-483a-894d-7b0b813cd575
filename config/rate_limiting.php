<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains rate limiting configurations for various API endpoints
    | to prevent abuse, ensure fair usage, and maintain system stability.
    |
    */

    'quicksight' => [
        'embed_url' => [
            /*
            |--------------------------------------------------------------------------
            | Per-User Rate Limit
            |--------------------------------------------------------------------------
            |
            | Maximum number of embed URL requests allowed per user per minute.
            | This helps prevent individual users from overwhelming the system.
            |
            */
            'limit_per_minute' => (int) env('QUICKSIGHT_RATE_LIMIT', 10),

            /*
            |--------------------------------------------------------------------------
            | Cache Hit Weight
            |--------------------------------------------------------------------------
            |
            | Weight applied to cache hits vs new URL generations.
            | Cache hits consume fewer resources and are counted at a lower rate.
            |
            */
            'cache_hit_weight' => (float) env('QUICKSIGHT_CACHE_HIT_WEIGHT', 0.2),

            /*
            |--------------------------------------------------------------------------
            | Bypass Roles
            |--------------------------------------------------------------------------
            |
            | User roles that can bypass rate limiting restrictions.
            | Useful for admin users and system processes.
            |
            */
            'bypass_roles' => explode(',', env('QUICKSIGHT_BYPASS_ROLES', 'admin,system')),

            /*
            |--------------------------------------------------------------------------
            | Per-Distributor Rate Limit
            |--------------------------------------------------------------------------
            |
            | Maximum number of embed URL requests allowed per distributor per minute.
            | This prevents one distributor from impacting others.
            |
            */
            'distributor_limit_per_minute' => (int) env('QUICKSIGHT_DISTRIBUTOR_RATE_LIMIT', 30),

            /*
            |--------------------------------------------------------------------------
            | IP-based Rate Limit Multiplier
            |--------------------------------------------------------------------------
            |
            | Multiplier for IP-based rate limiting as additional protection.
            | IP limit = user limit * multiplier
            |
            */
            'ip_limit_multiplier' => (int) env('QUICKSIGHT_IP_LIMIT_MULTIPLIER', 2),

            /*
            |--------------------------------------------------------------------------
            | Decay Time
            |--------------------------------------------------------------------------
            |
            | Time in minutes for rate limit window decay.
            |
            */
            'decay_minutes' => (int) env('QUICKSIGHT_RATE_LIMIT_DECAY_MINUTES', 1),

            /*
            |--------------------------------------------------------------------------
            | Enable Logging
            |--------------------------------------------------------------------------
            |
            | Whether to log rate limit violations for monitoring purposes.
            |
            */
            'enable_logging' => env('QUICKSIGHT_RATE_LIMIT_LOGGING', true),
        ],
    ],
];
