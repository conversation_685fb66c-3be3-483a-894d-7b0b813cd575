<?php

use App\Enums\PatientProviderUserAssociationType;

return [
    'uuidRegex' => '^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$',
    'providerUserAssociationType' => sprintf(
        '^%s$',
        implode('|', PatientProviderUserAssociationType::getValues()),
    ),
    'numericParamKeys' => [
        'campaign',
        'distributor',
        'distributorCampaign',
        'document',
        'documentId',
        'documentRequest',
        'documentRequestId',
        'fileImport',
        'fax',
        'globalSetting',
        'id',
        'lead',
        'logId',
        'manufacturer',
        'medicalPolicyForm',
        'medicalPolicyResponse',
        'medicalPolicyResponseId',
        'messageTemplate',
        'order',
        'orderId',
        'organization_setting',
        'patient',
        'payerId',
        'product',
        'productCategory',
        'provider',
        'providerUser',
        'user',
        'userId',
    ],
];
