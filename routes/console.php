<?php

use App\Console\Commands\Brightree\UpdateOrderShipped;
use App\Console\Commands\Communications\SendDigitalJourneyCommunications;
use App\Console\Commands\Communications\SendScheduledRenewalCommunications;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schedule;

// Helper function for conditional Sentry monitoring
if (!function_exists('addSentryIfProduction')) {
    function addSentryIfProduction($schedule)
    {
        if (app()->environment('production')) {
            return $schedule->sentryMonitor();
        }

        return $schedule;
    }
}

// Jobs that should be monitored only in production
addSentryIfProduction(Schedule::command('cache:prune-stale-tags')->hourly());
addSentryIfProduction(Schedule::command('cron:expired-data-cleaner')->daily());
addSentryIfProduction(Schedule::command('queue:prune-batches')->daily());

if (config('app.env') !== 'local') {
    addSentryIfProduction(Schedule::command(UpdateOrderShipped::class)->everyFourHours());
    // TODO: TESTING - Revert back to addSentryIfProduction() wrapper after testing
    Schedule::command(SendDigitalJourneyCommunications::class)->hourly()->sentryMonitor();
    addSentryIfProduction(Schedule::command(SendScheduledRenewalCommunications::class)->weekdays()->at('13:10')->timezone(config('app.sh_timezone')));

    if (app()->environment('production')) {
        // This job should only run AND be monitored in production
        Schedule::command('fax:fetch-logs ' . Carbon::now()->format('Y-m-d'))->everyFifteenMinutes()->sentryMonitor();
    }
}
