<?php

use App\Console\Commands\Communications\SendDigitalJourneyCommunications;
use App\Http\Controllers\AobController;
use App\Http\Controllers\Api\PatientBrightreeUpdateController;
use App\Http\Controllers\CMSArticleController;
use App\Http\Controllers\CMSHCPCController;
use App\Http\Controllers\CMSLCDController;
use App\Http\Controllers\Distributor\ImportRenewalRequestController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\GoogleMapsController;
use App\Http\Controllers\HealthCheckController;
use App\Http\Controllers\IdentificationTokenController;
use App\Http\Controllers\NpiController;
use App\Http\Controllers\PayerController;
use App\Http\Controllers\Public\AccountOwnerController;
use App\Http\Controllers\SchemaController;
use App\Http\Controllers\SignalWireController;
use App\Http\Controllers\UnsubscribeController;
use App\Http\Controllers\User\MeController;
use App\Http\Controllers\User\SignatureController;
use App\Http\Controllers\WHOICD10Controller;
use App\Http\Controllers\ZendeskSsoController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('passport/token', [
    'uses' => '\Laravel\Passport\Http\Controllers\AccessTokenController@issueToken',
    'as' => 'passport.token',
    'middleware' => 'throttle:login',
    'withoutMiddleware' => 'throttle:api',
]);

Route::get('health', [HealthCheckController::class, 'check'])->name('api.health-check');

Route::get('invitation', [IdentificationTokenController::class, 'getInvitation'])->name('get-invitation');
Route::post('accept-invitation', [IdentificationTokenController::class, 'acceptInvitation'])->name('accept-invitation');
Route::post('email-communications/unsubscribe', [IdentificationTokenController::class, 'unsubscribeEmailCommunication'])->name('email-communications.unsubscribe');

Route::get('files/faxes/{token}/{uuid}.{ext}', [FileController::class, 'getFaxWithToken'])->name('files.fax-with-token.download');
Route::post('signal-wire/webhooks/inbound-message', [SignalWireController::class, 'inboundMessageWebhook'])->name('signal-wire.inbound-message-webhook');
Route::post('signal-wire/webhooks/inbound-fax', [SignalWireController::class, 'inboundFaxWebhook'])->name('signal-wire.inbound-fax-webhook');

Route::post('signal-wire/webhooks/fax-status', [SignalWireController::class, 'faxStatusWebhook'])->name('signal-wire.fax-status-webhook');
Route::post('signal-wire/webhooks/sms-status', [SignalWireController::class, 'smsStatusWebhook'])->name('signal-wire.sms-status-webhook');


Route::get('renewal-requests/imports/{type}/csv-template/preview', [ImportRenewalRequestController::class, 'previewCsvTemplate'])->name('renewal-requests.imports.csv-template.preview');
Route::get('renewal-requests/imports/{type}/csv-template/download', [ImportRenewalRequestController::class, 'downloadCsvTemplate'])->name('renewal-requests.imports.csv-template.download');

Route::post('aob/submit', [AobController::class, 'submitAobForm'])->name('aob.submit');
Route::post('aob/decline', [AobController::class, 'declineAobForm'])->name('aob.decline');
Route::get('aob', [AobController::class, 'showAobForm'])->name('aob.show');

Route::get('unsubscribe', [UnsubscribeController::class, 'unsubscribe'])
    ->name('unsubscribe');

// Brightree Patient Updates - No middleware required
Route::get('brightree/security-group-updates', [PatientBrightreeUpdateController::class, 'processSecurityGroupUpdates'])
    ->name('brightree.security-group-updates');
Route::get('brightree/optin-status-updates', [PatientBrightreeUpdateController::class, 'processOptinStatusUpdates'])
    ->name('brightree.optin-status-updates');
Route::get('resubscribe', [UnsubscribeController::class, 'resubscribe'])
    ->name('resubscribe');
Route::get('dummy-unsubscribe', fn () => response()->json([
    'message' => 'This route is used only for signature verification.',
], 403))->name('dummy.unsubscribe');

Route::group([
    'middleware' => [
        'cognito',
    ],
], function () {
    Route::get('auth/check', function () {
        return response()->json();
    })->name('auth.check');

    /** Authed User */
    Route::prefix('me')->name('me.')->group(function () {
        Route::get('', [MeController::class, 'show'])->name('show');
        Route::put('', [MeController::class, 'update'])->name('update');
        Route::post('image', [MeController::class, 'uploadImage'])->name('upload-image');
        Route::resource('signature', SignatureController::class)->only(['index', 'store']);
    });

    /** CMS Data */
    Route::prefix('cms')->name('cms.')->group(function () {
        Route::get('articles', [CMSArticleController::class, 'index'])->name('articles.index');
        Route::get('hcpcs', [CMSHCPCController::class, 'index'])->name('hcpcs.index');
        Route::get('lcds', [CMSLCDController::class, 'index'])->name('lcds.index');
    });

    /** Payers  Data */
    Route::prefix('payers')->name('payers.')->group(function () {
        Route::get('', [PayerController::class, 'index'])->name('index');
        Route::get('search', [PayerController::class, 'searchPayer'])->name('search');
    });


    /** WHO Data */
    Route::get('who/icd-10s', [WHOICD10Controller::class, 'index'])->name('who.icd-10s.index');

    /** NPI registry proxy */
    Route::get('npi-registry', [NpiController::class, 'getByNumber'])->name('npi-registry.get-by-number');

    /** Schema */
    Route::name('schema.')->group(function () {
        Route::get('distributors/users/types', [SchemaController::class, 'distributorUserTypesIndex'])->name('distributors.users.types.index');
        Route::get('manufacturers/users/types', [SchemaController::class, 'manufacturerUserTypesIndex'])->name('manufacturers.users.types.index');
        Route::get('providers/users/types', [SchemaController::class, 'providerUserTypesIndex'])->name('providers.users.types.index');
        Route::get('providers/users/positions', [SchemaController::class, 'providerUserPositionsIndex'])->name('providers.users.positions.index');
        Route::get('shuttle/users/types', [SchemaController::class, 'shuttleUserTypesIndex'])->name('shuttle.users.types.index');
        Route::get('addresses/types', [SchemaController::class, 'addressesTypesIndex'])->name('addresses.types.index');
        Route::get('orders/activity-logs/types', [SchemaController::class, 'orderActivityLogTypesIndex'])->name('orders.activity-logs.types.index');
        Route::get('orders/cancelation-reasons', [SchemaController::class, 'orderCancelationReasonsIndex'])->name('orders.cancelation-reasons.index');
        Route::get('orders/statuses', [SchemaController::class, 'orderStatusesIndex'])->name('orders.statuses.index');
        Route::get('orders/types', [SchemaController::class, 'orderTypesIndex'])->name('orders.types.index');
        Route::get('orders/sources/types', [SchemaController::class, 'orderSourceTypesIndex'])->name('orders.source-types.index');
        Route::get('payers/plans/types', [SchemaController::class, 'payerPlanTypesIndex'])->name('payers.plans.types.index');
        Route::get('payers/services/types', [SchemaController::class, 'payerServiceTypesIndex'])->name('payers.services.types.index');
        Route::get('payers/types', [SchemaController::class, 'payerTypesIndex'])->name('payers.types.index');
        Route::get('products/duration-units', [SchemaController::class, 'productsDurationUnitsIndex'])->name('products.duration-units.index');
        Route::get('products/measure-units', [SchemaController::class, 'productsMeasureUnitsIndex'])->name('products.measure-units.index');
        Route::get('products/narrative-units', [SchemaController::class, 'productsNarrativeUnitsIndex'])->name('products.narrative-units.index');
        Route::get('patient-payer/eligibility-check-statuses', [SchemaController::class, 'eligibilityCheckStatusesIndex'])->name('patient-payer.eligibility-check-statuses.index');
    });

    /** Files */
    Route::prefix('files')->name('files.')->group(function () {
        Route::get('public/{uuid}.{ext}', [FileController::class, 'getPublicImage'])->name('public-image.show');
        Route::get('users/{uuid}.{ext}', [FileController::class, 'getUserImage'])->name('user-image.show');
        Route::get('signatures/{uuid}.{ext}', [FileController::class, 'getSignatureImage'])->name('signature-image.show');
        Route::get('documents/{uuid}.{ext}', [FileController::class, 'getOrderDocument'])->name('document.show');
        Route::get('faxes/{uuid}.{ext}', [FileController::class, 'getFax'])->name('fax.show');
        Route::get('insurance-cards/{uuid}.{ext}', [FileController::class, 'getInsuranceCard'])->name('insurance-card.show');
    });

    /** Zendesk SSO */
    // authenticated users only
    Route::post('zendesk-sso/jwt', [ZendeskSsoController::class, 'createJwt'])->name('zendesk-sso.create-jwt');

    /** Google Maps API */
    Route::get('/timezone', [GoogleMapsController::class, 'getTimezone'])->name('google-maps.timezone');
});


Route::get('account-owner/verify/{token}', [AccountOwnerController::class, 'verify'])->name('account-owner.verify')->middleware(app()->runningUnitTests() ? [] : 'throttle:5,1'); // 5 requests per 1 minute;
Route::post('account-owner/update/{token}', [AccountOwnerController::class, 'update'])->name('account-owner.update')->middleware(app()->runningUnitTests() ? [] : 'throttle:5,1'); // 5 requests per 1 minute;



// Uncomment for debugging. Digital Journey Cron Job debugging
// Route::get('/debug', function () {
//     $validHash = 'd8b59c48f0ec5415caf5100b2d8045d33773efe3'; // better: move to .env
//     $request = request();

//     if ($request->query('password') !== $validHash) {
//         abort(403, 'Unauthorized.');
//     }

//     // Resolve the command and call handle() directly
//     $command = app(SendDigitalJourneyCommunications::class);

//     $leads = $command->debug();

//     // If you want to capture its return value
//     $result = $command->handle();

//     return response()->json([
//         'status' => 'ok',
//         'leads' => $leads,
//         'result' => $result,
//     ]);
// });
