<?php

use App\Enums\OrganizationTypeEnum;
use App\Http\Controllers\PayerController;
use App\Http\Controllers\Provider\AddressController;
use App\Http\Controllers\Provider\DocumentRequest\DocumentController as DocumentRequestDocumentController;
use App\Http\Controllers\Provider\DocumentRequest\ProductController as DocumentRequestProductController;
use App\Http\Controllers\Provider\DocumentRequestController;
use App\Http\Controllers\Provider\GlobalSearchController;
use App\Http\Controllers\Provider\ManufacturerController;
use App\Http\Controllers\Provider\Order\ActivityLogController as OrderActivityController;
use App\Http\Controllers\Provider\Order\DistributorController as OrderDistributorController;
use App\Http\Controllers\Provider\Order\DocumentController as OrderDocumentController;
use App\Http\Controllers\Provider\Order\MedicalPolicyController as OrderMedicalPolicyController;
use App\Http\Controllers\Provider\Order\ProductController as OrderProductController;
use App\Http\Controllers\Provider\OrderController;
use App\Http\Controllers\Provider\Patient\AddressController as PatientAddressController;
use App\Http\Controllers\Provider\Patient\PayerController as PatientPayerController;
use App\Http\Controllers\Provider\PatientController;
use App\Http\Controllers\Provider\ProductCategoryController;
use App\Http\Controllers\Provider\ProductController;
use App\Http\Controllers\Provider\ProviderController;
use App\Http\Controllers\Provider\UserController;
use App\Http\Controllers\User\ResetPasswordController;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => [
    'cognito',
    'cognito-scope:int-hcp-scope',
    'organization:' . OrganizationTypeEnum::PROVIDER->value,
]], function () {
    /** PROVIDERS */
    Route::prefix('providers/{provider}')->name('providers.')->group(function () {
        Route::get('', [ProviderController::class, 'show'])->name('show');
        Route::put('', [ProviderController::class, 'update'])->name('update');
        Route::resource('addresses', AddressController::class, ['name' => 'addresses']);

        Route::post('payers/custom', [PayerController::class, 'storeCustom'])->name('payers.store-custom');

        /** Orders */
        Route::resource('orders', OrderController::class)->except(['destroy']);
        Route::prefix('orders/{order}')->name('orders.')->group(function () {
            Route::put('assign-user', [OrderController::class, 'assignUser'])->name('assign-user');
            Route::put('assign-distributor', [OrderController::class, 'assignDistributor'])->name('assign-distributor');
            Route::get('available-distributors', [OrderDistributorController::class, 'available'])->name('available-distributors');
            Route::put('cancel', [OrderController::class, 'cancel'])->name('cancel');
            Route::get('documentation', [OrderController::class, 'documentation'])->name('documentation');
            Route::get('documentation/preview', [OrderController::class, 'documentationPreview'])->name('documentation-preview');
            Route::get('certificate/preview', [OrderController::class, 'certificatePreview'])->name('certificate-preview');
            Route::resource('logs', OrderActivityController::class);
            Route::resource('products', OrderProductController::class);
            Route::post('sign', [OrderController::class, 'sign'])->name('sign');
            Route::get('escalation-timeline', [OrderController::class, 'escalationTimeline'])->name('escalation-timeline');
            //Diagnosis Codes
            Route::post('diagnosis-codes', [OrderController::class, 'addDiagnosisCodesToOrder'])->name('diagnosis-codes.attach');


            /** Medical Policy Forms */
            Route::prefix('medical-policies')->name('medical-policies.')->group(function () {
                Route::get('/', [OrderMedicalPolicyController::class, 'index'])->name('index');
                Route::get('/{medicalPolicyResponse}', [OrderMedicalPolicyController::class, 'show'])->name('show');
                Route::post('/', [OrderMedicalPolicyController::class, 'store'])->name('store');
                Route::post('/{medicalPolicyResponse}', [OrderMedicalPolicyController::class, 'update'])->name('update');
                Route::delete('/{medicalPolicyResponse}', [OrderMedicalPolicyController::class, 'destroy'])->name('destroy');
            });
        });
        Route::get('orders/counts', [OrderController::class, 'counts'])->name('orders.counts');

        /** Documents */
        Route::prefix('orders/{orderId}/documents')->name('orders.documents.')->group(function () {
            Route::get('/', [OrderDocumentController::class, 'index'])->name('index');
            Route::get('/{documentId}', [OrderDocumentController::class, 'show'])->name('show');
        });

        /** Document requests */
        Route::prefix('document-requests')->name('document-requests.')->group(function () {
            Route::get('', [DocumentRequestController::class, 'index'])->name('index');
            Route::get('counts', [DocumentRequestController::class, 'counts'])->name('counts');
            Route::get('{documentRequest}', [DocumentRequestController::class, 'show'])->name('show');
            Route::post('{documentRequest}/cmn-preview', [DocumentRequestController::class, 'CmnPreview'])->name('cmn-preview');
            Route::put('{documentRequest}/assign-user', [DocumentRequestController::class, 'assignUser'])->name('assign-user');
            Route::put('{documentRequest}/unassign-user', [DocumentRequestController::class, 'unassignUser'])->name('unassign-user');
            Route::put('{documentRequest}/decline', [DocumentRequestController::class, 'decline'])->name('decline');
            Route::get('{documentRequest}/cmn/preview', [DocumentRequestController::class, 'cmnPrescriptionRequestPreview'])->name('cmn.preview');
            Route::post('{documentRequest}/sign', [DocumentRequestController::class, 'sign'])->name('sign');
            Route::post('{documentRequest}/sync-icd-10', [DocumentRequestController::class, 'syncIcd10s'])->name('sync-icd-10');

            Route::resource('{documentRequest}/products', DocumentRequestProductController::class);
            /** Documents */
            Route::prefix('{documentRequest}/documents')->name('documents.')->group(function () {
                Route::get('/', [DocumentRequestDocumentController::class, 'index'])->name('index');
                Route::get('{documentId}', [DocumentRequestDocumentController::class, 'show'])->name('show');
                Route::post('upload', [DocumentRequestDocumentController::class, 'upload'])->name('upload');
            });
        });

        /** Patients */
        Route::resource('patients', PatientController::class)->only('index', 'show', 'store', 'update');
        Route::get('patients/duplicates', [PatientController::class, 'findDuplicates'])->name('patients.duplicates');
        Route::prefix('patients/{patient}/')->name('patients.')->group(function () {
            Route::patch('update-status', [PatientController::class, 'updateStatus'])->name('update-status');
            Route::get('activity-list', [PatientController::class, 'activityList'])->name('activity.index');
            Route::patch('cancel-activity', [PatientController::class, 'cancelActivity'])->name('cancel-activity');
            Route::resource('addresses', PatientAddressController::class);
            Route::resource('payers', PatientPayerController::class);
            Route::put('payers', [PatientPayerController::class, 'updatePayers'])->name('payers.update-payers');
            Route::post('payers/{payerId}/check-eligibility', [PatientPayerController::class, 'checkEligibility'])->name('payers.check-eligibility');
            Route::get('payers/{payerId}/get-eligibility-by-id/{eligibilityCheckId}', [PatientPayerController::class, 'getEligibilityById'])->name('payers.get-eligibility-by-id');
            Route::patch('payers/{payerId}/archive', [PatientPayerController::class, 'archive'])->name('payers.archive');
        });

        /** Products */
        Route::get('product-categories', [ProductCategoryController::class, 'index'])->name('product-categories.index');
        Route::get('product-categories/{productCategory}', [ProductCategoryController::class, 'show'])->name('product-categories.show');
        Route::get('products', [ProductController::class, 'index'])->name('products.index');
        Route::get('products/{product}', [ProductController::class, 'show'])->name('products.show');

        /** Users */
        Route::resource('users', UserController::class)->withTrashed()->except(['edit']);
        Route::get('users/select-list', [UserController::class, 'getSelectList'])->name('users.select-list');
        Route::prefix('users/{userId}')->name('users.')->group(function () {
            Route::put('activate', [UserController::class, 'activate'])->name('activate');
            Route::post('resend-invitation', [UserController::class, 'resendInvitation'])->name('resend-invitation');
            Route::post('reset-password', [ResetPasswordController::class, 'providerResetPassword'])->name('password.reset');
        });

        /** Distributors */
        Route::get('distributors', [ProviderController::class, 'distributors'])->name('distributors');

        /** Manufacturers */
        Route::get('/manufacturers/list', [ManufacturerController::class, 'list'])->name('manufacturers.list');

        Route::prefix('global-search')->name('global-search.')->group(function () {
            Route::get('lead-patient-list', [GlobalSearchController::class, 'leadPatientList'])->name('lead-patient-list');
            Route::get('lead-order-customer-list', [GlobalSearchController::class, 'leadPatientList'])->name('lead-order-customer-list');
        });
    });
});
