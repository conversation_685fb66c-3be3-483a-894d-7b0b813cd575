<?php

use App\Enums\OrganizationTypeEnum;
use App\Http\Controllers\API\QuicksightController;
use App\Http\Controllers\Api\V1\FaxIdentificationController;
use App\Http\Controllers\Distributor\AddressController;
use App\Http\Controllers\Distributor\Area\DistrictController;
use App\Http\Controllers\Distributor\Area\RegionController;
use App\Http\Controllers\Distributor\Area\TerritoryController;
use App\Http\Controllers\Distributor\BrightreeController;
use App\Http\Controllers\Distributor\Campaign\CustomFieldController;
use App\Http\Controllers\Distributor\Campaign\DigitalJourneyController;
use App\Http\Controllers\Distributor\Campaign\EmailTemplateController;
use App\Http\Controllers\Distributor\Campaign\Lead\ActivityLogController as LeadActivityController;
use App\Http\Controllers\Distributor\Campaign\Lead\AddressController as LeadAddressController;
use App\Http\Controllers\Distributor\Campaign\Lead\InsuranceCardRequestController as LeadInsuranceCardRequestController;
use App\Http\Controllers\Distributor\Campaign\Lead\PayerController as LeadPayerController;
use App\Http\Controllers\Distributor\Campaign\Lead\ProductController as LeadProductController;
use App\Http\Controllers\Distributor\Campaign\Lead\SameAndSimilarChecksController;
use App\Http\Controllers\Distributor\Campaign\Lead\SameAndSimilarHistoryController as LeadSameAndSimilarHistoryController;
use App\Http\Controllers\Distributor\Campaign\LeadController;
use App\Http\Controllers\Distributor\Campaign\MessageTemplateController;
use App\Http\Controllers\Distributor\CampaignController;
use App\Http\Controllers\Distributor\CommunicationPreferenceController;
use App\Http\Controllers\Distributor\DiagnosisCodesController;
use App\Http\Controllers\Distributor\DistributorController;
use App\Http\Controllers\Distributor\DistributorDashboardController;
use App\Http\Controllers\Distributor\DistributorLanguageController;
use App\Http\Controllers\Distributor\DocumentRequest\CmsHcpcController;
use App\Http\Controllers\Distributor\DocumentRequest\DocumentController as DocumentRequestDocumentController;
use App\Http\Controllers\Distributor\DocumentRequest\ProductController as DocumentRequestProductController;
use App\Http\Controllers\Distributor\DocumentRequestController;
use App\Http\Controllers\Distributor\FacilityController;
use App\Http\Controllers\Distributor\FaxController;
use App\Http\Controllers\Distributor\FilterSetController;
use App\Http\Controllers\Distributor\GlobalSearchController;
use App\Http\Controllers\Distributor\ImageGalleryController;
use App\Http\Controllers\Distributor\ImportRenewalRequestController;
use App\Http\Controllers\Distributor\LeadController as DistributorLeadController;
use App\Http\Controllers\Distributor\ManufacturerController;
use App\Http\Controllers\Distributor\NpiController;
use App\Http\Controllers\Distributor\OnDemandMessageTemplateController;
use App\Http\Controllers\Distributor\Order\ActivityLogController as OrderActivityController;
use App\Http\Controllers\Distributor\Order\DocumentController as OrderDocumentController;
use App\Http\Controllers\Distributor\Order\DocumentRequestController as OrderDocumentRequestController;
use App\Http\Controllers\Distributor\Order\MedicalPolicyController as OrderMedicalPolicyController;
use App\Http\Controllers\Distributor\Order\OrderNotesController;
use App\Http\Controllers\Distributor\Order\PatientController as OrderPatientController;
use App\Http\Controllers\Distributor\Order\ProductController as OrderProductController;
use App\Http\Controllers\Distributor\Order\SameAndSimilarController as OrderSameAndSimilarController;
use App\Http\Controllers\Distributor\Order\SameAndSimilarHistoryController;
use App\Http\Controllers\Distributor\Order\ShippingController as OrderShippingController;
use App\Http\Controllers\Distributor\Order\TaskController as OrderTaskController;
use App\Http\Controllers\Distributor\OrderController;
use App\Http\Controllers\Distributor\OrderSourceController;
use App\Http\Controllers\Distributor\OrganizationSettingsController;
use App\Http\Controllers\Distributor\Patient\ActivityLogController as PatientActivityLogController;
use App\Http\Controllers\Distributor\Patient\AddressController as PatientAddressController;
use App\Http\Controllers\Distributor\Patient\DocumentController as PatientDocumentController;
use App\Http\Controllers\Distributor\Patient\InsuranceCardRequestController as PatientInsuranceCardRequestController;
use App\Http\Controllers\Distributor\Patient\PayerController as PatientPayerController;
use App\Http\Controllers\Distributor\Patient\ProviderUserController as PatientProviderUserController;
use App\Http\Controllers\Distributor\PatientController;
use App\Http\Controllers\Distributor\PecosController;
use App\Http\Controllers\Distributor\ProductCategoryController;
use App\Http\Controllers\Distributor\ProductController;
use App\Http\Controllers\Distributor\ProviderController;
use App\Http\Controllers\Distributor\ProviderUserController;
use App\Http\Controllers\Distributor\ReportController;
use App\Http\Controllers\Distributor\SalesDashboardController;
use App\Http\Controllers\Distributor\SignalWireSmsLogsController;
use App\Http\Controllers\Distributor\TimeZonesController;
use App\Http\Controllers\Distributor\UserController;
use App\Http\Controllers\DistributorEmailTemplateController;
use App\Http\Controllers\MessageConfigurationController;
use App\Http\Controllers\OnDemandMessageController;
use App\Http\Controllers\PayerController;
use App\Http\Controllers\PharmacyPayerController;
use App\Http\Controllers\User\ResetPasswordController;
use App\Models\Distributor;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => [
        'cognito',
        'cognito-scope:int-dme-scope',
        'organization:' . OrganizationTypeEnum::DISTRIBUTOR->value,
    ],
], function () {
    /** DISTRIBUTORS */
    Route::prefix('distributors/{distributor}')->name('distributors.')->group(function () {

        /** Fax Identification */
        Route::prefix('faxes/identification')->name('faxes.identification.')->group(function () {
            Route::get('', [FaxIdentificationController::class, 'index'])->name('index');
            Route::get('{fax_sid}', [FaxIdentificationController::class, 'show'])->name('show');
            Route::put('{fax_sid}/verify', [FaxIdentificationController::class, 'verify'])->name('verify');
        });

        Route::post('users/create-cognito', [UserController::class, 'createCognitoUser'])->name('users.create-cognito');

        Route::get('', [DistributorController::class, 'show'])->name('show');

        Route::get('fax-logs', [DistributorController::class, 'getFaxLogsForDistributor'])->name('get-fax-logs');
        Route::get('fax-status-details/{faxSid}', [DistributorController::class, 'getFaxLogStatusDetails'])->name('get-fax-status-details');
        Route::get('fax-log-file/{faxId}', [DistributorController::class, 'getFaxLogDetail'])->name('get-fax-log-file');
        Route::get('sms-logs', [SignalWireSmsLogsController::class, 'index'])->name('sms-logs.index');

        Route::put('', [DistributorController::class, 'update'])->name('update');
        Route::resource('addresses', AddressController::class, ['name' => 'addresses']);
        Route::resource('image-gallery', ImageGalleryController::class)->only(['index', 'store', 'destroy']);
        Route::resource('reports', ReportController::class);
        Route::post('reports/orders/download', [ReportController::class, 'ordersDownload'])->name('reports.orders.download');
        Route::post('leads/report', [ReportController::class, 'leadsReport'])->name('reports.leads.download'); // TODO: rename to reports/leads/download
        Route::post('orders/report', [ReportController::class, 'ordersReport'])->name('reports.orders.download-dump'); // TODO: rename to reports/leads/download

        Route::post('payers/custom', [PayerController::class, 'storeCustom'])->name('payers.store-custom');

        // Timezones
        Route::get('timezones', [TimeZonesController::class, 'index'])->name('timezone.list');

        /** Diagnosis Codes */
        Route::get('diagnosis-codes', [DiagnosisCodesController::class, 'list'])->name('diagnosis-codes.list');
        /** Orders */
        Route::resource('orders', OrderController::class)->except(['destroy']);
        Route::get('orders/archived', [OrderController::class, 'archivedOrdersList'])->name('orders.archived');
        Route::prefix('orders/{order}')->name('orders.')->group(function () {
            Route::put('assign-user', [OrderController::class, 'assignUser'])->name('assign-user');
            Route::put('unassign-user', [OrderController::class, 'unassignUser'])->name('unassign-user');
            Route::put('cancel', [OrderController::class, 'cancel'])->name('cancel');
            Route::get('reroute-details', [OrderController::class, 'rerouteDetails'])->name('reroute-details');
            Route::get('documentation', [OrderController::class, 'documentation'])->name('documentation');
            Route::put('assign-provider', [OrderController::class, 'assignProvider'])->name('assign-provider');
            Route::put('assign-provider-user', [OrderController::class, 'assignProviderUser'])->name('assign-provider-user');
            Route::put('request-facility-update', [OrderController::class, 'requestFacilityUpdate'])->name('request-facility-update');
            Route::get('available-distributors', [OrderController::class, 'getAvailableDistributors'])->name('available-distributors.index');
            Route::get('available-external-manufacturer-users', [OrderController::class, 'getAvailableExternalManufacturerUsers'])->name('available-external-manufacturer-users.index');
            Route::put('assign-external-manufacturer-user', [OrderController::class, 'assignExternalManufacturerUser'])->name('assign-external-manufacturer-user');
            Route::put('unassign-external-manufacturer-user', [OrderController::class, 'unassignExternalManufacturerUser'])->name('unassign-external-manufacturer-user');
            /** Add diagnosis codes to order */
            Route::post('diagnosis-codes', [OrderController::class, 'addDiagnosisCodesToOrder'])->name('diagnosis-codes.attach');
            Route::resource('logs', OrderActivityController::class)->except(['edit']);
            Route::get('logs/{logId}/history', [OrderActivityController::class, 'history'])->name('logs.history');
            /** Order Notes */
            Route::prefix('notes')->group(function () {
                Route::prefix('{orderNoteId}')->group(function () {
                    Route::post('reply', [OrderNotesController::class, 'replyToNote'])->name('notes.reply');
                });
            });
            Route::resource('products', OrderProductController::class);
            Route::resource('tasks', OrderTaskController::class);
            /** Document Requests */
            Route::post('document-requests', [OrderDocumentRequestController::class, 'store'])->name('document-requests.store');
            Route::put('document-requests/{documentRequest}/cancel', [OrderDocumentRequestController::class, 'cancel'])->name('document-requests.cancel');
            Route::post('document-requests/aob', [OrderDocumentRequestController::class, 'requestAobSignature'])->name('document-requests.aob.request');
            Route::get('document-requests/aob/preview', [OrderDocumentRequestController::class, 'aobPreview'])->name('document-requests.aob.preview');
            Route::get('document-requests/cmn/preview', [OrderDocumentRequestController::class, 'cmnPrescriptionRequestPreview'])->name('document-requests.cmn.preview');
            /** Shipping */
            Route::post('shipping', [OrderShippingController::class, 'store'])->name('shipping.store');
            Route::put('shipping', [OrderShippingController::class, 'update'])->name('shipping.update');
            /** Manufacturers */
            Route::get('patient-manufacturers', [OrderPatientController::class, 'getManufacturers'])->name('patient-manufacturers.index');
            Route::put('patient-manufacturers/{manufacturer}', [OrderPatientController::class, 'updateManufacturerId'])->name('patient-manufacturers.update-manufacturer-id');

            Route::post('same-and-similar-inquiry/{product}', [OrderSameAndSimilarController::class, 'inquiry'])->name('same-and-similar-inquiry');
            Route::get('same-and-similar-history/{product}', [SameAndSimilarHistoryController::class, 'index'])->name('same-and-similar-history');
            /** Send shipment update fax */
            Route::post('shipment-fax/send', [OrderController::class, 'sendShipmentFax'])->name('send-shipment-fax');
            Route::get('shipment-fax/preview', [OrderController::class, 'previewShipmentFax'])->name('preview-shipment-fax');

            /** Archive Order */
            Route::prefix('archive')->name('archive.')->group(function () {
                Route::patch('', [OrderController::class, 'archive'])->name('archive-order');
            });
            /** Order Notes */
        });
        Route::get('orders/counts', [OrderController::class, 'counts'])->name('orders.counts');

        /** Documents */
        Route::prefix('orders/{orderId}/documents')->name('orders.documents.')->group(function () {
            Route::get('/', [OrderDocumentController::class, 'index'])->name('index');
            Route::get('{documentId}', [OrderDocumentController::class, 'show'])->name('show');
            Route::delete('{documentId}', [OrderDocumentController::class, 'delete'])->name('delete');
            Route::post('upload', [OrderDocumentController::class, 'upload'])->name('upload');
            Route::post('from-fax', [OrderDocumentController::class, 'createFromFax'])->name('store.from-fax');
            Route::delete('{documentId}', [OrderDocumentController::class, 'delete'])->name('delete');
            Route::delete('{documentId}/files/{fileId}', [OrderDocumentController::class, 'deleteDocumentFile'])->name('files.delete');
            Route::post('attach/{documentId}', [OrderDocumentController::class, 'attachDocumentToOrder'])->name('attach');
            Route::post('detach/{documentId}', [OrderDocumentController::class, 'detachDocumentFromOrder'])->name('detach');
        });

        /** Order Source */
        Route::prefix('order-source')->name('order-source.')->group(function () {
            Route::get('list', [OrderSourceController::class, 'index'])->name('index');
            Route::post('', [OrderSourceController::class, 'save'])->name('save');
            Route::put('activate', [OrderSourceController::class, 'activate'])->name('activate');
            Route::put('{orderSource}', [OrderSourceController::class, 'update'])->name('update');
            Route::delete('{orderSource}', [OrderSourceController::class, 'delete'])->name('delete');
            Route::get('{orderSource}/history', [OrderSourceController::class, 'history'])->name('history');
        });

        /** Medical Policy Forms */
        Route::get('orders/{orderId}/medical-policies', [OrderMedicalPolicyController::class, 'index'])
            ->name('orders.medical-policies.index');
        Route::get('orders/{orderId}/medical-policies/{medicalPolicyResponseId}', [OrderMedicalPolicyController::class, 'show'])
            ->name('orders.medical-policies.show');

        Route::prefix('renewal-requests')->name('renewals.')->group(function () {
            Route::prefix('imports')->group(function () {
                Route::get('', [ImportRenewalRequestController::class, 'index'])->name('index');
                Route::post('{requestType}/validate', [ImportRenewalRequestController::class, 'validateFile'])->name('validate-file');
                Route::post('{fileImport}', [ImportRenewalRequestController::class, 'store'])->name('store');
                Route::get('{fileImport}/status', [ImportRenewalRequestController::class, 'status'])->name('status');
                Route::post('{fileImport}/cancel', [ImportRenewalRequestController::class, 'cancel'])->name('cancel');
                Route::get('{fileImport}/download-errors', [ImportRenewalRequestController::class, 'downloadErrors'])->name('download-errors');
                Route::delete('{fileImport}', [ImportRenewalRequestController::class, 'destroy'])->name('destroy');
            });
        });

        /** Patients */
        Route::apiResource('patients', PatientController::class)->only('index', 'show', 'store', 'update');
        Route::get('patients/duplicates', [PatientController::class, 'findDuplicates'])->name('patients.duplicates');
        Route::prefix('patients/{patient}/')->name('patients.')->group(function () {
            Route::patch('update-status', [PatientController::class, 'updateStatus'])->name('update-status');
            Route::get('activity-list', [PatientController::class, 'activityList'])->name('activity.index');
            Route::patch('cancel-activity', [PatientController::class, 'cancelActivity'])->name('cancel-activity');
            Route::apiResource('addresses', PatientAddressController::class);
            Route::apiResource('payers', PatientPayerController::class);
            Route::put('payers', [PatientPayerController::class, 'updatePayers'])->name('payers.update-payers');
            Route::post('payers/{payerId}/check-eligibility', [PatientPayerController::class, 'checkEligibility'])->name('payers.check-eligibility');
            Route::get('/payers/{payerId}/eligibility-checks/{eligibilityCheckId}/logs', [PatientPayerController::class, 'getEligibilityCheckLogs'])->name('payers.eligibility-checks.logs');
            Route::get('payers/{payerId}/get-eligibility-by-id/{eligibilityCheckId}', [PatientPayerController::class, 'getEligibilityById'])->name('payers.get-eligibility-by-id');
            Route::patch('payers/{payerId}/archive', [PatientPayerController::class, 'archive'])->name('payers.archive');
            Route::patch('payers/{payer}/update-manual-check', [PatientPayerController::class, 'updateManualCheck'])->name('payers.update-manual-check');
            Route::patch('communication-preferences', [CommunicationPreferenceController::class, 'updatePatientPreferences'])->name('communication-preferences.update');
            Route::get('communication-logs', [PatientActivityLogController::class, 'communicationLogs'])->name('communication-logs');
            Route::get('logs', [PatientActivityLogController::class, 'patientLogs'])->name('patient-logs');
            Route::get('diagnosis-codes', [PatientController::class, 'getPatientDiagnosisCodes'])->name('diagnosis-codes.list');
            // physicians
            Route::get('provider_users', [PatientProviderUserController::class, 'index'])->name('provider-users.index');
            Route::put('provider_users/{providerUserAssociationType}', [PatientProviderUserController::class, 'update'])->name('provider-users.update');
            Route::delete('provider_users/{providerUserAssociationType}', [PatientProviderUserController::class, 'destroy'])->name('provider-users.destroy');
            // documents
            Route::get('documents', [PatientDocumentController::class, 'index'])->name('documents.index');
            Route::get('documents/{documentId}', [PatientDocumentController::class, 'show'])->name('documents.show');
            Route::post('/documents/upload', [PatientDocumentController::class, 'upload'])->name('documents.upload');
            Route::delete('documents/{documentId}', [PatientDocumentController::class, 'delete'])->name('documents.delete');
            Route::patch('documents/{documentId}/update', [PatientDocumentController::class, 'update'])->name('documents.update');

            // 📞 Communication Window Check
            Route::get('check-communication-window', [PatientController::class, 'checkCommunicationWindow'])->name('check-communication-window');

            // 📋 Insurance Card Requests
            Route::prefix('insurance-card-requests')->name('insurance-card-requests.')->group(function () {
                Route::get('/', [PatientInsuranceCardRequestController::class, 'index'])->name('index');
                Route::post('/', [PatientInsuranceCardRequestController::class, 'store'])->name('store');
                Route::post('upload-without-request', [PatientInsuranceCardRequestController::class, 'uploadWithoutRequest'])->name('upload-without-request');
                Route::get('{insuranceCardRequest}', [PatientInsuranceCardRequestController::class, 'show'])->name('show');
                Route::delete('{insuranceCardRequest}', [PatientInsuranceCardRequestController::class, 'destroy'])->name('destroy');
                Route::post('{insuranceCardRequest}/upload', [PatientInsuranceCardRequestController::class, 'upload'])->name('upload');
                Route::post('{insuranceCardRequest}/resend', [PatientInsuranceCardRequestController::class, 'resend'])->name('resend');
            });
            // 📦 Pharmacy Payer for Patient
            Route::prefix('pharmacy-payer')->name('pharmacy-payer.')->group(function () {
                Route::post('/', [PharmacyPayerController::class, 'storeForPatient'])->name('store');
                Route::get('/', [PharmacyPayerController::class, 'showForPatient'])->name('show');
                Route::put('{payer}', [PharmacyPayerController::class, 'updateForPatient'])->name('update');
                Route::delete('{payer}', [PharmacyPayerController::class, 'destroyForPatient'])->name('destroy');
            });


        });

        /** Campaigns */
        Route::resource('campaigns', CampaignController::class)->except(['edit', 'destroy']);
        // Grouped Lead routes
        Route::prefix('leads')->name('leads.')->group(function () {
            Route::get('/', [DistributorLeadController::class, 'index'])->name('index');
            Route::get('/{lead}', [DistributorLeadController::class, 'show'])->name('show');
            Route::delete('/{lead}', [DistributorLeadController::class, 'destroy'])->middleware('distributor-admin')->name('delete');
            Route::post('/', [DistributorLeadController::class, 'store'])->name('store');
            Route::get('/counts', [DistributorLeadController::class, 'counts'])->name('counts');
            Route::get('/import/{type}/download-template', [LeadController::class, 'downloadTemplate'])->name('download.template');
        });
        Route::prefix('on-demand-message-templates')->name('on-demand-message-templates.')->group(function () {
            Route::get('/placeholders', [OnDemandMessageTemplateController::class, 'onDemandMessageTemplateplaceholders'])->name('message-template-placeholders');

            Route::get('lookup', [OnDemandMessageTemplateController::class, 'lookupList'])->name('lookup-list');
            Route::get('/', [OnDemandMessageTemplateController::class, 'index'])->name('index');
            Route::post('/', [OnDemandMessageTemplateController::class, 'store'])->name('store'); // Add POST route for creating a template
            Route::put('/{template}', [OnDemandMessageTemplateController::class, 'update'])->name('update'); // Add PUT route for updating a template
            Route::get('/{template}', [OnDemandMessageTemplateController::class, 'show'])->name('show'); // Add GET route for showing a specific template
            Route::delete('/{template}', [OnDemandMessageTemplateController::class, 'destroy'])->name('destroy');
        });
        Route::prefix('campaigns/{campaign}')->name('campaigns.')->group(function () {
            Route::resource('message-templates', MessageTemplateController::class)->except(['destroy', 'edit']);

            Route::resource('email-templates', EmailTemplateController::class)->except(['destroy', 'edit']);

            // Custom Fields
            Route::resource('custom-fields', CustomFieldController::class)->only(['index', 'store', 'destroy']);

            Route::resource('digital-journeys', DigitalJourneyController::class)->except(['edit']);
            Route::resource('leads', LeadController::class)->except(['destroy']);
            Route::prefix('leads')->name('leads.')->group(function () {
                Route::get('counts', [LeadController::class, 'counts'])->name('counts');
                Route::post('import', [LeadController::class, 'import'])->name('import');
            });
            Route::prefix('leads/{lead}')->name('leads.')->group(function () {
                Route::resource('addresses', LeadAddressController::class);
                Route::put('assign-provider-npi-user', [LeadController::class, 'assignProviderUserFromNpi'])->name('assign-provider-npi-user');
                Route::post('convert-to-order', [LeadController::class, 'convertToOrder'])->name('convert-to-order');
                Route::resource('logs', LeadActivityController::class)->except(['edit']);
                Route::get('logs/{logId}/history', [LeadActivityController::class, 'history'])->name('logs.history');
                Route::post('notify', [LeadController::class, 'notify'])->name('notify');
                Route::get('check-communication-window', [LeadController::class, 'checkCommunicationWindow'])->name('check-communication-window');
                Route::resource('payers', LeadPayerController::class);
                Route::put('payers', [LeadPayerController::class, 'updatePayers'])->name('payers.update-payers');
                Route::post('payers/{payerId}/check-eligibility', [LeadPayerController::class, 'checkEligibility'])->name('payers.check-eligibility');
                Route::get('payers/{payerId}/get-eligibility-by-id/{eligibilityCheckId}', [LeadPayerController::class, 'getEligibilityById'])->name('payers.get-eligibility-by-id');
                Route::patch('payers/{payer}/update-manual-check', [LeadPayerController::class, 'updateManualCheck'])->name('payers.update-manual-check');
                Route::get('/payers/{payerId}/eligibility-checks/{eligibilityCheckId}/logs', [LeadPayerController::class, 'getEligibilityCheckLogs'])->name('payers.eligibility-checks.logs');
                Route::resource('products', LeadProductController::class);
                Route::post('same-and-similar-inquiry/{product}', [SameAndSimilarChecksController::class, 'inquiry'])->name('same-and-similar-inquiry');
                Route::get('same-and-similar-history/{product}', [LeadSameAndSimilarHistoryController::class, 'index'])->name('same-and-similar-history');
                // 📦 Pharmacy Payer for Lead
                Route::prefix('pharmacy-payer')->name('pharmacy-payer.')->group(function () {
                    Route::post('/', [PharmacyPayerController::class, 'storeForLead'])->name('store');
                    Route::get('/', [PharmacyPayerController::class, 'showForLead'])->name('show');
                    Route::put('{payer}', [PharmacyPayerController::class, 'updateForLead'])->name('update');
                    Route::delete('{payer}', [PharmacyPayerController::class, 'destroyForLead'])->name('destroy');
                });

                Route::prefix('insurance-card-requests')->name('insurance-card-requests.')->group(function () {
                    Route::get('/', [LeadInsuranceCardRequestController::class, 'index'])->name('index');
                    Route::post('/', [LeadInsuranceCardRequestController::class, 'store'])->name('store');
                    Route::post('upload-without-request', [LeadInsuranceCardRequestController::class, 'uploadWithoutRequest'])->name('upload-without-request');
                    Route::get('{insuranceCardRequest}', [LeadInsuranceCardRequestController::class, 'show'])->name('show');
                    Route::delete('{insuranceCardRequest}', [LeadInsuranceCardRequestController::class, 'destroy'])->name('destroy');
                    Route::post('{insuranceCardRequest}/upload', [LeadInsuranceCardRequestController::class, 'upload'])->name('upload');
                    Route::post('{insuranceCardRequest}/resend', [LeadInsuranceCardRequestController::class, 'resend'])->name('resend');
                });
            });
        });

        /** Products */
        Route::get('product-categories', [ProductCategoryController::class, 'index'])->name('product-categories.index');
        Route::get('product-categories/{productCategory}', [ProductCategoryController::class, 'show'])->name('product-categories.show');
        Route::resource('products', ProductController::class);

        /** Users */
        Route::resource('users', UserController::class)->withTrashed();
        Route::prefix('users/{user}')->name('users.')->group(function () {
            Route::put('activate', [UserController::class, 'activate'])->name('activate');
            Route::post('resend-invitation', [UserController::class, 'resendInvitation'])->name('resend-invitation');
            Route::post('reset-password', [ResetPasswordController::class, 'distributorResetPassword'])->name('password.reset');

            /** Permissions */
            Route::get('permissions', [UserController::class, 'getUserPermissions'])->name('permissions.index');
            Route::post('permissions', [UserController::class, 'updateUserPermissions'])->name('permissions.update');
        });

        /** Distributor Dashboard */
        Route::prefix('dashboard')->name('dashboard.')->group(function () {
            Route::get('manufacturers', [DistributorDashboardController::class, 'getManufacturers'])->name('get-manufacturers');
            Route::get('manufacturers/{manufacturer}', [DistributorDashboardController::class, 'getManufacturer'])->name('get-manufacturer');
            Route::get('days-to-ship', [DistributorDashboardController::class, 'getAverageDaysToShip'])->name('get-days-to-ship');
            Route::get('new-patients', [DistributorDashboardController::class, 'getNewPatients'])->name('get-new-patients');
            Route::get('payer-type-mix', [DistributorDashboardController::class, 'getPayerTypeMix'])->name('get-payer-type-mix');
            Route::get('cancellations', [DistributorDashboardController::class, 'getCancellations'])->name('get-cancellations');
            Route::get('open-pipelines', [DistributorDashboardController::class, 'getOpenPipelines'])->name('get-open-pipelines');
            Route::get('win-rate', [DistributorDashboardController::class, 'getWinRate'])->name('get-win-rate');
            Route::get('new-opportunities', [DistributorDashboardController::class, 'getNewOpportunities'])->name('get-new-opportunities');
            Route::get('top-payers', [DistributorDashboardController::class, 'getTopPayers'])->name('get-top-payers');
            Route::get('trend-by-brand', [DistributorDashboardController::class, 'getTrendByBrand'])->name('get-trend-by-brand');
            Route::get('orders-by-state', [DistributorDashboardController::class, 'getOrdersByState'])->name('get-orders-by-state');
        });

        /** Territory Management Tools */
        Route::resource('regions', RegionController::class);
        Route::prefix('regions/{region}')->name('regions.')->group(function () {
            Route::put('manager', [RegionController::class, 'updateManager'])->name('update-manager');
        });

        Route::resource('districts', DistrictController::class);
        Route::prefix('districts/{district}')->name('districts.')->group(function () {
            Route::put('manager', [DistrictController::class, 'updateManager'])->name('update-manager');
        });

        Route::resource('territories', TerritoryController::class);
        Route::prefix('territories/{territory}')->name('territories.')->group(function () {
            Route::put('manager', [TerritoryController::class, 'updateManager'])->name('update-manager');
            Route::put('zip-codes', [TerritoryController::class, 'updateZipCodes'])->name('update-zip-codes');
        });

        /** Sales Dashboard */
        Route::prefix('sales-dashboard')->name('sales-dashboard.')->group(function () {
            Route::get('days-to-ship', [SalesDashboardController::class, 'getAverageDaysToShip'])->name('get-days-to-ship');
            Route::get('new-patients', [SalesDashboardController::class, 'getNewPatients'])->name('get-new-patients');
            Route::get('payer-type-mix', [SalesDashboardController::class, 'getPayerTypeMix'])->name('get-payer-type-mix');
            Route::get('cancellations', [SalesDashboardController::class, 'getCancellations'])->name('get-cancellations');
            Route::get('open-pipelines', [SalesDashboardController::class, 'getOpenPipelines'])->name('get-open-pipelines');
            Route::get('provider-users', [SalesDashboardController::class, 'getProviderUsers'])->name('get-provider-users');
            Route::get('referral-trends', [SalesDashboardController::class, 'getReferralTrends'])->name('get-referral-trends');
            Route::get('top-payers', [SalesDashboardController::class, 'getTopPayers'])->name('get-top-payers');
            Route::get('trend-by-brand', [SalesDashboardController::class, 'getTrendByBrand'])->name('get-trend-by-brand');
            Route::get('document-collection', [SalesDashboardController::class, 'getDocumentCollection'])
                ->name('get-document-collection');
            Route::get('document-collection-detail', [SalesDashboardController::class, 'getDocumentCollectionDetail'])
                ->name('get-document-collection-detail');
        });

        /** Provider-users */
        Route::resource('provider-users', ProviderUserController::class)->only(['index', 'show', 'store']);
        Route::prefix('provider-users/{providerUser}')->group(function () {
            Route::put('update-pecos-status', [PecosController::class, 'updateProviderUserPecosStatus'])->name('update-pecos-status');
        });
        /** Providers */
        Route::get('providers/search', [ProviderController::class, 'search']);
        Route::resource('providers', ProviderController::class)->only(['index', 'show', 'store', 'update']);
        Route::prefix('providers/{provider}')->name('providers.')->group(function () {
            Route::patch('patch', [ProviderController::class, 'patch'])->name('patch');
            Route::delete('detach', [ProviderController::class, 'detach'])->name('detach');
            Route::prefix('provider-users/{providerUser}')->name('provider-users.')->group(function () {
                Route::post('resend-invitation', [ProviderController::class, 'resendInvitation'])->name('resend-invitation');
                Route::post('reset-password', [ResetPasswordController::class, 'distributorProviderUserResetPassword'])->name('password.reset');
            });
        });

        /** Facilities */
        Route::post('facilities/update-or-store', [FacilityController::class, 'updateOrStore']);
        Route::apiResource('facilities', FacilityController::class);

        /** Document requests */
        Route::prefix('document-requests')->name('document-requests.')->group(function () {
            Route::get('', [DocumentRequestController::class, 'index'])->name('index');
            Route::get('{documentRequestId}', [DocumentRequestController::class, 'show'])->name('show');
            Route::get('{documentRequestId}/similar-requests', [DocumentRequestController::class, 'showSimilarRequests'])->name('show-similar-requests');
            Route::put('{documentRequestId}', [DocumentRequestController::class, 'update'])->name('update');
            Route::get('counts', [DocumentRequestController::class, 'counts'])->name('counts');
            Route::post('{documentRequestId}/request-again', [DocumentRequestController::class, 'requestAgain'])->name('request-again');
            Route::post('', [DocumentRequestController::class, 'store'])->name('store');
            Route::post('preview', [DocumentRequestController::class, 'preview'])->name('preview');
            Route::put('{documentRequestId}/assign-user', [DocumentRequestController::class, 'assignUser'])->name('assign-user');
            Route::put('{documentRequestId}/unassign-user', [DocumentRequestController::class, 'unassignUser'])->name('unassign-user');
            Route::put('{documentRequestId}/cancel', [DocumentRequestController::class, 'cancel'])->name('cancel');
            Route::resource('{documentRequest}/products', DocumentRequestProductController::class);
            Route::resource('{documentRequest}/cms-hcpcs', CmsHcpcController::class)->only(['destroy', 'update']);

            /** Documents */
            Route::prefix('{documentRequestId}/documents')->name('documents.')->group(function () {
                Route::get('/', [DocumentRequestDocumentController::class, 'index'])->name('index');
                Route::get('{documentId}', [DocumentRequestDocumentController::class, 'show'])->name('show');
                Route::delete('{documentId}', [DocumentRequestDocumentController::class, 'delete'])->name('delete');
                Route::post('upload', [DocumentRequestDocumentController::class, 'upload'])->name('upload');
                Route::post('from-fax', [DocumentRequestDocumentController::class, 'createFromFax'])->name('store.from-fax');
                Route::delete('{documentId}', [DocumentRequestDocumentController::class, 'delete'])->name('delete');
                Route::delete('{documentId}/files/{fileId}', [DocumentRequestDocumentController::class, 'deleteDocumentFile'])->name('files.delete');
            });
        });

        /** Manufacturers */
        Route::get('/manufacturers', [ManufacturerController::class, 'index'])->name('manufacturers.index');
        Route::get('/manufacturers/list', [ManufacturerController::class, 'listFromOrderProducts'])->name('manufacturers.list');

        /** Brightree */
        Route::prefix('brightree')->name('brightree.')->group(function () {
            Route::get('get-patient-by-bt-patient-id/{id}', [BrightreeController::class, 'getPatientByBtPatientId'])->name('get-patient-by-bt-patient-id');
            Route::get('get-patient-by-bt-params', [BrightreeController::class, 'getPatientByBtParams'])->name('get-patient-by-bt-params');
            Route::get('get-order-by-id/{id}', [BrightreeController::class, 'getOrderById'])->name('get-order-by-id');
            Route::get('get-note-by-id/{id}', [BrightreeController::class, 'getNoteById'])->name('get-note-by-id');
            Route::patch('sync-patient-by-id/{id}', [BrightreeController::class, 'syncPatientById'])->name('sync-patient-by-id');
            Route::patch('sync-patient-by-order-id/{id}', [BrightreeController::class, 'syncPatientByOrderId'])->name('sync-patient-by-order-id');
            Route::post('create-patient-by-bt-patient-id/{id}', [BrightreeController::class, 'createPatientByBtPatientId'])->name('create-patient-by-bt-patient-id');
            Route::post('create-patient-by-bt-patient-key/{id}', [BrightreeController::class, 'createPatientByBtPatientKey'])->name('create-patient-by-bt-patient-key');
            Route::post('create-bt-patient-by-patient-id/{id}', [BrightreeController::class, 'createBtPatientByPatientId'])->name('create-bt-patient-by-patient-id');
            Route::patch('update-note/{id}/{order}', [BrightreeController::class, 'updateNote'])->name('update-note');
        });

        /** Faxes */
        Route::get('faxes/counts', [FaxController::class, 'counts'])->name('faxes.counts');
        Route::resource('faxes', FaxController::class)->only(['index', 'show', 'update']);
        Route::prefix('faxes/{fax}')->name('faxes.')->group(function () {
            Route::post('restore', [FaxController::class, 'restore'])->name('restore');
            Route::post('remove', [FaxController::class, 'remove'])->name('remove');
        });

        /** NPI Registry */
        Route::get('npi-registry/search-physician', [NpiController::class, 'searchPhysician'])->name('npi-registry.search-physician');

        /** Organization settings */
        Route::get('organization-settings/all', [OrganizationSettingsController::class, 'all'])->name('organization-settings.all');
        Route::patch('organization-settings/bulk-update', [OrganizationSettingsController::class, 'bulkUpdate'])->name('organization-settings.bulk-update');
        Route::apiResource('organization-settings', OrganizationSettingsController::class)->except(['store', 'edit', 'destroy']);

        Route::prefix('global-search')->name('global-search.')->group(function () {
            Route::get('lead-patient-list', [GlobalSearchController::class, 'leadPatientList'])->name('lead-patient-list');
            Route::get('lead-order-customer-list', [GlobalSearchController::class, 'leadPatientList'])->name('lead-order-customer-list');
        });
        Route::post('send-on-demand-message', [OnDemandMessageController::class, 'notify'])->name('ondemand-message-send');
        Route::apiResource('email-templates', DistributorEmailTemplateController::class);
        Route::get('languages', [DistributorLanguageController::class, 'getLanguages'])->name('languages-index');

        Route::prefix('message-configuration')->name('message-configuration.')->group(function () {
            Route::get('placeholders', [MessageConfigurationController::class, 'placeholders'])->name('placeholders');
            Route::get('lookup', [MessageConfigurationController::class, 'lookupList'])->name('lookupList');
            Route::get('get-all-categories', [MessageConfigurationController::class, 'getAllCategories'])->name('getAllCategories');
            Route::get('templates', [MessageConfigurationController::class, 'index'])->name('templates.index');
            Route::post('templates', [MessageConfigurationController::class, 'store'])->name('templates.store');
            Route::put('templates', [MessageConfigurationController::class, 'update'])->name('templates.update');
            Route::delete('templates/{id}', [MessageConfigurationController::class, 'destroy'])->name('templates.destroy');
        });

        Route::prefix('filter-sets')->name('filter-sets.')->group(function () {
            Route::post('create', [FilterSetController::class, 'store'])->name('store');
            Route::get('list/{view}', [FilterSetController::class, 'index'])->name('list');
            Route::put('update/{filterSet}', [FilterSetController::class, 'update'])->name('update');
            Route::delete('delete/{filterSet}', [FilterSetController::class, 'destroy'])->name('delete');
        });

        /** QuickSight Embedded Analytics */
        Route::middleware(['throttle.quicksight'])->group(function () {
            Route::post('analytics/embed-url', [QuicksightController::class, 'getEmbedUrl'])->name('quicksight.embed-url');
            Route::post('analytics/users', [QuicksightController::class, 'createUser'])->name('quicksight.create-user');
        });
    });
});
