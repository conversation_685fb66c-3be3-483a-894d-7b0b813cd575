<?php

use App\Http\Controllers\External\AuthController;
use Illuminate\Support\Facades\Route;

/* for all external API */
Route::prefix('auth')->name('auth.')->group(function () {
    Route::post('login', [AuthController::class, 'token'])
        ->middleware('throttle:login')
        ->withoutMiddleware('throttle:ext-api')
        ->name('login'); // NOTE: deprecated

    Route::post('logout', [AuthController::class, 'revoke'])->name('logout'); // NOTE: deprecated
    Route::post('revoke', [AuthController::class, 'revoke'])->name('revoke');
    Route::post('token', [AuthController::class, 'token'])
        ->middleware('throttle:login')
        ->withoutMiddleware('throttle:ext-api')
        ->name('token');
});
