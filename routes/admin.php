<?php

use App\Http\Controllers\Admin\CampaignController;
use App\Http\Controllers\Admin\Distributor\OrganizationSettingsController;
use App\Http\Controllers\Admin\Distributor\PayerController as DistributorPayerController;
use App\Http\Controllers\Admin\Distributor\UserController as DistributorUserController;
use App\Http\Controllers\Admin\DistributorController;
use App\Http\Controllers\Admin\Export\PayersController;
use App\Http\Controllers\Admin\FilterSetController;
use App\Http\Controllers\Admin\GlobalSettingsController;
use App\Http\Controllers\Admin\Manufacturer\UserController as ManufacturerUserController;
use App\Http\Controllers\Admin\ManufacturerController;
use App\Http\Controllers\Admin\MedicalPolicyFormController;
use App\Http\Controllers\Admin\OauthClientController;
use App\Http\Controllers\Admin\ProductCategoryController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductGroupController;
use App\Http\Controllers\Admin\Provider\UserController as ProviderUserController;
use App\Http\Controllers\Admin\ProviderController;
use App\Http\Controllers\Admin\QA\CronController;
use App\Http\Controllers\Admin\QA\CustomUploadsController;
use App\Http\Controllers\Admin\QA\OcrController;
use App\Http\Controllers\Admin\QA\TemplateController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\API\QuicksightController;
use App\Http\Controllers\CMSArticleController;
use App\Http\Controllers\CMSHCPCController;
use App\Http\Controllers\CMSLCDController;
use App\Http\Controllers\Distributor\AddressController as DistributorAddressController;
use App\Http\Controllers\PayerController;
use App\Http\Controllers\User\ResetPasswordController;
use App\Http\Controllers\WHOICD10Controller;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => [
    'cognito',
    'cognito-scope:int-sha-scope',
]], function () {
    /** SH ADMINISTRATION */
    Route::prefix('admin')->name('admin.')->group(function () {
        /** Campaigns */
        Route::resource('campaigns', CampaignController::class);

        /** CMS Data */
        Route::prefix('cms')->name('cms.')->group(function () {
            Route::resource('hcpcs', CMSHCPCController::class);
            Route::resource('articles', CMSArticleController::class);
            Route::resource('lcds', CMSLCDController::class);
        });

        /** Distributors */
        Route::resource('distributors', DistributorController::class);
        Route::prefix('distributors/{distributor}')->name('distributors.')->group(function () {
            Route::post('image', [DistributorController::class, 'uploadImage'])->name('upload-image');
            Route::resource('addresses', DistributorAddressController::class);
            Route::prefix('payers')->name('payers.')->group(function () {
                Route::get('', [DistributorPayerController::class, 'index'])->name('index');
                Route::get('{payer}', [DistributorPayerController::class, 'show'])->name('show');
                Route::post('', [DistributorPayerController::class, 'store'])->name('store');
                Route::put('{payerId}', [DistributorPayerController::class, 'update'])->name('update');
                Route::delete('{payerId}', [DistributorPayerController::class, 'destroy'])->name('destroy');
                Route::post('import', [DistributorPayerController::class, 'import'])->name('import');
            });

            Route::resource('users', DistributorUserController::class)->only(['index']);
            Route::prefix('users/{user}')->name('users.')->group(function () {
                Route::post('change-owner', [DistributorUserController::class, 'changeOwner'])->name('change-owner');
                Route::post('resend-invitation', [DistributorUserController::class, 'resendInvitation'])->name('resend-invitation');
                Route::post('reset-password', [ResetPasswordController::class, 'distributorResetPassword'])->name('password.reset');
            });
            Route::apiResource('organization-settings', OrganizationSettingsController::class)->except(['store', 'edit', 'destroy']);
        });
        Route::get('distributors/payers/template', [DistributorPayerController::class, 'downloadTemplate'])->name('distributors.payers.template');

        /** Manufacturers */
        Route::resource('manufacturers', ManufacturerController::class);
        Route::prefix('manufacturers/{manufacturer}')->name('manufacturers.')->group(function () {
            Route::post('image', [ManufacturerController::class, 'uploadImage'])->name('upload-image');

            Route::resource('users', ManufacturerUserController::class)->only(['index']);
            Route::prefix('users/{user}')->name('users.')->group(function () {
                Route::post('resend-invitation', [ManufacturerUserController::class, 'resendInvitation'])->name('resend-invitation');
                Route::post('change-owner', [ManufacturerUserController::class, 'changeOwner'])->name('change-owner');
                Route::post('reset-password', [ResetPasswordController::class, 'manufacturerResetPassword'])->name('password.reset');
            });
        });

        /** Medical Policy Forms */
        Route::resource('medical-policy-forms', MedicalPolicyFormController::class)->parameter('medical-policy-forms', 'medicalPolicyForm');
        Route::get('medical-policy-forms/list-for-product', [MedicalPolicyFormController::class, 'getMedicalPolicyFormsForProduct'])
            ->name('medical-policy-forms.list-for-product');

        /** Oauth clients */
        Route::resource('oauth-clients', OauthClientController::class);

        /** Payers */
        Route::resource('payers', PayerController::class);
        Route::get('custom-payers', [PayerController::class, 'customPayersIndex'])->name('custom-payers');
        Route::prefix('payers/{payer}')->name('payers.')->group(function () {
            Route::post('approve', [PayerController::class, 'approveCustomPayer'])->name('approve');
            Route::post('alias', [PayerController::class, 'aliasCustomPayer'])->name('alias');
            Route::post('link', [PayerController::class, 'linkCustomPayer'])->name('link');
            Route::post('decline', [PayerController::class, 'declineCustomPayer'])->name('decline');
        });

        Route::prefix('export')->name('export.')->group(function () {
            Route::get('payers', [PayersController::class, 'getPayersCsv'])->name('payers-csv');
            Route::get('payer-services', [PayersController::class, 'getPayerServicesCsv'])->name('payer-services-csv');
        });

        /** Product Categories */
        Route::resource('product-categories', ProductCategoryController::class)
            ->parameter('product-categories', 'productCategory');
        Route::post('product-categories/{productCategory}/image', [ProductCategoryController::class, 'uploadImage'])
            ->name('product-categories.upload-image');

        /** Product Groups */
        Route::resource('product-groups', ProductGroupController::class)
            ->parameter('product-groups', 'productGroup');
        Route::post('product-groups/{productGroup}/image', [ProductGroupController::class, 'uploadImage'])
            ->name('product-groups.upload-image');

        /** Products */
        Route::resource('products', ProductController::class);
        Route::post('products/{product}/image', [ProductController::class, 'uploadImage'])
            ->name('products.upload-image');

        /** Providers */
        Route::resource('providers', ProviderController::class);
        Route::prefix('providers/{provider}')->name('providers.')->group(function () {
            Route::post('image', [ProviderController::class, 'uploadImage'])->name('upload-image');

            Route::resource('users', ProviderUserController::class)->only(['index']);
            Route::prefix('users/{user}')->name('users.')->group(function () {
                Route::post('change-owner', [ProviderUserController::class, 'changeOwner'])->name('change-owner');
                Route::post('resend-invitation', [ProviderUserController::class, 'resendInvitation'])->name('resend-invitation');
                Route::post('reset-password', [ResetPasswordController::class, 'providerResetPassword'])->name('password.reset');
            });
        });

        /** Users */
        Route::resource('users', UserController::class);
        Route::prefix('users/{userId}')->name('users.')->group(function () {
            Route::put('activate', [UserController::class, 'activate'])->name('activate');
            Route::post('resend-invitation', [UserController::class, 'resendInvitation'])->name('resend-invitation');
            Route::post('reset-password', [ResetPasswordController::class, 'shuttleResetPassword'])->name('password.reset');
        });

        /** WHO Data */
        Route::prefix('who')->name('who.')->group(function () {
            Route::resource('icd-10s', WHOICD10Controller::class)->parameter('icd-10s', 'icd10');
        });

        /** Global settings */
        Route::resource('global-settings', GlobalSettingsController::class)->only(['index', 'show', 'update']);

        /** QA */
        Route::prefix('qa')->name('qa.')->group(function () {
            /** Test OCR */
            Route::post('request-id-from-pdf', [OcrController::class, 'getRequestIdFromPdf'])->name('request-id-from-pdf.show');

            /** Upload custom public files to CDN */
            Route::post('upload', [CustomUploadsController::class, 'upload'])->name('upload');

            /** Run cron commands */
            Route::post('run-cron/{command}', [CronController::class, 'runCronCommand'])->middleware('throttle:2,1')->name('run-cron-command');

            Route::post('template/blade-to-pdf', [TemplateController::class, 'bladeToPdf'])->name('template.blade-to-pdf');
            Route::post('template/html-to-pdf', [TemplateController::class, 'htmlToPdf'])->name('template.html-to-pdf');
        });

        Route::prefix('filter-sets')->name('filter-set.')->group(function () {
            Route::post('create', [FilterSetController::class, 'store'])->name('store');
            Route::get('list/{view}', [FilterSetController::class, 'index'])->name('list');
            Route::put('update/{filterSet}', [FilterSetController::class, 'update'])->name('update');
            Route::delete('delete/{filterSet}', [FilterSetController::class, 'destroy'])->name('delete');
        });

        /** QuickSight Embedded Analytics */
        Route::middleware(['throttle.quicksight'])->group(function () {
            Route::post('create-quick-sight-user', [QuicksightController::class, 'createQuickSightUserByAdmin'])->name('create-quick-sight-user');
        });
    });
});
