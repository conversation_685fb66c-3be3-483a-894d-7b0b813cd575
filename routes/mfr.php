<?php

use App\Enums\OrganizationTypeEnum;
use App\Http\Controllers\Manufacturer\AddressController;
use App\Http\Controllers\Manufacturer\Area\DistrictController;
use App\Http\Controllers\Manufacturer\Area\RegionController;
use App\Http\Controllers\Manufacturer\Area\TerritoryController;
use App\Http\Controllers\Manufacturer\Area\ZipCodesController;
use App\Http\Controllers\Manufacturer\DistributorDashboardController;
use App\Http\Controllers\Manufacturer\FilterSetController;
use App\Http\Controllers\Manufacturer\GlobalSearchController;
use App\Http\Controllers\Manufacturer\ManufacturerController;
use App\Http\Controllers\Manufacturer\Order\NotesController;
use App\Http\Controllers\Manufacturer\OrderSourceController;
use App\Http\Controllers\Manufacturer\OrganizationSettingsController;
use App\Http\Controllers\Manufacturer\Patient\AddressController as PatientAddressController;
use App\Http\Controllers\Manufacturer\Patient\PayerController as PatientPayerController;
use App\Http\Controllers\Manufacturer\PatientController as PatientController;
use App\Http\Controllers\Manufacturer\ProductController as ProductController;
use App\Http\Controllers\Manufacturer\SalesDashboardController;
use App\Http\Controllers\Manufacturer\UserController as UserController;
use App\Http\Controllers\User\ResetPasswordController;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => [
    'cognito',
    'cognito-scope:int-mfr-scope',
    'organization:' . OrganizationTypeEnum::MANUFACTURER->value,
]], function () {
    /** MANUFACTURERS */
    Route::prefix('manufacturers/{manufacturer}')->name('manufacturers.')->group(function () {
        Route::get('', [ManufacturerController::class, 'show'])->name('show');
        Route::put('', [ManufacturerController::class, 'update'])->name('update');
        Route::resource('addresses', AddressController::class);

        /** Patients */
        Route::resource('patients', PatientController::class)->only('index', 'show', 'store', 'update');
        Route::prefix('patients/{patient}/')->name('patients.')->group(function () {
            Route::resource('addresses', PatientAddressController::class);
            Route::resource('payers', PatientPayerController::class);
            Route::put('payers', [PatientPayerController::class, 'updatePayers'])->name('payers.update-payers');
        });

        /** Products */
        Route::resource('products', ProductController::class);


        /** Order Source */
        Route::prefix('order-source')->name('order-source.')->group(function () {
            Route::post('', [OrderSourceController::class, 'save'])->name('save');
            Route::put('{orderSource}', [OrderSourceController::class, 'update'])->name('update');
            Route::put('activate', [OrderSourceController::class, 'activate'])->name('activate');
            Route::delete('{orderSource}', [OrderSourceController::class, 'delete'])->name('delete');
            Route::get('{orderSource}/history', [OrderSourceController::class, 'history'])->name('history');

        });

        /** Users */
        Route::resource('users', UserController::class)->withTrashed();
        Route::prefix('users/{user}')->name('users.')->group(function () {
            Route::put('activate', [UserController::class, 'activate'])->name('activate');
            Route::post('resend-invitation', [UserController::class, 'resendInvitation'])->name('resend-invitation');
            Route::post('reset-password', [ResetPasswordController::class, 'manufacturerResetPassword'])->name('password.reset');

            /** Permissions */
            Route::get('permissions', [UserController::class, 'getUserPermissions'])->name('permissions.index');
            Route::post('permissions', [UserController::class, 'updateUserPermissions'])->name('permissions.update');
        });

        /** Distributor Dashboard */
        Route::prefix('dashboard')->name('dashboard.')->group(function () {
            Route::get('distributors', [DistributorDashboardController::class, 'getDistributors'])->name('get-distributors');
            Route::get('distributors/{distributor}', [DistributorDashboardController::class, 'getDistributor'])->name('get-distributor');
            Route::get('days-to-ship', [DistributorDashboardController::class, 'getAverageDaysToShip'])->name('get-days-to-ship');
            Route::get('new-patients', [DistributorDashboardController::class, 'getNewPatients'])->name('get-new-patients');
            Route::get('payer-type-mix', [DistributorDashboardController::class, 'getPayerTypeMix'])->name('get-payer-type-mix');
            Route::get('cancellations', [DistributorDashboardController::class, 'getCancellations'])->name('get-cancellations');
            Route::get('open-pipelines', [DistributorDashboardController::class, 'getOpenPipelines'])->name('get-open-pipelines');
            Route::get('win-rate', [DistributorDashboardController::class, 'getWinRate'])->name('get-win-rate');
            Route::get('new-opportunities', [DistributorDashboardController::class, 'getNewOpportunities'])->name('get-new-opportunities');
            Route::get('summary', [DistributorDashboardController::class, 'getSummary'])->name('get-summary');
            Route::get('top-payers', [DistributorDashboardController::class, 'getTopPayers'])->name('get-top-payers');
            Route::get('orders-by-state', [DistributorDashboardController::class, 'getOrdersByState'])->name('get-orders-by-state');
        });

        /** Territory Management Tools */
        Route::resource('regions', RegionController::class);
        Route::prefix('regions/{region}')->name('regions.')->group(function () {
            Route::put('manager', [RegionController::class, 'updateManager'])->name('update-manager');
        });

        Route::resource('districts', DistrictController::class);
        Route::prefix('districts/{district}')->name('districts.')->group(function () {
            Route::put('manager', [DistrictController::class, 'updateManager'])->name('update-manager');
        });

        Route::resource('territories', TerritoryController::class);
        Route::prefix('territories/{territory}')->name('territories.')->group(function () {
            Route::put('manager', [TerritoryController::class, 'updateManager'])->name('update-manager');
            Route::prefix('zip-codes')->group(function () {
                Route::put('', [TerritoryController::class, 'updateZipCodes'])->name('update-zip-codes');
                Route::delete('{zip_code}', [ZipCodesController::class, 'destroy'])->name('destroy-zip-code');
            });

            Route::prefix('sales-rep/{manufacturer_user}')->group(function () {
                Route::post('zip-codes', [TerritoryController::class, 'assignZipCodesToSalesRep'])->name('assign-zip-codes-to-sales-rep');
                Route::delete('unassign', [TerritoryController::class, 'unassignedZipCodesOfSalesRep'])->name('unassigned-zip-codes-of-sales-rep');
            });
        });

        /** Sales Dashboard */
        Route::prefix('sales-dashboard')->name('sales-dashboard.')->middleware(['manufacturer-sales-dashboard'])->group(function () {
            Route::get('days-to-ship', [SalesDashboardController::class, 'getAverageDaysToShip'])->name('get-days-to-ship');
            Route::get('new-patients', [SalesDashboardController::class, 'getNewPatients'])->name('get-new-patients');
            Route::get('payer-type-mix', [SalesDashboardController::class, 'getPayerTypeMix'])->name('get-payer-type-mix');
            Route::get('cancellations', [SalesDashboardController::class, 'getCancellations'])->name('get-cancellations');
            Route::get('open-pipelines', [SalesDashboardController::class, 'getOpenPipelines'])->name('get-open-pipelines');
            Route::get('provider-users', [SalesDashboardController::class, 'getProviderUsers'])->name('get-provider-users');
            Route::get('referral-trends', [SalesDashboardController::class, 'getReferralTrends'])->name('get-referral-trends');
            Route::get('top-payers', [SalesDashboardController::class, 'getTopPayers'])->name('get-top-payers');
            Route::get('summary', [SalesDashboardController::class, 'getSummary'])->name('get-summary');
            Route::get('document-collection', [SalesDashboardController::class, 'getDocumentCollection'])
                ->name('get-document-collection');
            Route::get('document-collection-detail', [SalesDashboardController::class, 'getDocumentCollectionDetail'])
                ->name('get-document-collection-detail');
            Route::get('shipped-orders-trend', [SalesDashboardController::class, 'shippedOrdersTrend'])->name('shipped-orders-trend');
        });

        Route::prefix('global-search')->name('global-search.')->group(function () {
            Route::get('lead-patient-list', [GlobalSearchController::class, 'leadPatientList'])->name('lead-patient-list');
        });

        Route::get('organization-settings/all', [OrganizationSettingsController::class, 'all'])->name('organization-settings.all');
        Route::patch('organization-settings/bulk-update', [OrganizationSettingsController::class, 'bulkUpdate'])->name('organization-settings.bulk-update');
        Route::apiResource('organization-settings', OrganizationSettingsController::class)->except(['store', 'edit', 'destroy']);


        Route::prefix('filter-sets')->name('filter-set.')->group(function () {
            Route::post('create', [FilterSetController::class, 'store'])->name('store');
            Route::get('list/{view}', [FilterSetController::class, 'index'])->name('list');
            Route::put('update/{filterSet}', [FilterSetController::class, 'update'])->name('update');
            Route::delete('delete/{filterSet}', [FilterSetController::class, 'destroy'])->name('delete');
        });

        //Order Notes
        Route::prefix('orders')->name('orders.')->group(function () {
            Route::prefix('{order}')->group(function () {
                Route::prefix('notes')->name('notes.')->group(function () {
                    Route::get('', [NotesController::class, 'list'])->name('list');
                    Route::post('', [NotesController::class, 'createNote'])->name('create-note');
                });
            });
        });

    });


});
