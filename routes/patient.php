<?php

use App\Http\Controllers\Patient\InsuranceCardRequestController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Patient API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for patient-facing functionality.
| These routes are publicly accessible via token-based authentication.
|
*/

Route::prefix('patient')->name('patient.')->group(function () {
    Route::prefix('insurance-card-requests')->name('insurance-card-requests.')
        ->middleware('validate-insurance-card-token')->group(function () {
            Route::get('{token}', [InsuranceCardRequestController::class, 'show'])->name('show');
            Route::post('{token}/images', [InsuranceCardRequestController::class, 'submitImages'])->name('submit-images');
        });
    Route::post('insurance-card-requests/{token}/resend', [InsuranceCardRequestController::class, 'resend'])
        ->name('insurance-card-requests.resend-public');
});
