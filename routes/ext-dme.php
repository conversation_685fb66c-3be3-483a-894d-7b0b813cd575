<?php

use App\Enums\OrganizationTypeEnum;
use App\Http\Controllers\Api\V1\FaxIdentificationController;
use App\Http\Controllers\External\Distributor\DistributorCampaignController;
use App\Http\Controllers\External\Distributor\EligibilityChecksController;
use App\Http\Controllers\External\Distributor\LeadController;
use App\Http\Controllers\External\Distributor\MeController;
use App\Http\Controllers\External\Distributor\PayerSearchController;
use Illuminate\Support\Facades\Route;

Route::prefix('distributors')->name('dme.')->middleware([
    'auth:ext-api',
    'client:ext-dme-scope',
    'organization:' . OrganizationTypeEnum::DISTRIBUTOR->value,
])->group(function () {
    /* distributor (me) */
    Route::get('', [MeController::class, 'show'])->name('show');
    Route::put('', [MeController::class, 'update'])->name('update');

    /* leads */
    Route::resource('leads', LeadController::class)->only(['index', 'store']);

    /* distributor campaigns */
    Route::resource('campaigns', DistributorCampaignController::class)->only(['index']);

    /* external payer search */
    Route::get('payers/search', [PayerSearchController::class, 'search'])->name('payers.search');
    /* eligibility checks */
    Route::prefix('eligibility-checks')->name('eligibility-checks.')->group(function () {
        Route::post('create', [EligibilityChecksController::class, 'create'])->name('create');
    });

    // Fax AI Identification API
    Route::post('faxes/identification', [FaxIdentificationController::class, 'store'])->name('api.v1.faxes.identification.store');

});
