<?php

use App\Enums\OrganizationTypeEnum;
use App\Http\Controllers\External\Manufacturer\CampaignController;
use App\Http\Controllers\External\Manufacturer\MeController;
use App\Http\Controllers\External\Manufacturer\NpiController;
use App\Http\Controllers\External\Manufacturer\OrderController;
use App\Http\Controllers\External\Manufacturer\Patient\AddressController as PatientAddressController;
use App\Http\Controllers\External\Manufacturer\Patient\PayerController as PatientPayerController;
use App\Http\Controllers\External\Manufacturer\PatientController;
use App\Http\Controllers\External\Manufacturer\PayerController;
use App\Http\Controllers\External\Manufacturer\ProductController;
use Illuminate\Support\Facades\Route;

Route::prefix('manufacturers')->name('mfr.')->middleware([
    'auth:ext-api',
    'client:ext-mfr-scope',
    'organization:' . OrganizationTypeEnum::MANUFACTURER->value,
])->group(function () {
    /* Manufacturer (me) */
    Route::get('', [MeController::class, 'show'])->name('show');
    Route::put('', [MeController::class, 'update'])->name('update');

    /* Manufacturer campaigns */
    Route::get('campaigns/own', [CampaignController::class, 'own'])->name('campaigns.own');
    Route::get('campaigns/own/distributors', [CampaignController::class, 'ownDistributors'])->name('campaigns.own.distributors');

    Route::resource('orders', OrderController::class)->only(['index', 'show', 'store']);
    Route::prefix('orders/{order}/')->name('orders.')->group(function () {
        Route::put('cancel', [OrderController::class, 'cancel'])->name('cancel');
    });
    Route::resource('patients', PatientController::class)->only('index', 'show', 'store', 'update');
    Route::prefix('patients/{patient}/')->name('patients.')->group(function () {
        Route::resource('addresses', PatientAddressController::class);
        Route::resource('payers', PatientPayerController::class);
        Route::post('payers/{payer}/check-eligibility', [PatientPayerController::class, 'checkEligibility'])->name('payers.check-eligibility');
        Route::get('payers/{payer}/check-eligibility/last', [PatientPayerController::class, 'getCheckEligibility'])->name('payers.check-eligibility.last');
    });
    Route::resource('products', ProductController::class);
    Route::get('product-categories', [ProductController::class, 'productCategoriesIndex'])->name('product-categories.index');
    Route::get('product-categories/{productCategory}', [ProductController::class, 'productCategoriesShow'])->name('product-categories.show');

    Route::get('payers', [PayerController::class, 'index'])->name('payers.index');
    Route::get('providers/npi-registry/search-physician', [NpiController::class, 'searchPhysician'])->name('providers.npi-registry.search-physician');
});
