<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link href="https://fonts.googleapis.com/css2?family=Inter&display=swap" rel="stylesheet" />
    <title>PDF Template</title>
    <style type="text/css">
      .main-container {
        width: 100%;
      }
      body {
        font-family: 'inter', sans-serif;
      }
      a {
        color: {{ $primary_color }};
      }
      p {
        padding: 0;
        margin: 0;
        font-size: 14px;
      }
      table {
        width: 100%;
        color: #101137;
        border-collapse: collapse;
      }
      .info-table td {
        width: 50%;
      }
      td {
        padding-left: 10px;
      }
      td p b {
        padding: 5px;
      }
      .header-table tr td {
        padding-left: 0;
      }
      .info-table {
        margin-bottom: 15px;
      }
      .table-header .heading {
        text-align: left;
        font-size: 16px;
        padding-left: 10px;
        padding-bottom: 7px;
      }
        .certificate-table {
            padding-left: 16px;
            padding-right: 16px;
        }
        .certificate-table td {
            font-style: normal;
            line-height: 130%;
            font-weight: 400;
            font-size: 14px;
        }
        .certificate-table .certificate-title {
            font-size: 24px;
            color: {{ $primary_color }};
            font-weight: 600;
        }
        .certificate-table .sub-title {
            color: {{ $primary_color }};
            font-size: 16px;
            font-weight: 600;
        }
        .certificate-signature {
            margin-top: 8px;
        }
        .certificate-signature td {
            border: 1px solid #CED0D3;
            border-radius: 4px;
            text-align: center;
            padding: 10px;
            height: 200px;
        }
        .wrapper {
            padding-bottom: 8px;
            margin-bottom: 16px;
            border-bottom: 1px solid #CED0D3;
        }
        .wrapper > tbody > tr > td {
            width: 50%;
            vertical-align: baseline;
        }
        .title-table {
            padding-bottom: 16px;
        }
        .legal {
            margin-top: 120px;
        }
        .legal td {
            padding-bottom: 12px;
            font-size: 12px;
            line-height: 16px;
            color: #a3a6ab;
        }
        .image {
            width: 100%;
            max-width: 400px;
        }
        .page-break {
            page-break-before: always;
        }
        .content {
            padding: 0;
        }
        .content td {
            padding: 4px 0;
        }
        .content td:first-child {
            font-weight: 600;
        }
    </style>
  </head>
  <body>
    <main class="main-container">
      <table class="header-table" style="margin-bottom: 30px">
        <tr style="height: 30px">
          <td style="width: 165px; padding-top: 1px" rowspan="4">
            <img
              style="height: 120px"
              class="logo "
              src="{{ $logo_url }}"
              alt="AOB Template"
            />
          </td>
          <td colspan="2">
            <p style="font-size: 16px; padding-left: 0">
              <b>ASSIGNMENT OF BENEFITS AND RELEASE OF INFORMATION</b>
            </p>
          </td>
        </tr>
        <tr style="height: 30px">
          <td>
            <p><b>Appy Medical, LLC</b></p>
          </td>
          <td>
            <p><b>Phone:</b> (*************</p>
          </td>
        </tr>
        <tr style="height: 30px">
          <td>
            <p>
              <b>Website:</b>
              <a href="https://www.appymedical.com">https://www.appymedical.com</a>
            </p>
          </td>
          <td>
            <p>
              <b>Email:</b>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </td>
        </tr>
        <tr style="height: 30px">
          <td colspan="2">
            <p><b>Address: </b>8789 South Redwood Rd, Suite 250 West Jordan, UT 84088</p>
          </td>
        </tr>
      </table>

      <!-- Patient Information Section -->
      <table class="info-table">
        <thead class="table-header">
          <tr style="height: 40px">
            <th class="heading" colspan="2">Patient Information</th>
          </tr>
        </thead>
        <tbody>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Name: <b>{{ $patient['name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Date of Birth: <b>{{ $patient['date_of_birth'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Gender: <b>{{ $patient['gender'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Street Address: <b>{{ $patient['address_line_1'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>City: <b>{{ $patient['city'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>State: <b>{{ $patient['state'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Zipcode: <b>{{ $patient['zip'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Primary Phone: <b>{{ $patient['primary_phone'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Secondary Phone: <b>{{ $patient['secondary_phone'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Email Address: <b>{{ $patient['email'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Name of Guardian: <b>{{ $patient['guardian_name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Preferred Method of Contact: <b>{{ $patient['contact_method'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Emergency Contact Name: <b>{{ $patient['emergency_name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Relation: <b>{{ $patient['relation'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Emergency Contact Phone: <b>{{ $patient['emergency_phone'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px"></td>
          </tr>
        </tbody>
      </table>
      <!-- End of Patient Information Section -->

      <!-- Physician Information Section -->
      <table class="info-table">
        <thead class="table-header">
          <tr style="height: 40px">
            <th class="heading" colspan="2">Physician Information</th>
          </tr>
        </thead>
        <tbody>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Prescriber Name: <b>{{ $physician['name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Speciality: <b>{{ $physician['specialty'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Office Address: <b>{{ $physician['address_line_1'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>NPI (If Available): <b>{{ $physician['npi'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>City: <b>{{ $physician['city'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>State: <b>{{ $physician['state'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Zipcode: <b>{{ $physician['zip'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Office Phone: <b>{{ $physician['phone'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Office Fax: <b>{{ $physician['fax'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Office Contact Name: <b>{{ $physician['contact_name'] }}</b></p>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End of Physician Information Section -->

      <!-- Primary Insurance Information Section -->
      <table class="info-table">
        <thead class="table-header">
          <tr style="height: 40px">
            <th class="heading" colspan="2">Primary Insurance Information</th>
          </tr>
        </thead>
        <tbody>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Insurance Name: <b>{{ $primary_insurance['name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Policy ID: <b>{{ $primary_insurance['policy_number'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Group Number: <b>{{ $primary_insurance['group_number'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Provider Phone Number: <b>{{ $primary_insurance['phone'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Plan Type: <b>{{ $primary_insurance['plan_type'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Policy Holder's Name: <b>{{ $primary_insurance['holder_name'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Date of Birth: <b>{{ $primary_insurance['date_of_birth'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Relation to Patient: <b>{{ $primary_insurance['relation'] }}</b></p>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End of Primary Insurance Information Section -->

      <!-- Secondary Insurance Information Section -->
      <table class="info-table">
        <thead class="table-header">
          <tr style="height: 40px">
            <th class="heading" colspan="2">Secondary Insurance Information</th>
          </tr>
        </thead>
        <tbody>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Insurance Name: <b>{{ $secondary_insurance['name'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Policy ID: <b>{{ $secondary_insurance['policy_number'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Group Number: <b>{{ $secondary_insurance['group_number'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Provider Phone Number: <b>{{ $secondary_insurance['phone'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Plan Type: <b>{{ $secondary_insurance['plan_type'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Policy Holder's Name: <b>{{ $secondary_insurance['holder_name'] }}</b></p>
            </td>
          </tr>
          <tr style="height: 40px">
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Date of Birth: <b>{{ $secondary_insurance['date_of_birth'] }}</b></p>
            </td>
            <td style="border: 1px solid #dfe2e5; padding: 12px 10px">
              <p>Relation to Patient: <b>{{ $secondary_insurance['relation'] }}</b></p>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End of Secondary Insurance Information Section -->

      <!-- Disclaimer Information Section -->
      <table class="disclaimer-table">
        <thead class="table-header">
          <tr style="height: 40px">
            <th style="padding-left: 0; padding-bottom: 3px; text-align: left" colspan="2">
              Assignment of Insurance Benefits and Authorization to Release Information
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding-left: 0">
              <p style="text-align: justify; line-height: 20px">
                Please be aware that all medical information is confidential under certain state and federal
                laws. Such information may not be released without your consent. Many insurance carriers
                require medical information to be submitted with claims to evaluate medical necessity. Please
                provide your written consent to release related information when required or requested to your
                insurance company(s) and/or your healthcare team. I do hereby authorize Appy Medical, LLC to
                acquire from and/or release to my healthcare team, and/or my public or private insurance
                provider(s), and/or contracted distributors any information required for the purposes of
                healthcare management and/or for processing and reviewing all past, present and future medical
                claims on my behalf, including deductible amounts. I understand that upon acceptance of
                products from Appy Medical, LLC, I assume responsibility for any deductible, co-pay, or other
                balance not covered by my insurance carrier.
                I understand that if I do not pay for this product or service upon receipt of an invoice, I
                may receive autodialed, pre-recorded calls, or both, from or on behalf of Appy Medical at
                the telephone or wireless number(s) provided above. I consent to receiving future calls at
                those number(s) by autodialed calls, pre-recorded calls, or both, and understand that my
                consent to such calls is not a condition of purchasing any goods or services.
                I authorize Appy Medical, LLC to submit claims to my insurance company on my behalf, and my
                insurance company to pay benefits directly to Appy Medical, LLC. Should any insurance payment
                be made directly to the insured for monies due on this account, I agree to immediately pay
                over these funds to Appy Medical, LLC. I will be informed of my insurance coverage and
                estimated out-of-pocket expense prior to any shipment of products, or any bills being sent. I
                will notify Appy Medical, LLC in the event my insurance changes. This authorization will
                remain in effect until I revoke it in writing. I acknowledge that I have received a copy of
                the Notices of Privacy Practices for Appy Medical, LLC and of the state and federal Medicare,
                health care fraud, and abuse disclosures or have reviewed those documents online at
                <a href="http://appymedical.com/">Appy Medical</a>. If the recipient of products or devices is
                under the age of 18, then I represent that I am the minor’s guardian and I am signing on
                his/her behalf I further acknowledge that Appy Medical, LLC has various policies posted on
                <a href="http://www.appymedical.com/">Appy Medical</a>
                (including Privacy Policy and Patient’s Rights) and that I agree to the terms of those
                policies.
              </p>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End of Disclaimer Information Section -->

      <!-- Signature Section -->
      <table style="margin-top: 20px width: 100%; table-layout: fixed;">
        <tbody>
          <tr>
            <td colspan="2" style="border-top: 1px solid #dfe2e5; padding-bottom: 10px"></td>
          </tr>
          <tr>
            <td style="width:80%">
              <p>
                <b style="padding: 0">Patient/Guardian Signature:</b>
              </p>
              @if (!empty($signatureSrc))
                    <img
                        src="data:image/png;base64,{{ $signatureSrc }}"
                        alt="Signature"
                        style="margin-top: 10px; display: block; width: 100%;max-width: 300px;"
                    />
              @endif
            </td>
            <td style="vertical-align: top; width:20%;">
              <p><b style="padding: 0">Date:</b></p>
              <p style="margin-top: 10px">{{ $signatureDate }}</p>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- Signature Section -->
      @if(!empty($envelope))
        <div class="page-break"></div>
        @include('pdf.aob.parts.certificate')
      @endif
    </main>
  </body>
</html>