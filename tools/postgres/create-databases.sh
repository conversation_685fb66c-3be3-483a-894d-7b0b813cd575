#!/bin/bash

set -euo pipefail

function create_extra_database() {
	local database=$1
	echo "  Creating user and database '$database'"
	psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" <<-EOSQL
		CREATE USER $database WITH PASSWORD '$database';
		CREATE DATABASE $database;
		GRANT ALL ON DATABASE $database TO $database;
		ALTER DATABASE $database OWNER TO $database;
EOSQL
}

if [ -n "$POSTGRES_EXTRA_DBS" ]; then
	echo "Additional database creation requested: $POSTGRES_EXTRA_DBS"
	for db in $(echo "$POSTGRES_EXTRA_DBS" | tr ',' ' '); do
		create_extra_database "$db"
	done
	echo "Multiple databases created"
fi
