version: '3.7'

services:
  api:
    build:
      context: ./tools/php-fpm
      dockerfile: Dockerfile.local
      args:
        WWWGROUP: '${WWWGROUP:-1001}'
    image: shuttle-health-api:local
    container_name: api.sh
    depends_on:
      - postgres
      - redis
    environment:
      XDEBUG_MODE: 'off'
      XDEBUG_CONFIG: 'host.iac.internal'
    extra_hosts:
      - 'host.iac.internal:host-gateway'
    networks:
      - shuttle-net
    volumes:
      - './:/var/www/'
      - './tools/php-fpm/conf/local/www.conf:/usr/local/etc/php-fpm.d/zz-docker.conf'
      - './tools/php-fpm/conf/local/supervisor.conf:/etc/supervisord.conf'
      - './tools/php-fpm/conf/local/crontab:/etc/cron.d/crontab'
      - './tools/php-fpm/conf/local/php.ini:/usr/local/etc/php/php.ini'
    restart: unless-stopped
    tty: true
    working_dir: /var/www
    # add healthcheck for pre nginx

  nginx:
    image: nginx:alpine
    container_name: nginx.sh
    depends_on:
      - api
    hostname: api-local.shuttle.health
    networks:
      - shuttle-net
    ports:
      - '${APP_HTTPS_PORT:-443}:443'
    restart: unless-stopped
    tty: true
    volumes:
      - './public:/var/www/public'
      - './storage/app:/var/www/storage/app'
      - './tools/nginx/conf.d:/etc/nginx/conf.d'
      - './tools/nginx/ssl:/etc/nginx/ssl'

  postgres:
    build:
      context: ./tools/postgres
      dockerfile: Dockerfile
    image: shuttle-health-postgres:local
    container_name: postgres.sh
    environment:
      POSTGRES_DB: '${DB_DATABASE}'
      POSTGRES_EXTRA_DBS: 'test_${DB_DATABASE}'
      POSTGRES_USER: '${DB_USERNAME}'
      POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
    networks:
      - shuttle-net
    ports:
      - '${DB_PORT:-5432}:5432'
    restart: unless-stopped
    volumes:
      - 'api-sh-fs-postgres:/var/lib/postgresql/data'
    healthcheck:
      test: ["CMD", "pg_isready", "-q", "-d", "${DB_DATABASE}", "-U", "${DB_USERNAME}"]
      retries: 3
      timeout: 5s

  redis:
    image: redis:alpine
    container_name: redis.sh
    networks:
      - shuttle-net
    ports:
      - '${REDIS_PORT:-6379}:6379'
    restart: unless-stopped
    volumes:
      - 'api-sh-fs-redis:/data'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s

opensearch-node1: # This is also the hostname of the container within the Docker network (i.e. https://opensearch-node1/)
  image: opensearchproject/opensearch:2.13.0 # Specifying the latest available image - modify if you want a specific version
  container_name: opensearch-node1
  environment:
    - discovery.type=single-node
    - node.name=opensearch-node1 # Name the node that will run in this container
    - bootstrap.memory_lock=true # Disable JVM heap memory swapping
    - "OPENSEARCH_JAVA_OPTS=-Xms1024m -Xmx1024m" # Set min and max JVM heap sizes to at least 50% of system RAM
    - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Shutt!3He@lth    # Sets the demo admin user password when using demo configuration, required for OpenSearch 2.12 and later
  ulimits:
    memlock:
      soft: -1 # Set memlock to unlimited (no soft or hard limit)
      hard: -1
    nofile:
      soft: 65536 # Maximum number of open files for the opensearch user - set to at least 65536
      hard: 65536
    # On Windows WSL
    # wsl -d docker-desktop
    # sysctl -w vm.max_map_count=262144
    #
  volumes:
    - opensearch-data1:/usr/share/opensearch/data # Creates volume called opensearch-data1 and mounts it to the container
  ports:
    - 9200:9200 # REST API
    - 9600:9600 # Performance Analyzer
  networks:
    - shuttle-net

opensearch-dashboards:
  image: opensearchproject/opensearch-dashboards:2.13.0
  container_name: opensearch-dashboards
  ports:
    - 5601:5601 # Map host port 5601 to container port 5601
  expose:
    - "5601" # Expose port 5601 for web access to OpenSearch Dashboards
  environment:
    - 'OPENSEARCH_HOSTS=["https://opensearch-node1:9200"]'
  networks:
    - shuttle-net
  depends_on:
    - opensearch-node1

networks:
  shuttle-net:
    name: shuttle-net
    driver: bridge

volumes:
  api-sh-fs-postgres:
    driver: local
  api-sh-fs-redis:
    driver: local
  opensearch-data1:
    driver: local
