APP_NAME="Shuttle Health"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_LOG_QUERIES=false
API_DEBUGGER_ENABLED=true
APP_VERSION=v0.0.0

APP_DOMAIN="shuttle.health"
API_HOSTNAME="api-local.${APP_DOMAIN}"
APP_URL="https://${API_HOSTNAME}"
APP_HTTP_PORT=80
APP_HTTPS_PORT=443

PROVIDER_CLIENT="hcp-local.${APP_DOMAIN}"
DISTRIBUTOR_CLIENT="dme-local.${APP_DOMAIN}"
MANUFACTURER_CLIENT="mfr-local.${APP_DOMAIN}"
SH_ADMIN_CLIENT="sha-local.${APP_DOMAIN}"
CORS_ORIGINS="${PROVIDER_CLIENT},${DISTRIBUTOR_CLIENT},${MANUFACTURER_CLIENT},${SH_ADMIN_CLIENT}"

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_PORT=5432
DB_DATABASE=shuttle_health
DB_HOST=postgres
DB_USERNAME=shuttle_health
DB_PASSWORD=secret
DB_USE_IAM_AUTH=false

LOG_HIDE_RAW_SQL_IN_EXCEPTIONS=false
SENTRY_TRACE_SQL_BINDINGS_ENABLED=true

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
HORIZON_MEMORY_LIMIT=64

SESSION_DOMAIN=".${APP_DOMAIN}"
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

GMAIL_SERVICE_ACCOUNT_GOOGLE_APPLICATION_CREDENTIALS=

SH_MANAGER_EMAIL=<EMAIL>
SH_PAYER_MANAGER_EMAIL=<EMAIL>

FILESYSTEM_DISK=local

PUBLIC_CONTENT_DISK=local-public-content
PRIVATE_CONTENT_DISK=local-private-content
DOCUMENTATION_DISK=local-documentation

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-2
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_BUCKET_PUBLIC_CONTENT=sh-public-content
AWS_BUCKET_PRIVATE_CONTENT=sh-private-content
AWS_BUCKET_DOCUMENTATION=sh-documentation

CDN_URL=https://aws.cdn

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

CHANGE_HEALTHCARE_URL=
CHANGE_HEALTHCARE_CLIENT_ID=
CHANGE_HEALTHCARE_SECRET=

SIGNALWIRE_FAX_NUMBER=
SIGNALWIRE_PROJECT_ID=
SIGNALWIRE_SPACE_URL=
SIGNALWIRE_TOKEN=
SIGNALWIRE_TEXT_NUMBER=
SIGNALWIRE_SIGNATURE=
SIGNALWIRE_FAX_LINK_TTL=

TWILIO_ACCOUNT_SID=
TWILIO_MESSAGING_SID=
TWILIO_AUTH_TOKEN=

SLACK_ENG_NOTIFICATION_URL=
SLACK_QA_NOTIFICATION_URL=

PRIMARY_COMMUNICATIONS_SERVICE=

P_VERIFY_URL=
P_VERIFY_CLIENT_ID=
P_VERIFY_CLIENT_SECRET=

EDI_PARSER_URL=

HORIZON_PATH='horizon'
HORIZON_USER=
HORIZON_PASSWORD=

DB_ANONYMIZATION_PORT=
DB_ANONYMIZATION_DATABASE=
DB_ANONYMIZATION_HOST=
DB_ANONYMIZATION_USERNAME=
DB_ANONYMIZATION_PASSWORD=


ZENDESK_SUBDOMAIN=
ZENDESK_USERNAME=
ZENDESK_TOKEN=
ZENDESK_SSO_SHARED_SECRET=

# OpenSearch
OS_HOSTS="https://opensearch-node1:9200"
OS_USERNAME=admin
OS_PASSWORD=Shutt!3He@lth
OS_INDEX_PREFIX=false
# prefix will be added to all indexes created by the package with an underscore
# ex: my_app_user_logs for UserLog.php model

OS_SIG_V4_PROVIDER=
OS_SIG_V4_REGION=
OS_SIG_V4_SERVICE=

OS_SSL_CERT=
OS_SSL_CERT_PASSWORD=
OS_SSL_KEY=
OS_SSL_KEY_PASSWORD=

OS_OPT_VERIFY_SSL=false
OS_OPT_RETRIES=
OS_OPT_SNIFF_ON_START=
OS_OPT_PORT_HOST_HEADERS=



COGNITO_CLIENT_ID = 3r75cv1e1anrmj21n0ju79vl2c
COGNITO_CLIENT_REGION = us-east-2

DEFAULT_SEED_PASSWORD="P@ssw0rd!SH#2024"
APP_BYPASS_COMMUNICATION_WINDOW=true
