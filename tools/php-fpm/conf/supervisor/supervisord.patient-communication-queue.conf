[supervisord]
nodaemon=true
loglevel=info
logfile=/tmp/supervisord.log
pidfile=/var/run/supervisord.pid
strip_ansi=true

[program:laravel-queue-redis-patient-communication]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis-patient-communication
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stopwaitsecs=3600