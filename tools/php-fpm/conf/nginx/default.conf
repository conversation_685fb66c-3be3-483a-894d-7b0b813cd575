upstream php-fpm {
    server unix:/var/run/php-fpm.sock;
    #server localhost:9000;
}

server {
    listen 443 ssl default_server;
    server_name localhost;

    ssl_certificate /etc/nginx/ssl/shuttle.health.crt;
    ssl_certificate_key /etc/nginx/ssl/shuttle.health.key;

    # Handle client IP from AWS ALB
    set_real_ip_from 10.0.0.0/8;
    set_real_ip_from 127.0.0.1;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    # Laravel web root directory
    root /var/www/public;
    index index.php index.html;

    # Add security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload' always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "accelerometer=(),camera=(),geolocation=(),gyroscope=(),magnetometer=(),microphone=(),payment=(),usb=()" always;

    charset utf-8;

    # Nginx health
    location = /health {
        access_log off;
        default_type application/json;
        return 200 '{"status": "OK"}';
    }

    # API health
    location = /api/health {
        access_log off;
        include fastcgi_params;
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
    }
}

server {
    listen 443 ssl;
    server_name api-dev.shuttle.health;

    ssl_certificate /etc/nginx/ssl/shuttle.health.crt;
    ssl_certificate_key /etc/nginx/ssl/shuttle.health.key;

    # Increase buffer size
    proxy_buffer_size 16k;
    proxy_buffers 4 16k;
    proxy_busy_buffers_size 32k;

    # Handle client IP from AWS ALB
    set_real_ip_from **********/16;
    set_real_ip_from 127.0.0.1;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    # Allow larger file uploads
    client_max_body_size 100M;

    # Laravel web root directory
    root /var/www/public;
    index index.php index.html;

    charset utf-8;

    # Add security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload' always;    
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "accelerometer=(),camera=(),geolocation=(),gyroscope=(),magnetometer=(),microphone=(),payment=(),usb=()" always;


    location /horizon {
        add_header Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; font-src * data:; connect-src *; frame-src *;" always;
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    location / {
        add_header Content-Security-Policy "default-src 'self'" always;
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }
        
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }
    
    location = /robots.txt  {
        access_log off;
        log_not_found off;
    }

    error_page 404 /index.php;

    location ~ \.php$ {

        # Check if the request is for Horizon
        if ($request_uri ~* "^/wPo2B5MbtWZb5VqAT1T2zGGQA9DfBR52HcV(/.*)?$") {
            add_header Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; font-src * data:; connect-src *; frame-src *;" always;
        }

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        if (!-f $document_root$fastcgi_script_name) {
            return 404;
        }
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_index index.php;

        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;

        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}

server {
    listen 443 ssl;
    server_name api-qa.shuttle.health;

    ssl_certificate /etc/nginx/ssl/shuttle.health.crt;
    ssl_certificate_key /etc/nginx/ssl/shuttle.health.key;

    # Increase buffer size
    proxy_buffer_size 16k;
    proxy_buffers 4 16k;
    proxy_busy_buffers_size 32k;

    # Handle client IP from AWS ALB
    set_real_ip_from **********/16;
    set_real_ip_from 127.0.0.1;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    # Allow larger file uploads
    client_max_body_size 100M;

    # Laravel web root directory
    root /var/www/public;
    index index.php index.html;

    # Add security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload' always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "accelerometer=(),camera=(),geolocation=(),gyroscope=(),magnetometer=(),microphone=(),payment=(),usb=()" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    # Pass the PHP scripts to PHP-FPM listening on php-fpm.sock
    location ~ \.php$ {

        # Check if the request is for Horizon
        if ($request_uri ~* "^/wPo2B5MbtWZb5VqAT1T2zGGQA9DfBR52HcV(/.*)?$") {
            add_header Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; font-src * data:; connect-src *; frame-src *;" always;
        }

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        if (!-f $document_root$fastcgi_script_name) {
            return 404;
        }
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_index index.php;

        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;

        include fastcgi_params;
    }
   
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}

server {
    listen 443 ssl;
    server_name api-preprod.shuttle.health;

    ssl_certificate /etc/nginx/ssl/shuttle.health.crt;
    ssl_certificate_key /etc/nginx/ssl/shuttle.health.key;

    # Increase buffer size
    proxy_buffer_size 16k;
    proxy_buffers 4 16k;
    proxy_busy_buffers_size 32k;

    # Handle client IP from AWS ALB
    set_real_ip_from **********/16;
    set_real_ip_from 127.0.0.1;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    # Allow larger file uploads
    client_max_body_size 100M;

    # Laravel web root directory
    root /var/www/public;
    index index.php index.html;

    # Add security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload' always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "accelerometer=(),camera=(),geolocation=(),gyroscope=(),magnetometer=(),microphone=(),payment=(),usb=()" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    # Pass the PHP scripts to PHP-FPM listening on php-fpm.sock
    location ~ \.php$ {

        # Check if the request is for Horizon
        if ($request_uri ~* "^/wPo2B5MbtWZb5VqAT1T2zGGQA9DfBR52HcV(/.*)?$") {
            add_header Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; font-src * data:; connect-src *; frame-src *;" always;
        }

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        if (!-f $document_root$fastcgi_script_name) {
            return 404;
        }
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_index index.php;

        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;

        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}

server {
    listen 443 ssl;
    server_name api.shuttle.health;

    ssl_certificate /etc/nginx/ssl/shuttle.health.crt;
    ssl_certificate_key /etc/nginx/ssl/shuttle.health.key;

    # Increase buffer size
    proxy_buffer_size 16k;
    proxy_buffers 4 16k;
    proxy_busy_buffers_size 32k;

    # Handle client IP from AWS ALB
    set_real_ip_from **********/16;
    set_real_ip_from 127.0.0.1;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    # Allow larger file uploads
    client_max_body_size 100M;

    # Laravel web root directory
    root /var/www/public;
    index index.php index.html;

    # Add security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload' always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "accelerometer=(),camera=(),geolocation=(),gyroscope=(),magnetometer=(),microphone=(),payment=(),usb=()" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    # Pass the PHP scripts to PHP-FPM listening on php-fpm.sock
    location ~ \.php$ {

        # Check if the request is for Horizon
        if ($request_uri ~* "^/wPo2B5MbtWZb5VqAT1T2zGGQA9DfBR52HcV(/.*)?$") {
            add_header Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; font-src * data:; connect-src *; frame-src *;" always;
        }

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        if (!-f $document_root$fastcgi_script_name) {
            return 404;
        }
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_index index.php;

        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;

        include fastcgi_params;
    }
   
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}