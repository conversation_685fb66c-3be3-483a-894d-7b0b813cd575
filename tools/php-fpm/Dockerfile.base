FROM php:8.1-fpm AS base

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    ca-certificates \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    jpegoptim optipng pngquant gifsicle \
    curl \
    nginx \
    supervisor \
    postgresql-client \
    # need for gd
    zip \
    unzip \
    # need for intl
    lua-zlib-dev \
    # need for mbstring
    libonig-dev \
    libzip-dev \
    # need for pdo_pgsql
    libpq-dev \
    # Install php extensions
    && \
    docker-php-ext-configure opcache --enable-opcache \
    && \
    docker-php-ext-install \
        bcmath \
        exif \
        gd \
        mbstring \
        pcntl \
        pdo \
        pdo_pgsql \
        zip \
        opcache \
    && pecl install -o -f redis \
    && \
    mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini" \
    && \
    # Configure opcache
    echo "opcache.enable=1" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.enable_cli=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.revalidate_path=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.max_file_size=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.max_accelerated_files=100000" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.max_wasted_percentage=10" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.validate_timestamps=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.revalidate_freq=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.error_log='/proc/self/fd/2'" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.memory_consumption=256" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.save_comments=0" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.interned_strings_buffer=64" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.fast_shutdown=1" >> /usr/local/etc/php/conf.d/opcache.ini && \
    # Configure PHP
    # php.ini
    sed -i "s|;*upload_max_filesize =.*|upload_max_filesize = 100M|i" "$PHP_INI_DIR/php.ini" && \
    sed -i "s|;*memory_limit =.*|memory_limit = 2048M|i" "$PHP_INI_DIR/php.ini" && \
    sed -i "s|;*max_file_uploads =.*|max_file_uploads = 20|i" "$PHP_INI_DIR/php.ini" && \
    sed -i "s|;*post_max_size =.*|post_max_size = 100M|i" "$PHP_INI_DIR/php.ini" && \
    sed -i "s|;*cgi.fix_pathinfo=.*|cgi.fix_pathinfo = 0|i" "$PHP_INI_DIR/php.ini" && \
    sed -i 's/expose_php = On/expose_php = Off/g' "$PHP_INI_DIR/php.ini" && \
    sed -i "s|;*fastcgi.logging=.*|fastcgi.logging = Off|i" "$PHP_INI_DIR/php.ini" && \
    # www.conf
    echo '[www]' > /usr/local/etc/php-fpm.d/www.conf && \
    echo 'user=www-data' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'group=www-data' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'listen=127.0.0.1:9000' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'pm=dynamic' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'pm.max_children=50' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'pm.min_spare_servers=15' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'pm.max_spare_servers=25' >> /usr/local/etc/php-fpm.d/www.conf && \
    echo 'pm.start_servers=15' >> /usr/local/etc/php-fpm.d/www.conf && \
    #echo 'pm.max_requests=1000' >> /usr/local/etc/php-fpm.d/www.conf && \
    docker-php-ext-enable \
        redis \
        opcache \
    && rm -rf /tmp/pear/* \
    # Install composer
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
    # Clear cache
    && apt-get clean && rm -rf /var/lib/apt/lists/*