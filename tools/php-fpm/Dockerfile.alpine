FROM 717177975706.dkr.ecr.us-east-2.amazonaws.com/shuttle-health-api:8.3-fpm-alpine3.21 AS final

# Unique SENTRY_RELEASE value generated during CI/CD
ARG SENTRY_RELEASE
ENV SENTRY_RELEASE=${SENTRY_RELEASE}

ARG APP_VERSION
ENV APP_VERSION=${APP_VERSION}

# Set working directory
WORKDIR /var/www

COPY --chown=www-data:www-data . .

RUN echo "Copy nginx/php/supervisor configs" \
    && mv tools/php-fpm/conf/supervisor/supervisord* /etc \
    && mv tools/php-fpm/conf/php/php.ini-production $PHP_INI_DIR/php.ini \
    && rm -rf /usr/local/etc/php-fpm.d/* \
    && mv tools/php-fpm/conf/php/www.conf /usr/local/etc/php-fpm.d/www.conf \
    && mv tools/php-fpm/conf/nginx/nginx.conf /etc/nginx/nginx.conf \
    && mv tools/php-fpm/conf/nginx/default.conf /etc/nginx/conf.d/default.conf \
    && mv tools/php-fpm/docker-entrypoint.sh /docker-entrypoint.sh \
    && mkdir -p /etc/nginx/ssl \
    && mv tools/php-fpm/conf/nginx/shuttle.health.crt /etc/nginx/ssl/shuttle.health.crt \
    && mv tools/php-fpm/conf/nginx/shuttle.health.key /etc/nginx/ssl/shuttle.health.key \
    && mkdir -p /etc/cron.d \
    && mv tools/php-fpm/conf/cron/crontab /etc/cron.d/crontab \
    && mv tools/php-fpm/pdfid/pdfid.py /usr/local/bin/pdfid.py \
    && chmod +x /usr/local/bin/pdfid.py \
    # Composer install
    && composer install --optimize-autoloader --no-dev \
    && chown -R www-data:www-data vendor \
    # Copy dotenv template file
    && mv tools/dotenv.tpl .env \
    # Make Container Entrypoint Executable
    && chmod +x /docker-entrypoint.sh \
    # Make sure www-data has correct uid and gid
    && usermod --uid 1001 www-data \
    && groupmod --gid 1001 www-data \
    # Init crontab for www-data user
    && chmod 644 /etc/cron.d/crontab \
    && crontab -u www-data /etc/cron.d/crontab \
    # Cleaning
    && rm -rf .git .github tools

#USER www-data

EXPOSE 443

STOPSIGNAL SIGQUIT

CMD ["sh", "-c", "/docker-entrypoint.sh"]