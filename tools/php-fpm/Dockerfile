FROM 717177975706.dkr.ecr.us-east-2.amazonaws.com/shuttle-health-api:base AS final

# Set working directory
WORKDIR /var/www

COPY --chown=www-data:www-data . .

RUN composer install --optimize-autoloader --no-dev \
    && chown -R www-data:www-data vendor \
    # Copy nginx/php/supervisor configs
    && cp tools/php-fpm/conf/supervisor/supervisor.conf /etc/supervisord.conf \
    && cp tools/php-fpm/conf/php/php.ini-production /usr/local/etc/php/conf.d/app.ini \
    && cp tools/php-fpm/conf/nginx/nginx.conf /etc/nginx/sites-enabled/default \
    && cp tools/php-fpm/docker-entrypoint.sh /docker-entrypoint.sh \
    && cp tools/dotenv.tpl .env \
    && mkdir -p /etc/nginx/ssl \
    && cp tools/php-fpm/conf/nginx/shuttle.health.crt /etc/nginx/ssl/shuttle.health.crt \
    && cp tools/php-fpm/conf/nginx/shuttle.health.key /etc/nginx/ssl/shuttle.health.key \
    # Make Container Entrypoint Executable
    && chmod +x /var/www/deployment/run.sh \
    && chmod +x /docker-entrypoint.sh \
    # make sure www-data has correct uid and gid
    && usermod --uid 1001 www-data \
    && groupmod --gid 1001 www-data \
    # Cleaning
    && rm -rf .git .github bin iac tools deployment

#USER www-data

EXPOSE 443

STOPSIGNAL SIGQUIT

CMD ["bash", "-c", "/docker-entrypoint.sh"]