FROM nginx:1.26-alpine3.19 AS nginx

COPY tools/php-fpm/conf/nginx/nginx.conf /etc/nginx/nginx.conf
COPY tools/php-fpm/conf/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY tools/php-fpm/conf/nginx/shuttle.health.crt /etc/nginx/ssl/shuttle.health.crt
COPY tools/php-fpm/conf/nginx/shuttle.health.key /etc/nginx/ssl/shuttle.health.key

RUN set -x ; \
  addgroup -g 1001 -S www-data ; \
  adduser -u 1001 -D -S -G www-data www-data && exit 0 ; exit 1

USER www-data

EXPOSE 443

STOPSIGNAL SIGQUIT

CMD ["nginx", "-g", "daemon off;"]