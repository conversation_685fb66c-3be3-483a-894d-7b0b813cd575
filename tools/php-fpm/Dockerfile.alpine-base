FROM php:8.3-fpm-alpine3.21 AS base

RUN set -x && \
    apk update && apk upgrade && \
    apk add --no-cache \
        # for usermod and groupmod
        shadow \
        # for healthchecks
        curl \
        jq \
        # nginx and supervisor
        supervisor \
        nginx \
        # for tesseract and image processing
        imagemagick-dev \
        imagemagick-pdf \
        tesseract-ocr-dev \
    && \
    wget https://github.com/tesseract-ocr/tessdata/raw/4.1.0/eng.traineddata && \
    wget https://github.com/tesseract-ocr/tessdata/raw/4.1.0/deu.traineddata && \
    mv -v *.traineddata /usr/share/tessdata \
    # prepare nginx
    && \
    mkdir /etc/nginx/conf.d && \
    rm -rf /etc/nginx/nginx.conf && \
    rm -rf /etc/nginx/http.d/* && \
    chown -R www-data:www-data /var/lib/nginx \
    && \
    # Install PHP extensions
    curl -sSLf \
        -o /usr/local/bin/install-php-extensions \
        https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions \
    && \
    chmod +x /usr/local/bin/install-php-extensions && \
    install-php-extensions \
        gd \
        redis \
        bcmath \
        exif \
        mbstring \
        pcntl \
        pdo \
        pdo_pgsql \
        pgsql \
        zip \
        opcache \
        soap \
        imagick \
        excimer \
    && \
    curl -sS https://getcomposer.org/installer -o composer-setup.php && \
    php composer-setup.php --install-dir=/usr/local/bin --filename=composer && \
    rm -rf composer-setup.php \
    # Delete APK cache
    && rm -rf /var/cache/apk/*