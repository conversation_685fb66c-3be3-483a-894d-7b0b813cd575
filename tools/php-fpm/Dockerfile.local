FROM php:8.3-fpm-alpine3.21 AS base

RUN set -x && \
    apk update && apk upgrade \
    && apk add --no-cache \
        # for usermod and groupmod
        shadow \
        # for healthchecks
        curl \
        # for xdebug
        linux-headers \
        # bash and supervisor
        supervisor \
        bash \
        # for tesseract and image processing
        imagemagick-dev \
        imagemagick-pdf \
        tesseract-ocr-dev \
        && \
        wget https://github.com/tesseract-ocr/tessdata/raw/4.1.0/eng.traineddata && \
        wget https://github.com/tesseract-ocr/tessdata/raw/4.1.0/deu.traineddata && \
        mv -v *.traineddata /usr/share/tessdata

RUN curl -sSLf -o /usr/local/bin/install-php-extensions \
    https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions \
    && \
    chmod +x /usr/local/bin/install-php-extensions && \
    install-php-extensions \
    xdebug \
    gd \
    redis \
    bcmath \
    exif \
    mbstring \
    pcntl \
    pdo \
    pdo_pgsql \
    pgsql \
    zip \
    opcache \
    soap \
    imagick \
    excimer \
    # Install composer
    && curl -sS https://getcomposer.org/installer -o composer-setup.php \
    && php composer-setup.php --install-dir=/usr/local/bin --filename=composer \
    && rm -rf composer-setup.php \
    #&& pip install --no-cache-dir --no-dependencies -r /tmp/requirements.txt \
    # Delete APK cache
    && rm -rf /var/cache/apk/*

FROM base AS final

# Set working directory
WORKDIR /var/www

COPY conf/local/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY conf/local/php.ini $PHP_INI_DIR/php.ini
COPY conf/local/supervisor.conf /etc/supervisord.conf
COPY conf/local/crontab /etc/cron.d/crontab
COPY pdfid/pdfid.py /usr/local/bin/pdfid.py
COPY pdfid/pdf-parser.py /usr/local/bin/pdf-parser.py


# make sure www-data has correct uid and gid
RUN chmod +x /usr/local/bin/pdfid.py \
    && chmod +x /usr/local/bin/pdf-parser.py \
    && usermod --uid 1001 www-data \
    && groupmod --gid 1001 www-data \
    && chmod 644 /etc/cron.d/crontab \
    && crontab -u www-data /etc/cron.d/crontab \
    && mkdir -p /tmp/client_temp && chown -R www-data:www-data /tmp/client_temp \
    && mkdir -p /tmp/fastcgi_temp && chown -R www-data:www-data /tmp/fastcgi_temp \
    && mkdir -p /tmp/proxy_temp_path && chown -R www-data:www-data /tmp/proxy_temp_path \
    && mkdir -p /tmp/scgi_temp && chown -R www-data:www-data /tmp/scgi_temp \
    && mkdir -p /tmp/uwsgi_temp && chown -R www-data:www-data /tmp/uwsgi_temp \
    && mkdir -p /tmp/pear && chown -R www-data:www-data /tmp/pear \
    && mkdir -p /tmp/php_temp && chown -R www-data:www-data /tmp/php_temp
    
EXPOSE 9000

STOPSIGNAL SIGQUIT

CMD ["/usr/bin/supervisord", "-n", "-c", "/etc/supervisord.conf"]