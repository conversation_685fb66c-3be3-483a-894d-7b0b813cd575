#!/bin/sh

set -euo pipefail

__timestamp(){
  date "+%Y-%m-%dT%H:%M:%S%z"
}

__log(){
  level_name="$1"
  message="$2"
  echo '{}' | \
  jq  --monochrome-output \
      --compact-output \
      --raw-output \
      --arg timestamp "$(__timestamp)" \
      --arg level_name "$level_name" \
      --arg message "$message" \
      '.timestamp=$timestamp|.level_name=$level_name|.message=$message'
}

__log "INFO" "Start docker-entrypoint execution"

# Set ownership to root:root
chown root:root /tmp

# Set permissions to drwxrwxrwt (1777)
chmod 1777 /tmp

mkdir -p /tmp/client_temp && chown -R www-data:www-data /tmp/client_temp
mkdir -p /tmp/fastcgi_temp && chown -R www-data:www-data /tmp/fastcgi_temp
mkdir -p /tmp/proxy_temp_path && chown -R www-data:www-data /tmp/proxy_temp_path
mkdir -p /tmp/scgi_temp && chown -R www-data:www-data /tmp/scgi_temp
mkdir -p /tmp/uwsgi_temp && chown -R www-data:www-data /tmp/uwsgi_temp
mkdir -p /tmp/pear && chown -R www-data:www-data /tmp/pear
mkdir -p /tmp/php_temp && chown -R www-data:www-data /tmp/php_temp

__log "INFO" "Generating ${GMAIL_SERVICE_ACCOUNT_GOOGLE_APPLICATION_CREDENTIALS}"
echo "${GMAIL_SERVICE_ACCOUNT_JSON}" > ${GMAIL_SERVICE_ACCOUNT_GOOGLE_APPLICATION_CREDENTIALS}
chown www-data:www-data ${GMAIL_SERVICE_ACCOUNT_GOOGLE_APPLICATION_CREDENTIALS}

cd /var/www

export CONTAINER_ROLE=${CONTAINER_ROLE:-standalone}

if [ "${CONTAINER_ROLE}" = "standalone" ]; then

    __log "INFO" "Starting application in standalone mode"

    __log "INFO" "Running command: php artisan optimize"
    php artisan optimize
    __log "INFO" "Executed command: php artisan optimize with status $?"

    __log "INFO" "Running command: php artisan migrate"
    php artisan migrate --force
    __log "INFO" "Executed command: php artisan migrate with status $?"

    __log "INFO" "Running command: php artisan horizon:terminate"
    php artisan horizon:terminate
    __log "INFO" "Executed command: php artisan horizon:terminate with status $?"

    __log "INFO" "All preparations done. Starting supervisor."
    /usr/bin/supervisord -n -c /etc/supervisord.conf

    elif [ "${CONTAINER_ROLE}" = "queue" ]; then

        __log "INFO" "Starting application in queue mode"

        __log "INFO" "Running command: php artisan optimize"
        php artisan optimize
        __log "INFO" "Executed command: php artisan optimize with status $?"

        __log "INFO" "Running command: php artisan horizon:terminate"
        php artisan horizon:terminate
        __log "INFO" "Executed command: php artisan horizon:terminate with status $?"

        __log "INFO" "All preparations done. Starting supervisor."
        /usr/bin/supervisord -n -c /etc/supervisord.queue.conf

    elif [ "${CONTAINER_ROLE}" = "patient-communication-queue" ]; then

        __log "INFO" "Starting application in queue mode for patient queue communication. Starting supervisor"
        /usr/bin/supervisord -n -c /etc/supervisord.patient-communication-queue.conf

    elif [ "${CONTAINER_ROLE}" = "scheduler" ]; then

        __log "INFO" "Starting application in scheduler mode"

        __log "INFO" "Running command: php artisan optimize"
        php artisan optimize
        __log "INFO" "Executed command: php artisan optimize with status $?"

        __log "INFO" "All preparations done. Starting supervisor."
        /usr/bin/supervisord -n -c /etc/supervisord.scheduler.conf

    elif [ "${CONTAINER_ROLE}" = "php-fpm" ]; then

        __log "INFO" "Starting application in php-fpm mode"

        __log "INFO" "Running command: php artisan optimize"
        php artisan optimize
        __log "INFO" "Executed command: php artisan optimize with status $?"

        __log "INFO" "Running command: php artisan migrate"
        php artisan migrate --force
        __log "INFO" "Executed command: php artisan migrate with status $?"

        __log "INFO" "All preparations done. Starting supervisor."
        /usr/bin/supervisord -n -c /etc/supervisord.php-fpm.conf

    elif [ "${CONTAINER_ROLE}" = "indexing" ]; then

        __log "INFO" "Starting application in indexing mode"

        tail -f /dev/null

fi