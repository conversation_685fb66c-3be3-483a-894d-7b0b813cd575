<?php

use App\Enums\ActivityLogTypeEnum;
use App\Models\PatientDocument;
use App\Models\Provider;
use App\Models\Scopes\NotArchivedOrderScope;
use App\Services\ShuttleHealth\ActivityLogger;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            PatientDocument::withoutGlobalScope(SoftDeletingScope::class)
                ->with([
                    'order' => function ($query) {
                        $query->withoutGlobalScope(NotArchivedOrderScope::class);
                    },
                    'order.distributor',
                    'order.provider',
                    'documentRequest.distributor',
                    'documentRequest.provider',
                    'createdByOrganization',
                ])
                ->chunkById(500, function (Collection $orderDocuments) {
                    /** @var PatientDocument $orderDocument */
                    foreach ($orderDocuments as $orderDocument) {
                        $createdByOrganization = $orderDocument->createdByOrganization;

                        $distributor = $orderDocument->getDistributor();

                        if ($distributor !== null) {
                            // we always associate the distributor if it exists
                            $orderDocument->associateDistributor($distributor);
                        }

                        if ($createdByOrganization instanceof Provider) {
                            // if document created by provider, we associate it with the provider
                            $orderDocument->associateProvider($createdByOrganization);
                        }
                        // Audit Logging
                        $globalPatient = $orderDocument->getGlobalPatient();

                        if (!$globalPatient) {
                            $patientProfileId = 'N/A';
                            $status = 'error';
                        } else {
                            $patientProfileId = $globalPatient->id;
                        }
                        $orgId = $distributor?->id ?? ($createdByOrganization instanceof Provider ? $createdByOrganization->id : 'N/A');
                        $status = 'success';

                        if ($patientProfileId === 'N/A') {
                            $status = 'error';
                        }

                        ActivityLogger::write(
                            $orderDocument,
                            ActivityLogTypeEnum::MIGRATION,
                            [
                                'process_name' => 'DocumentMigration',
                                'migration_details' => [
                                    'old_document_id' => $orderDocument->id,
                                    'new_document_id' => $orderDocument->id,
                                    'order_id' => $orderDocument->order_id ?? 'N/A',
                                    'patient_profile_id' => $patientProfileId,
                                    'organization_id' => $orgId,
                                    'change_description' =>
                                        "Document having ID {$orderDocument->id} and Order ID {$orderDocument->order_id} migrated to Patient Document Section from Order-specific document. Now Order ID {$orderDocument->order_id} is referencing the document associated with the Patient profile using a foreign key, New Document ID referenced to this patient is {$orderDocument->id}.",
                                    'missing_data_handling' => $patientProfileId === 'N/A' ? "Set patient_profile_id to 'N/A' for document ID {$orderDocument->id}" : null,
                                    'status' => $status,
                                ],
                            ],
                            Carbon::now('UTC'),
                            null,
                        );
                    }
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('distributor_document')->truncate();
        DB::table('provider_document')->truncate();
        DB::table('patient_document')->truncate();
    }
};
