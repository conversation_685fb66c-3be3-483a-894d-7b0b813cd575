<?php

use App\Enums\OrderSourceEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE orders SET source = \'' . OrderSourceEnum::OTHER->value . '\' WHERE source IS NULL');

            Schema::table('orders', function (Blueprint $table) {
                $table->string('source', 30)->default(OrderSourceEnum::OTHER->value)->change();
                $table->string('source_description')->nullable();
                $table->boolean('source_editable')->default(true);
            });

            Schema::table('orders', function (Blueprint $table) {
                $table->boolean('source_editable')->default(false)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['source_description', 'source_editable']);
            $table->string('source', 30)->nullable()->default(null)->change();
        });
    }
};
