<?php

use App\Extensions\Logger;
use App\Models\PatientDocument;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            $documentsToUpdate = PatientDocument::whereNull('global_patient_id')
                ->whereNotNull('order_id')
                ->whereHas('order', function ($query) {
                    $query->whereNotNull('global_patient_id');
                })
                ->with('order:id,global_patient_id')
                ->get();

            $recordsToUpdate = $documentsToUpdate->count();

            if ($recordsToUpdate > 0) {
                Logger::info("Updating {$recordsToUpdate} patient_documents records with global_patient_id from orders table");

                $documentsToUpdate->each(function (PatientDocument $document) {
                    $document->update([
                        'global_patient_id' => $document->order->global_patient_id,
                    ]);
                });

                Logger::info('Successfully updated patient_documents records with global_patient_id');
            } else {
                Logger::warning('No patient_documents records need global_patient_id updates');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Set global_patient_id back to null for records that were updated
        DB::transaction(function () {
            PatientDocument::whereNotNull('global_patient_id')
                ->whereNotNull('order_id')
                ->update(['global_patient_id' => null]);
        });
    }
};
