<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            // Document Type and Processing - uses DocumentRequestTypeEnum values
            $table->enum('document_type', [
                'cmn',
                'chart_notes',
                'custom',
                'lab',
                'unidentified',
            ])->nullable()->after('fax_log_metadata');

            // S3 Storage Information
            $table->string('s3_bucket', 100)->nullable()->after('document_type');
            $table->string('s3_key', 500)->nullable()->after('s3_bucket');

            // AI Processing
            $table->json('ai_extracted_data')->nullable()->after('s3_key');
            $table->text('processing_notes')->nullable()->after('ai_extracted_data');

            // Identification Results
            $table->boolean('identified')->default(false)->after('processing_notes');
            $table->enum('match_method', ['none', 'exact', 'fuzzy'])->default('none')->after('identified');
            $table->decimal('match_score', 5, 3)->nullable()->after('match_method');
            $table->timestamp('matched_at')->nullable()->after('match_score');

            // Entity Associations
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('document_request_id')->nullable()->after('order_id');

            // Human Validation Workflow
            $table->boolean('is_accurate_identification')->nullable()->after('document_request_id');
            $table->text('feedback_notes')->nullable()->after('is_accurate_identification');
            $table->unsignedBigInteger('validated_by_user_id')->nullable()->after('feedback_notes');
            $table->timestamp('validated_at')->nullable()->after('validated_by_user_id');

            // Human Corrections/Overrides
            $table->unsignedBigInteger('corrected_patient_id')->nullable()->after('validated_at');
            $table->unsignedBigInteger('corrected_order_id')->nullable()->after('corrected_patient_id');
            $table->unsignedBigInteger('corrected_document_request_id')->nullable()->after('corrected_order_id');
            $table->boolean('human_override_applied')->default(false)->after('corrected_document_request_id');

            // Audit Trail
            $table->json('audit_ref')->nullable()->after('human_override_applied');
            $table->string('idempotency_key', 255)->nullable()->after('audit_ref');
            $table->string('raw_json_s3_key', 500)->nullable()->after('idempotency_key');
        });

        // Add Foreign Key Constraints
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            $table->foreign('patient_id')->references('id')->on('patients')->onDelete('set null');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
            $table->foreign('document_request_id')->references('id')->on('document_requests')->onDelete('set null');
            $table->foreign('validated_by_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('corrected_patient_id')->references('id')->on('patients')->onDelete('set null');
            $table->foreign('corrected_order_id')->references('id')->on('orders')->onDelete('set null');
            $table->foreign('corrected_document_request_id')->references('id')->on('document_requests')->onDelete('set null');
        });

        // Add Indexes for Performance
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            $table->index(['fax_log_id', 'document_type'], 'idx_fax_log_id_doc_type');
            $table->index(['identified', 'document_type'], 'idx_identified_doc_type');
            $table->index(['patient_id', 'identified'], 'idx_patient_identified');
            $table->index(['order_id', 'document_type'], 'idx_order_doc_type');
            $table->index(['document_request_id', 'identified'], 'idx_doc_req_identified');
            $table->index(['validated_at', 'is_accurate_identification'], 'idx_validation_metrics');
            $table->index(['matched_at', 'match_method'], 'idx_matching_metrics');
            $table->index('idempotency_key', 'idx_idempotency');
            $table->index('s3_key', 'idx_s3_key');
        });
    }

    public function down()
    {
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['patient_id']);
            $table->dropForeign(['order_id']);
            $table->dropForeign(['document_request_id']);
            $table->dropForeign(['validated_by_user_id']);
            $table->dropForeign(['corrected_patient_id']);
            $table->dropForeign(['corrected_order_id']);
            $table->dropForeign(['corrected_document_request_id']);

            // Drop indexes
            $table->dropIndex('idx_fax_log_id_doc_type');
            $table->dropIndex('idx_identified_doc_type');
            $table->dropIndex('idx_patient_identified');
            $table->dropIndex('idx_order_doc_type');
            $table->dropIndex('idx_doc_req_identified');
            $table->dropIndex('idx_validation_metrics');
            $table->dropIndex('idx_matching_metrics');
            $table->dropIndex('idx_idempotency');
            $table->dropIndex('idx_s3_key');

            // Drop all columns
            $table->dropColumn([
                'document_type',
                's3_bucket',
                's3_key',
                'ai_extracted_data',
                'processing_notes',
                'identified',
                'match_method',
                'match_score',
                'matched_at',
                'order_id',
                'document_request_id',
                'is_accurate_identification',
                'feedback_notes',
                'validated_by_user_id',
                'validated_at',
                'corrected_patient_id',
                'corrected_order_id',
                'corrected_document_request_id',
                'human_override_applied',
                'audit_ref',
                'idempotency_key',
                'raw_json_s3_key',
            ]);
        });
    }
};
