<?php

use App\Enums\CustomFieldTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distributor_campaign_id')->constrained('distributor_campaigns')->cascadeOnDelete();
            $table->string('display_name', 255);
            $table->string('system_name', 255);
            $table->enum('field_type', CustomFieldTypeEnum::values());
            $table->timestamps();
            $table->softDeletes();

            // Unique constraints per campaign
            $table->unique(['distributor_campaign_id', 'display_name', 'system_name'], 'custom_fields_campaign_display_and_system_unique');

            // Indexes for performance
            $table->index(['distributor_campaign_id', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_fields');
    }
};
