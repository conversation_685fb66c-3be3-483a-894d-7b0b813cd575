<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('signal_wire_sms_logs', function (Blueprint $table) {
            $table->string('sender_name')->nullable();
            $table->string('receiver_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('signal_wire_sms_logs', function (Blueprint $table) {
            $table->dropColumn('sender_name');
            $table->dropColumn('receiver_name');
        });
    }
};
