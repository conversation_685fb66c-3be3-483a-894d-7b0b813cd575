<?php

use App\Enums\DigitalJourneyLeadStatusEnum;
use App\Enums\DigitalJourneyTemplateTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_journey_leads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lead_id')->after('id');
            $table->unsignedBigInteger('digital_journey_id')->after('lead_id');
            $table->string('digital_journey_template_type')->default(DigitalJourneyTemplateTypeEnum::MESSAGE);
            $table->string('status')->default(DigitalJourneyLeadStatusEnum::PENDING);
            $table->dateTime('send_scheduled_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->foreign('lead_id')->references('id')->on('leads')->cascadeOnDelete();
            $table->foreign('digital_journey_id')->references('id')->on('digital_journeys')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_journey_leads');
    }
};
