<?php

use App\Enums\OrderSourceEnum;
use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Order;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $distributors = Distributor::with(['orders', 'orderSources'])->get();

        foreach ($distributors as $distributor) {
            foreach ($distributor->orders as $order) {
                $orderSource = $order?->source ?? null;

                if (!$orderSource) {
                    continue;
                }
                $sourceEnum = OrderSourceEnum::tryFromValue($orderSource);
                $entityOrderSource = $distributor->orderSources()
                    ->where('order_source_name', $sourceEnum->value)
                    ->first();

                Order::where('id', $order?->id)->update(['order_source_id' => $entityOrderSource->id]); //Update silently without any events
            }
        }

        $manufacturers = Manufacturer::with(['products', 'orderSources'])->get();

        foreach ($manufacturers as $manufacturer) {
            foreach ($manufacturer->products as $product) {
                foreach ($product->orders as $order) {
                    $orderSource = $order?->source ?? null;

                    if (!$orderSource) {
                        continue;
                    }
                    $sourceEnum = OrderSourceEnum::tryFromValue($orderSource);
                    $entityOrderSource = $manufacturer->orderSources()
                        ->where('order_source_name', $sourceEnum->value)
                        ->first();

                    Order::where('id', $order?->id)->update(['order_source_id' => $entityOrderSource->id]); //Update silently without any events
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
