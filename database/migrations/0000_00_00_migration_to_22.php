<?php

use App\Enums\ProductQuantityTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('DELETE FROM migrations WHERE migration like \'2014_%\'');
        DB::statement('DELETE FROM migrations WHERE migration like \'2016_%\'');
        DB::statement('DELETE FROM migrations WHERE migration like \'2019_%\'');
        DB::statement('DELETE FROM migrations WHERE migration like \'2021_%\'');

        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->id();
                $table->string('first_name');
                $table->string('middle_name')->nullable();
                $table->string('last_name');
                $table->string('email')->nullable()->unique();
                $table->string('entity_user_type')->nullable();
                $table->unsignedBigInteger('mobile')->nullable();
                $table->unsignedBigInteger('phone')->nullable();
                $table->unsignedBigInteger('fax')->nullable();
                $table->string('image_url')->nullable();
                $table->string('password')->nullable();
                $table->rememberToken();
                $table->timestamp('last_login_at')->nullable();
                $table->boolean('notifications')->default(true);
                $table->string('position_type')->nullable();
                $table->string('position_description')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });

            Schema::create('password_resets', function (Blueprint $table) {
                $table->string('email')->index();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
            });

            Schema::create('user_invitations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->unique()->constrained()->cascadeOnDelete();
                $table->string('token', 32)->unique();
                $table->dateTime('expires_at')->nullable();
                $table->timestamps();
            });

            Schema::create('failed_jobs', function (Blueprint $table) {
                $table->id();
                $table->string('uuid')->unique();
                $table->text('connection');
                $table->text('queue');
                $table->longText('payload');
                $table->longText('exception');
                $table->timestamp('failed_at')->useCurrent();
            });

            Schema::create('payers', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('type');
                $table->boolean('custom')->default(false);
            });

            Schema::create('payer_plans', function (Blueprint $table) {
                $table->id();
                $table->foreignId('payer_id')->constrained()->cascadeOnDelete();
                $table->string('name');
                $table->string('payer_plan_type');
                $table->char('group_number', 20);
                $table->boolean('rx');
                $table->char('rx_bin', 15);
                $table->char('rx_pcn', 15);
                $table->index('payer_id', 'payer_plans_payer_id_index');
            });

            Schema::create('payer_services', function (Blueprint $table) {
                $table->id();
                $table->foreignId('payer_id')->constrained()->cascadeOnDelete();
                $table->string('payer_service_type');
                $table->char('ch_service_id', 15);
                $table->boolean('accepts_secondary')->nullable();
                $table->boolean('enrollment_required')->nullable();
                $table->string('enrollment_method')->nullable();
                $table->string('enrollment_type')->nullable();
                $table->string('report_level')->nullable();
                $table->string('states')->nullable();
                $table->text('notes')->nullable();

                $table->unique(['payer_service_type', 'ch_service_id']);
                $table->index('payer_id', 'payer_service_payer_id_index');
            });

            Schema::create('payer_plan_financials', function (Blueprint $table) {
                $table->foreignId('payer_plan_id')->nullable()->constrained()->cascadeOnDelete();
                $table->unsignedSmallInteger('deductible');
                $table->unsignedSmallInteger('max_out_of_pocket');
                $table->unsignedSmallInteger('primary_care');
                $table->unsignedSmallInteger('urgent_care');
                $table->unsignedSmallInteger('emergency_room');
                $table->unsignedSmallInteger('specialist');
                $table->unsignedSmallInteger('teladoc');
            });

            Schema::create('manufacturers', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('phone')->nullable();
                $table->unsignedBigInteger('fax')->nullable();
                $table->string('image_url')->nullable();
                $table->timestamps();
            });

            Schema::create('manufacturer_user', function (Blueprint $table) {
                $table->foreignId('manufacturer_id')->constrained()->cascadeOnDelete();
                $table->foreignId('user_id')->constrained()->cascadeOnDelete();
                $table->boolean('owner')->default(false);
                $table->index('manufacturer_id', 'manufacturer_user_manufacturer_id_index');
                $table->index('user_id', 'manufacturer_user_user_id_index');
            });

            Schema::create('providers', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('phone')->nullable();
                $table->unsignedBigInteger('fax')->nullable();
                $table->string('image_url')->nullable();
                $table->timestamps();
            });

            Schema::create('distributors', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('phone')->nullable();
                $table->unsignedBigInteger('fax')->nullable();
                $table->string('image_url')->nullable();
                $table->unsignedBigInteger('assigned_fax')->nullable()->unique();
                $table->string('bt_user')->nullable();
                $table->string('bt_pass')->nullable();
                $table->timestamps();
            });

            Schema::create('patients', function (Blueprint $table) {
                $table->id();
                $table->string('first_name');
                $table->string('middle_name')->nullable();
                $table->string('last_name');
                $table->enum('gender', ['M', 'F']);
                $table->date('date_of_birth');
                $table->string('email')->nullable();
                $table->unsignedBigInteger('mobile')->nullable();
                $table->boolean('mobile_communications')->default(false);
                $table->string('timezone')->nullable();
                $table->timestamps();
            });

            Schema::create('provider_user', function (Blueprint $table) {
                $table->foreignId('provider_id')->constrained()->cascadeOnDelete();
                $table->foreignId('user_id')->constrained()->cascadeOnDelete();
                $table->boolean('owner')->default(false);

                $table->index('provider_id', 'provider_user_provider_id_index');
                $table->index('user_id', 'provider_user_user_id_index');
            });

            Schema::create('distributor_user', function (Blueprint $table) {
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
                $table->foreignId('user_id')->constrained()->cascadeOnDelete();
                $table->boolean('owner')->default(false);

                $table->index('distributor_id', 'distributor_user_distributor_id_index');
                $table->index('user_id', 'distributor_user_user_id_index');
            });

            Schema::create('npi_records', function (Blueprint $table) {
                $table->id();
                $table->morphs('npiable');
                $table->bigInteger('npi');
                $table->timestamp('attested_on')->nullable();
                $table->timestamp('verified_on')->nullable();
                $table->json('basic')->nullable();
                $table->json('other_names')->nullable();
                $table->json('addresses')->nullable();
                $table->json('taxonomies')->nullable();
                $table->json('identifiers')->nullable();
                $table->json('endpoints')->nullable();
            });

            Schema::create('facilities', function (Blueprint $table) {
                $table->id();
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
                $table->foreignId('user_id');
                $table->string('name')->nullable();
                $table->unsignedBigInteger('phone')->nullable();
                $table->unsignedBigInteger('fax')->nullable();
                $table->string('address_line_1');
                $table->string('address_line_2')->nullable();
                $table->string('city');
                $table->char('state', 2);
                $table->string('zip');
                $table->timestamps();
            });

            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->string('type');
                $table->string('status');
                $table->foreignId('patient_id')->constrained();
                $table->foreignId('provider_id')->nullable()->constrained();
                $table->foreignId('provider_user_id')->nullable()->constrained('users');
                $table->string('provider_order_id')->nullable();
                $table->foreignId('distributor_id')->nullable()->constrained();
                $table->foreignId('distributor_user_id')->nullable()->constrained('users');
                $table->string('distributor_order_id')->nullable();
                $table->date('due_on')->nullable();
                $table->foreignId('created_by')->constrained('users');
                $table->date('follow_up_at')->nullable();
                $table->integer('external_order_id')->nullable();
                $table->foreignId('facility_id')->nullable()->constrained();
                $table->timestamps();

                $table->index(['patient_id', 'type', 'status'], 'order_patient_id_type_status_index');
                $table->index(['provider_id', 'type', 'status'], 'order_provider_id_type_status_index');
                $table->index(['provider_id', 'provider_user_id'], 'order_provider_id_provider_user_id_index');
                $table->index(['distributor_id', 'type', 'status'], 'order_distributor_id_type_status_index');
                $table->index([
                    'distributor_id',
                    'distributor_user_id',
                ], 'order_distributor_id_distributor_user_id_index');
                $table->index('follow_up_at', 'orders_follow_up_at_index');
            });

            Schema::create('cms_hcpcs', function (Blueprint $table) {
                $table->id();
                $table->char('code', 6);
                $table->char('seq_num', 4);
                $table->unsignedTinyInteger('rec_id');
                $table->string('short_description');
                $table->text('long_description');
                $table->boolean('is_same_and_similar_eligible')->default(false);

                $table->unique(['code', 'seq_num']);
                $table->index(['code', 'seq_num']);
            });

            Schema::create('cms_articles', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('cms_id');
                $table->unsignedSmallInteger('cms_version');
                $table->unsignedSmallInteger('type');
                $table->string('title');
                $table->text('description')->nullable();
                $table->string('status')->nullable();
                $table->timestamp('publish_date')->nullable();
                $table->timestamp('effective_start_date')->nullable();
                $table->timestamp('effective_end_date')->nullable();
                $table->timestamp('update_date')->nullable();

                $table->unique(['cms_id', 'cms_version']);
                $table->index(['cms_id', 'cms_version']);
            });

            Schema::create('cms_lcds', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('cms_id');
                $table->unsignedSmallInteger('cms_version');
                $table->string('title');

                $table->unique(['cms_id', 'cms_version']);
                $table->index(['cms_id', 'cms_version']);
            });

            Schema::create('cms_lcd_articles', function (Blueprint $table) {
                $table->foreignId('cms_lcd_id');
                $table->foreignId('cms_article_id');
                $table->timestamp('update_date')->nullable();

                $table->index('cms_lcd_id', 'cms_lcd_articles_cms_lcd_id_index');
                $table->index('cms_article_id', 'cms_lcd_articles_cms_article_id_index');
            });

            Schema::create('cms_lcd_hcpcs', function (Blueprint $table) {
                $table->foreignId('cms_lcd_id');
                $table->foreignId('cms_hcpc_id');

                $table->index('cms_lcd_id', 'cms_lcd_hcpcs_cms_lcd_id_index');
                $table->index('cms_hcpc_id', 'cms_lcd_hcpcs_cms_hcpc_id_index');
            });

            Schema::create('icd_10s', function (Blueprint $table) {
                $table->id();
                $table->char('code', 8);
                $table->string('short_description');
                $table->string('long_description', 400);

                $table->unique('code');
                $table->index('code');
            });

            Schema::create('cms_article_icd_10', function (Blueprint $table) {
                $table->foreignId('cms_article_id');
                $table->foreignId('icd_10_id');

                $table->index('cms_article_id', 'cms_article_icd_10_cms_article_id_index');
                $table->index('icd_10_id', 'cms_article_icd_10_icd_10_id_index');
            });

            Schema::create('products', function (Blueprint $table) {
                $table->id();
                $table->foreignId('manufacturer_id')->nullable()->constrained();
                $table->foreignId('cms_hcpc_id')->nullable();
                $table->unsignedBigInteger('commercial_hcpc_id')->nullable();
                $table->foreign('commercial_hcpc_id')->references('id')->on('cms_hcpcs');
                $table->string('external_id')->nullable()->unique();
                $table->string('name')->unique();
                $table->longText('description')->nullable();
                $table->string('sku', 40)->nullable();
                $table->string('upi', 40)->nullable();
                $table->decimal('default_measure_count', 12, 6)->unsigned()->default(0);
                $table->string('default_measure_unit')->nullable();
                $table->unsignedInteger('default_duration_count')->default(0);
                $table->string('default_duration_unit')->nullable();
                $table->string('image_url')->nullable();
                $table->string('quantity_type')->nullable()->default(ProductQuantityTypeEnum::STANDARD->value);
                $table->string('default_narrative_unit')->nullable();
                $table->string('default_narrative_measure_unit')->nullable();
                $table->decimal('default_narrative_measure_count', 12, 6)->unsigned()->default(0);
                $table->boolean('inclusion_narrative')->default(false);
                $table->text('narrative_text')->nullable();
                $table->timestamps();
                $table->softDeletes();

                $table->index('manufacturer_id', 'product_id_manufacturer_id_index');
            });

            Schema::create('order_product', function (Blueprint $table) {
                $table->foreignId('order_id')->constrained()->cascadeOnDelete();
                $table->foreignId('product_id')->constrained()->cascadeOnDelete();
                $table->decimal('measure_count', 12, 6)->unsigned()->default(0);
                $table->string('measure_unit')->nullable();
                $table->unsignedInteger('duration_count')->default(0);
                $table->string('duration_unit')->nullable();
                $table->string('narrative_unit')->nullable();
                $table->string('narrative_measure_unit')->nullable();
                $table->decimal('narrative_measure_count', 12, 6)->unsigned()->default(0);
                $table->string('serial_number')->nullable();
                $table->timestamps();

                $table->index('order_id', 'order_product_order_id_index');
                $table->index('product_id', 'order_product_product_id_index');
            });

            Schema::create('distributor_product', function (Blueprint $table) {
                $table->foreignId('distributor_id')->constrained();
                $table->foreignId('product_id')->constrained();

                $table->index('distributor_id', 'distributor_product_distributor_id_index');
                $table->index('product_id', 'distributor_product_product_id_index');
            });

            $this->rolesAndPermissionUp();
        }
    }

    public function down()
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_resets');
        Schema::dropIfExists('user_invitations');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('payers');
        Schema::dropIfExists('payer_plans');
        Schema::dropIfExists('payer_services');
        Schema::dropIfExists('payer_plan_financials');
        Schema::dropIfExists('manufacturers');
        Schema::dropIfExists('manufacturer_user');
        Schema::dropIfExists('providers');
        Schema::dropIfExists('distributors');
        Schema::dropIfExists('patients');
        Schema::dropIfExists('provider_user');
        Schema::dropIfExists('distributor_user');
        Schema::dropIfExists('npi_records');
        Schema::dropIfExists('facilities');
        Schema::dropIfExists('orders');
        Schema::dropIfExists('cms_hcpcs');
        Schema::dropIfExists('cms_articles');
        Schema::dropIfExists('cms_lcds');
        Schema::dropIfExists('cms_lcd_articles');
        Schema::dropIfExists('cms_lcd_hcpcs');
        Schema::dropIfExists('icd_10s');
        Schema::dropIfExists('cms_article_icd_10');
        Schema::dropIfExists('products');
        Schema::dropIfExists('order_product');
        Schema::dropIfExists('distributor_product');
        $this->rolesAndPermissionDown();
    }

    private function rolesAndPermissionUp(): void
    {
        $tableNames = config('permission.table_names');
        $columnNames = config('permission.column_names');
        $teams = config('permission.teams');
        $pivotRole = $columnNames['role_pivot_key'] ?? 'role_id';
        $pivotPermission = $columnNames['permission_pivot_key'] ?? 'permission_id';

        if (empty($tableNames)) {
            throw new Exception('Error: config/permission.php not loaded. Run [php artisan config:clear] and try again.');
        }

        if ($teams && empty($columnNames['team_foreign_key'] ?? null)) {
            throw new Exception('Error: team_foreign_key on config/permission.php not loaded. Run [php artisan config:clear] and try again.');
        }

        Schema::create($tableNames['permissions'], function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');       // For MySQL 8.0 use string('name', 125);
            $table->string('guard_name'); // For MySQL 8.0 use string('guard_name', 125);
            $table->timestamps();

            $table->unique(['name', 'guard_name']);
        });

        Schema::create($tableNames['roles'], function (Blueprint $table) use ($teams, $columnNames) {
            $table->bigIncrements('id');

            if ($teams || config('permission.testing')) { // permission.testing is a fix for sqlite testing
                $table->unsignedBigInteger($columnNames['team_foreign_key'])->nullable();
                $table->index($columnNames['team_foreign_key'], 'roles_team_foreign_key_index');
            }
            $table->string('name');       // For MySQL 8.0 use string('name', 125);
            $table->string('title'); // For MySQL 8.0 use string('title', 125);
            $table->string('guard_name'); // For MySQL 8.0 use string('guard_name', 125);
            $table->timestamps();

            if ($teams || config('permission.testing')) {
                $table->unique([$columnNames['team_foreign_key'], 'name', 'guard_name']);
            } else {
                $table->unique(['name', 'guard_name']);
            }
        });

        Schema::create($tableNames['model_has_permissions'], function (Blueprint $table) use ($tableNames, $columnNames, $teams, $pivotPermission) {
            $table->unsignedBigInteger($pivotPermission);

            $table->string('model_type');
            $table->unsignedBigInteger($columnNames['model_morph_key']);
            $table->index([$columnNames['model_morph_key'], 'model_type'], 'model_has_permissions_model_id_model_type_index');

            $table->foreign($pivotPermission)
                ->references('id')
                ->on($tableNames['permissions'])
                ->onDelete('cascade');

            if ($teams) {
                $table->unsignedBigInteger($columnNames['team_foreign_key']);
                $table->index($columnNames['team_foreign_key'], 'model_has_permissions_team_foreign_key_index');

                $table->primary(
                    [$columnNames['team_foreign_key'], $pivotPermission, $columnNames['model_morph_key'], 'model_type'],
                    'model_has_permissions_permission_model_type_primary',
                );
            } else {
                $table->primary(
                    [$pivotPermission, $columnNames['model_morph_key'], 'model_type'],
                    'model_has_permissions_permission_model_type_primary',
                );
            }
        });

        Schema::create($tableNames['model_has_roles'], function (Blueprint $table) use ($tableNames, $columnNames, $teams, $pivotRole) {
            $table->unsignedBigInteger($pivotRole);

            $table->string('model_type');
            $table->unsignedBigInteger($columnNames['model_morph_key']);
            $table->index([$columnNames['model_morph_key'], 'model_type'], 'model_has_roles_model_id_model_type_index');

            $table->foreign($pivotRole)
                ->references('id')
                ->on($tableNames['roles'])
                ->onDelete('cascade');

            if ($teams) {
                $table->unsignedBigInteger($columnNames['team_foreign_key']);
                $table->index($columnNames['team_foreign_key'], 'model_has_roles_team_foreign_key_index');

                $table->primary(
                    [$columnNames['team_foreign_key'], $pivotRole, $columnNames['model_morph_key'], 'model_type'],
                    'model_has_roles_role_model_type_primary',
                );
            } else {
                $table->primary(
                    [$pivotRole, $columnNames['model_morph_key'], 'model_type'],
                    'model_has_roles_role_model_type_primary',
                );
            }

            $table->foreignId('manufacturer_id')->nullable();
        });

        Schema::create($tableNames['role_has_permissions'], function (Blueprint $table) use ($tableNames, $pivotRole, $pivotPermission) {
            $table->unsignedBigInteger($pivotPermission);
            $table->unsignedBigInteger($pivotRole);

            $table->foreign($pivotPermission)
                ->references('id')
                ->on($tableNames['permissions'])
                ->onDelete('cascade');

            $table->foreign($pivotRole)
                ->references('id')
                ->on($tableNames['roles'])
                ->onDelete('cascade');

            $table->primary([$pivotPermission, $pivotRole], 'role_has_permissions_permission_id_role_id_primary');
        });

        app('cache')
            ->store(config('permission.cache.store') != 'default' ? config('permission.cache.store') : null)
            ->forget(config('permission.cache.key'));
    }

    private function rolesAndPermissionDown(): void
    {
        $tableNames = config('permission.table_names');

        if (empty($tableNames)) {
            throw new Exception('Error: config/permission.php not found and defaults could not be merged. Please publish the package configuration before proceeding, or drop the tables manually.');
        }

        Schema::drop($tableNames['role_has_permissions']);
        Schema::drop($tableNames['model_has_roles']);
        Schema::drop($tableNames['model_has_permissions']);
        Schema::drop($tableNames['roles']);
        Schema::drop($tableNames['permissions']);
    }
};
