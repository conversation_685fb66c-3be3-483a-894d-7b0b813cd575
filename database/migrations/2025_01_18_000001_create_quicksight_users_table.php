<?php

use App\Enums\QuicksightEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateQuicksightUsersTable extends Migration
{
    public function up(): void
    {
        Schema::create('quicksight_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distributor_id')->constrained('distributors')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('set null');
            $table->string('arn');
            $table->string('username');
            $table->enum('role', QuicksightEnum::values());
            $table->jsonb('metadata')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Create composite unique index
            $table->unique(['distributor_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quicksight_users');
    }
}
