<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        $permissions = [
            'can view orders',
            'can manage orders',
            'can create orders',
            'can assign orders',
            'can sign orders',
            'can invite users',
            'can manage API settings',
            'can manage permissions',
            'can chat',
        ];

        $permissionIds = DB::table('permissions')
            ->whereIn('name', $permissions)
            ->pluck('id');

        DB::table('role_permission')->whereIn('permission_id', $permissionIds)->delete();
        DB::table('permissions')->whereIn('name', $permissions)->delete();
    }

    public function down(): void
    {
    }
};
