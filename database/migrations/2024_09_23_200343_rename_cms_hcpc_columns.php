<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_hcpcs', function (Blueprint $table) {
            $table->renameColumn('quantity', 'default_measure_count');
            $table->decimal('default_measure_count', 12, 6)->unsigned()->nullable()->change();
            $table->renameColumn('quantity_units', 'default_measure_unit');
            $table->renameColumn('length_of_need', 'default_duration_count');
            $table->renameColumn('length_of_need_units', 'default_duration_unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_hcpcs', function (Blueprint $table) {
            $table->renameColumn('default_measure_count', 'quantity');
            $table->smallInteger('quantity')->nullable()->change();
            $table->renameColumn('default_measure_unit', 'quantity_units');
            $table->renameColumn('default_duration_count', 'length_of_need');
            $table->renameColumn('default_duration_unit', 'length_of_need_units');
        });
    }
};
