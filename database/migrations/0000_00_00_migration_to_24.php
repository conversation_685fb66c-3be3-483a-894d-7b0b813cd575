<?php

use App\Enums\FaxReviewStatusEnum;
use App\Models\Patient;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('DELETE FROM migrations WHERE migration like \'2023_%\'');

        if (!Schema::hasTable('user_signatures')) {
            Schema::create('user_signatures', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->unique()->constrained()->cascadeOnDelete();
                $table->string('full_name')->nullable();
                $table->timestamps();

                $table->index('user_id', 'user_signatures_user_id_index');
            });

            Schema::create('order_envelopes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id');
                $table->foreignId('signature_id');
                $table->string('ip_address');
                $table->timestamps();

                $table->index('order_id', 'order_envelopes_order_id_index');
                $table->index('signature_id', 'order_envelopes_signature_id_index');
            });

            Schema::create('order_cancellations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->constrained();
                $table->string('reason');
                $table->text('additional_info')->nullable();
                $table->timestamps();

                $table->index('order_id', 'order_cancellations_order_id_index');
            });

            Schema::create('order_shippings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->unique()->constrained();
                $table->text('tracking_number')->nullable();
                $table->text('additional_info')->nullable();
                $table->date('shipped_at')->nullable();
                $table->timestamps();
            });

            Schema::create('cms_hcpc_medical_policy_form', function (Blueprint $table) {
                $table->foreignId('cms_hcpc_id')->constrained()->cascadeOnDelete();
                $table->foreignId('medical_policy_form_id')->constrained()->cascadeOnDelete();
                $table->string('order_type');
                $table->string('payer_type');

                $table->unique(['cms_hcpc_id', 'order_type', 'payer_type'], 'cms_hcpc_medical_policy_form_unique');
                $table->index(['cms_hcpc_id', 'order_type', 'payer_type'], 'cms_hcpc_medical_policy_form_condition_index');
            });

            Schema::create('order_snapshots', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->unique()->constrained()->cascadeOnDelete();
                $table->json('patient');
                $table->foreignId('eligibility_check_id')->nullable()->constrained('patient_payer_eligibility_checks')->cascadeOnDelete();
                $table->timestamps();

                $table->index('order_id', 'order_snapshots_order_id_index');
            });

            Schema::create('patient_caregivers', function (Blueprint $table) {
                $table->id();
                $table->foreignIdFor(Patient::class);
                $table->string('name')->nullable();
                $table->string('relationship')->nullable();
                $table->string('mobile')->nullable();
                $table->string('email')->nullable();
                $table->timestamps();
            });

            Schema::create('order_tasks', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->constrained();
                $table->foreignId('user_id')->constrained();
                $table->string('type');
                $table->string('status');
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->index('order_id', 'order_tasks_order_id_index');
            });

            Schema::create('product_ancillary_product', function (Blueprint $table) {
                $table->foreignId('product_id')->constrained();
                $table->foreignId('ancillary_product_id')->constrained('products');

                $table->index('product_id', 'product_ancillary_product_product_id');
                $table->index('ancillary_product_id', 'product_ancillary_product_ancillary_product_id');
            });

            Schema::create('files', function (Blueprint $table) {
                $table->id();
                $table->uuid();
                $table->string('type');
                $table->string('file_name');
                $table->foreignId('relation_id')->nullable();
                $table->string('extension')->nullable();
                $table->timestamps();

                $table->unique('uuid', 'files_uuid_unique');
                $table->index(['type', 'relation_id'], 'files_type_relation_id_index');
            });

            Schema::create('faxes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('distributor_id')->nullable(true)->constrained('distributors')->cascadeOnDelete();
                $table->string('recognized_id')->nullable(true);
                $table->uuid('fax_uuid')->nullable(true);
                $table->string('review_status')->default(FaxReviewStatusEnum::NEW->value);
                $table->string('fax_status');
                $table->unsignedBigInteger('from')->nullable(true);
                $table->unsignedBigInteger('to');
                $table->string('direction');
                $table->unsignedInteger('pages_count')->nullable(true);
                $table->json('provider_metadata')->nullable(true);
                $table->timestamps();

                $table->index([
                    'distributor_id',
                    'direction',
                    'review_status',
                ], 'faxes_distributor_id_direction_review_index');
                $table->unique('fax_uuid', 'faxes_fax_uuid_unique');
            });

            Schema::create('order_document_requests', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->constrained();
                $table->string('type');
                $table->string('status');
                $table->boolean('paused')->default(false);
                $table->date('date_needed')->nullable();
                $table->json('details')->nullable();
                $table->dateTime('last_requested_at')->nullable();
                $table->foreignId('fax_id')->nullable(true)->constrained('faxes')->nullOnDelete();
                $table->foreignId('fax_file_id')->nullable();
                $table->string('reason')->nullable();
                $table->timestamps();

                $table->index('order_id', 'order_document_requests_order_id_index');
            });

            Schema::create('order_documents', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('type');
                $table->foreignId('order_id')->constrained();
                $table->foreignId('relation_id')->nullable();
                $table->foreignId('order_document_request_id')->nullable()->constrained('order_document_requests')->nullOnDelete();
                $table->foreignId('uploaded_by')->nullable()->constrained('users')->nullOnDelete();
                $table->json('details')->nullable();
                $table->date('signature_date')->nullable();
                $table->boolean('ltp')->default(false); // life time prescription
                $table->timestamps();

                $table->index('order_id', 'order_documents_order_id_index');
                $table->index(['type', 'relation_id'], 'order_documents_type_relation_id_index');
            });

            Schema::create('notifications', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->string('type');
                $table->morphs('notifiable');
                $table->text('data');
                $table->timestamp('read_at')->nullable();
                $table->timestamps();
            });

            Schema::create('distributor_provider_user', function (Blueprint $table) {
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
                $table->foreignId('user_id')->constrained()->cascadeOnDelete();
                $table->unsignedBigInteger('fax')->nullable();
                $table->unsignedBigInteger('phone')->nullable();
                $table->unique(['distributor_id', 'user_id']);
            });

            Schema::create('distributor_provider', function (Blueprint $table) {
                $table->id();
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
                $table->foreignId('provider_id')->constrained()->cascadeOnDelete();
            });

            Schema::create('reports', function (Blueprint $table) {
                $table->id();
                $table->morphs('reportable');
                $table->string('type');
                $table->json('data')->nullable();
                $table->timestamp('created_at')->useCurrent();
                $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

                $table->unique(['reportable_type', 'reportable_id', 'type']);
            });

            Schema::create('campaigns', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->foreignId('manufacturer_id')->unique()->constrained();
                $table->timestamp('created_at')->useCurrent();
                $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();
            });

            Schema::create('campaign_distributor', function (Blueprint $table) {
                $table->foreignId('campaign_id')->constrained()->cascadeOnDelete();
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
            });

            Schema::create('regions', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('manager_id')->nullable();
                $table->timestamps();
            });

            Schema::create('districts', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('manager_id')->nullable();
                $table->unsignedBigInteger('region_id');
                $table->timestamps();
            });

            Schema::create('territories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->unsignedBigInteger('manager_id')->nullable();
                $table->unsignedBigInteger('district_id');
                $table->timestamps();
            });

            Schema::create('zip_codes', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('area_id');
                $table->string('area_type');
                $table->string('zip_code');
                $table->timestamps();
            });

            $this->passportUp();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_signatures');
        Schema::dropIfExists('order_envelopes');
        Schema::dropIfExists('order_cancellations');
        Schema::dropIfExists('order_shippings');
        Schema::dropIfExists('cms_hcpc_medical_policy_form');
        Schema::dropIfExists('order_snapshots');
        Schema::dropIfExists('patient_caregivers');
        Schema::dropIfExists('order_tasks');
        Schema::dropIfExists('product_ancillary_product');
        Schema::dropIfExists('files');
        Schema::dropIfExists('faxes');
        Schema::dropIfExists('order_document_requests');
        Schema::dropIfExists('order_documents');
        Schema::dropIfExists('reports');
        Schema::dropIfExists('campaign_distributor');
        Schema::dropIfExists('campaigns');
        Schema::dropIfExists('regions');
        Schema::dropIfExists('districts');
        Schema::dropIfExists('territories');
        Schema::dropIfExists('zip_codes');
        $this->passportDown();
    }

    private function passportUp(): void
    {
        Schema::create('oauth_auth_codes', function (Blueprint $table) {
            $table->string('id', 100)->primary();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('client_id');
            $table->text('scopes')->nullable();
            $table->boolean('revoked');
            $table->dateTime('expires_at')->nullable();
        });

        Schema::create('oauth_access_tokens', function (Blueprint $table) {
            $table->string('id', 100)->primary();
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->unsignedBigInteger('client_id');
            $table->string('name')->nullable();
            $table->text('scopes')->nullable();
            $table->boolean('revoked');
            $table->timestamps();
            $table->dateTime('expires_at')->nullable();
        });

        Schema::create('oauth_refresh_tokens', function (Blueprint $table) {
            $table->string('id', 100)->primary();
            $table->string('access_token_id', 100)->index();
            $table->boolean('revoked');
            $table->dateTime('expires_at')->nullable();
        });

        Schema::create('oauth_clients', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('name');
            $table->string('secret', 100)->nullable();
            $table->string('provider')->nullable();
            $table->text('redirect');
            $table->boolean('personal_access_client');
            $table->boolean('password_client');
            $table->boolean('revoked');
            $table->timestamps();
        });

        Schema::create('oauth_personal_access_clients', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('client_id');
            $table->timestamps();
        });
    }

    private function passportDown(): void
    {
        Schema::dropIfExists('oauth_auth_codes');
        Schema::dropIfExists('oauth_access_tokens');
        Schema::dropIfExists('oauth_refresh_tokens');
        Schema::dropIfExists('oauth_clients');
        Schema::dropIfExists('oauth_personal_access_clients');
    }
};
