<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('order_documents', function (Blueprint $table) {
            $table->unsignedBigInteger('global_patient_id')->nullable()->after('id');
            $table->foreign('global_patient_id')->references('id')->on('global_patients')->nullOnDelete();
        });

        Schema::rename('order_documents', 'patient_documents');
    }

    public function down(): void
    {
        // First, rename back so we can drop constraints on the original name
        Schema::rename('patient_documents', 'order_documents');

        Schema::table('order_documents', function (Blueprint $table) {
            $table->dropForeign(['global_patient_id']);
            $table->dropColumn('global_patient_id');
        });
    }
};
