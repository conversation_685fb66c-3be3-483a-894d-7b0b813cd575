<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('payer_aliases');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('payer_aliases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payer_id');
            $table->string('alias');

            $table->unique('alias');
        });
    }
};
