<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('providers', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('distributors', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('product_categories', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
        Schema::table('product_groups', function (Blueprint $table) {
            $table->dropColumn('image_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('providers', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('distributors', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('products', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('product_categories', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
        Schema::table('product_groups', function (Blueprint $table) {
            $table->string('image_url')->nullable();
        });
    }
};
