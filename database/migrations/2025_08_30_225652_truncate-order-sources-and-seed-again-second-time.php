<?php

use App\Enums\OrderSourceEnum;
use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Order;
use App\Models\OrderSource;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!App::isProduction()) {
            OrderSource::truncate();
            Order::query()->update(['order_source_id' => null]);
            $distributors = Distributor::orderBy('id', 'desc')->get();

            foreach ($distributors as $distributor) {
                $defaultOrderSources = OrderSourceEnum::systemOrderSources();

                foreach ($defaultOrderSources as $defaultOrderSource) {
                    $distributor->orderSources()->updateOrCreate(
                        [
                            'order_source_slug' => Str::slug($defaultOrderSource->value), // Fixed: use slug for slug field
                            'organization_id' => $distributor->id,
                            'organization_type' => get_class($distributor),
                        ],
                        [
                            'order_source_name' => $defaultOrderSource->value,
                            'order_source_slug' => Str::slug($defaultOrderSource->value),
                            'is_system' => true,
                            'is_active' => true,
                        ],
                    );
                }
            }

            $manufacturers = Manufacturer::orderBy('id', 'desc')->get();

            foreach ($manufacturers as $manufacturer) {
                $defaultOrderSources = OrderSourceEnum::systemOrderSources();

                foreach ($defaultOrderSources as $defaultOrderSource) {
                    $manufacturer->orderSources()->updateOrCreate(
                        [
                            'order_source_slug' => Str::slug($defaultOrderSource->value), // Fixed: use slug for slug field
                            'organization_id' => $manufacturer->id,
                            'organization_type' => get_class($manufacturer),
                        ],
                        [
                            'order_source_name' => $defaultOrderSource->value,
                            'order_source_slug' => Str::slug($defaultOrderSource->value),
                            'is_system' => true,
                            'is_active' => true,
                        ],
                    );
                }
            }

            // Bulk update distributor orders
            $distributors = Distributor::with(['orders', 'orderSources'])->get();

            foreach ($distributors as $distributor) {
                // Group orders by source for bulk updates
                $ordersBySource = $distributor->orders;

                foreach ($ordersBySource as $sourceValue => $orders) {
                    $sourceEnum = OrderSourceEnum::tryFromValue($sourceValue);

                    if (!$sourceEnum) {
                        continue; // Skip if enum not found
                    }

                    $entityOrderSource = $distributor->orderSources()
                        ->where('order_source_name', $sourceEnum->value)
                        ->first();

                    if (!$entityOrderSource) {
                        continue; // Skip if order source entity not found
                    }

                    // Bulk update all orders with this source
                    $orderIds = $orders->pluck('id');
                    Order::whereIn('id', $orderIds)->update(['order_source_id' => $entityOrderSource->id]);
                }
            }

            // Bulk update manufacturer orders
            $manufacturers = Manufacturer::with(['products.orders', 'orderSources'])->get();

            foreach ($manufacturers as $manufacturer) {
                // Collect all orders from all products
                $allOrders = collect();

                foreach ($manufacturer->products as $product) {
                    $allOrders = $allOrders->merge($product->orders);
                }

                // Group orders by source for bulk updates
                $ordersBySource = $allOrders;

                foreach ($ordersBySource as $sourceValue => $orders) {
                    $sourceEnum = OrderSourceEnum::tryFromValue($sourceValue);

                    if (!$sourceEnum) {
                        continue; // Skip if enum not found
                    }

                    $entityOrderSource = $manufacturer->orderSources()
                        ->where('order_source_name', $sourceEnum->value)
                        ->first();

                    if (!$entityOrderSource) {
                        continue; // Skip if order source entity not found
                    }

                    // Bulk update all orders with this source
                    $orderIds = $orders->pluck('id');
                    Order::whereIn('id', $orderIds)->whereNull('order_source_id')->update(['order_source_id' => $entityOrderSource->id]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
