<?php

use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Extensions\Logger;
use App\Models\Distributor;
use App\Models\Provider;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            if (!Schema::hasColumn('order_documents', 'created_by_organization_id')) {
                Schema::table('order_documents', function (Blueprint $table) {
                    $table->nullableMorphs('created_by_organization');
                });
            }

            DB::table('order_documents')
                ->orderBy('id')
                ->chunkById(500, function ($orderDocuments) {
                    foreach ($orderDocuments as $orderDocument) {
                        try {
                            $orgId = null;
                            $orgType = null;
                            $needsProvider = (
                                $orderDocument->source === OrderDocumentSourceEnum::MPF
                                || $orderDocument->source === OrderDocumentSourceEnum::PROVIDER_USER
                                || (
                                    $orderDocument->source === OrderDocumentSourceEnum::SYSTEM
                                    && in_array($orderDocument->type, [
                                        OrderDocumentTypeEnum::COSC,
                                        OrderDocumentTypeEnum::CMN,
                                    ], true)
                                )
                            );

                            $order = DB::table('orders')->where('id', $orderDocument->order_id)->first();

                            if ($needsProvider) {

                                if ($order) {
                                    if ($order->provider_id) {
                                        $orgId = $order->provider_id;
                                        $orgType = Provider::class;
                                    } else {
                                        if ($order->created_by_organization_type === 'App\Models\Provider') {
                                            $orgId = $order->created_by_organization_id;
                                            $orgType = Provider::class;
                                        }
                                    }
                                }

                                if (!$orgId && $orderDocument->document_request_id) {
                                    $docReq = DB::table('document_requests')->where('id', $orderDocument->document_request_id)->first();

                                    if ($docReq) {
                                        if ($docReq->provider_id) {
                                            $orgId = $docReq->provider_id;
                                            $orgType = Provider::class;
                                        } elseif ($docReq->created_by_organization_type === 'App\Models\Provider') {
                                            $orgId = $docReq->created_by_organization_id;
                                            $orgType = Provider::class;
                                        }
                                    }
                                }
                            } else {

                                if ($order && $order->distributor_id) {
                                    $orgId = $order->distributor_id;
                                    $orgType = Distributor::class;
                                } elseif ($orderDocument->document_request_id) {
                                    $docReq = DB::table('document_requests')->where('id', $orderDocument->document_request_id)->first();

                                    if ($docReq && $docReq->distributor_id) {
                                        $orgId = $docReq->distributor_id;
                                        $orgType = Distributor::class;
                                    }
                                }
                            }

                            if ($orgId && $orgType) {
                                DB::table('order_documents')
                                    ->where('id', $orderDocument->id)
                                    ->update([
                                        'created_by_organization_id' => $orgId,
                                        'created_by_organization_type' => $orgType,
                                        'updated_at' => now(),
                                    ]);
                            }
                        } catch (Exception $e) {
                            Logger::error($e->getMessage());
                        }
                    }
                });

            $nullCount = DB::table('order_documents')
                ->whereNull('created_by_organization_id')
                ->count();

            if ($nullCount > 0) {
                // Keep the column nullable since we have documents without organizations
                Schema::table('order_documents', function (Blueprint $table) {
                    $table->unsignedBigInteger('created_by_organization_id')->nullable()->change();
                    $table->string('created_by_organization_type')->nullable()->change();
                });
            } else {
                // All documents have organizations, safe to make NOT NULL
                Schema::table('order_documents', function (Blueprint $table) {
                    $table->unsignedBigInteger('created_by_organization_id')->change();
                    $table->string('created_by_organization_type')->change();
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_documents', function (Blueprint $table) {
            $table->dropMorphs('created_by_organization');
        });
    }
};
