<?php

use App\Enums\AddressTypeEnum;
use App\Models\Order;
use App\Models\SameAndSimilarCheck;
use App\Models\SameAndSimilarCheckHistory;
use App\Models\Scopes\NotArchivedOrderScope;
use App\Services\ShuttleHealth\OrganizationContextService;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $sameAndSimilarChecks = SameAndSimilarCheck::select('id', 'order_id')->get();
        $organizationContextService = app(OrganizationContextService::class);

        foreach ($sameAndSimilarChecks as $check) {
            $order = Order::withoutGlobalScope(NotArchivedOrderScope::class)->where('id', $check->order_id)->first();
            $distributor = $order->distributor;
            $organizationContextService->setOrganization($distributor);
            $patientData = $order->patient;
            $updateSameAndSimilarCheck = SameAndSimilarCheck::where('id', $check->id)->update([
                'patient_id' => $patientData->id,
                'patient_first_name' => $patientData->first_name,
                'patient_last_name' => $patientData->last_name,
                'patient_dob' => $patientData->date_of_birth,
                'patient_state_code' => $patientData->addresses()->where('type', AddressTypeEnum::MAILING)->first()?->state,
                'patient_gender' => $patientData->gender,
            ]);
            $updateSameAndSimilarCheckHistory = SameAndSimilarCheckHistory::where('same_and_similar_check_id', $check->id)->update([
                'patient_id' => $patientData->id,
                'patient_first_name' => $patientData->first_name,
                'patient_last_name' => $patientData->last_name,
                'patient_dob' => $patientData->date_of_birth,
                'patient_state_code' => $patientData->addresses()->where('type', AddressTypeEnum::MAILING)->first()?->state,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
