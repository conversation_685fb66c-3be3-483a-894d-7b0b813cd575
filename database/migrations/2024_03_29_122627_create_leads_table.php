<?php

use App\Enums\LeadQualityRankEnum;
use App\Enums\LeadStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distributor_campaign_id')->constrained('distributor_campaigns')->cascadeOnDelete();
            $table->string('first_name', 50);
            $table->string('last_name', 50);
            $table->char('gender', 1)->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('email')->nullable();
            $table->unsignedBigInteger('mobile');
            $table->string('zip', 10)->nullable();
            $table->boolean('communications')->default(false);
            $table->string('communications_text')->nullable();
            $table->string('payer_type')->nullable();
            $table->string('source')->nullable();
            $table->string('source_url')->nullable();
            $table->string('marketing_id', 100)->nullable();
            $table->date('created_date')->nullable();
            $table->date('follow_up_at')->nullable();
            $table->string('status', 20)->default(LeadStatusEnum::OPEN->value);
            $table->foreignId('assigned_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->tinyInteger('quality_rank', unsigned: true)->default(LeadQualityRankEnum::MEDIUM->value);
            $table->foreignId('patient_id')->nullable()->constrained('patients')->nullOnDelete();
            $table->date('canceled_date')->nullable();
            $table->string('canceled_reason')->nullable();
            $table->json('notes')->nullable();
            $table->date('converted_date')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

            $table->index(['assigned_user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
