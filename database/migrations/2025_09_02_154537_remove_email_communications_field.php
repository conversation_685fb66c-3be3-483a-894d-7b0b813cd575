<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('leads', 'email_communications')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->dropColumn('email_communications');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('leads', 'email_communications')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->boolean('email_communications')->default(true);
            });
        }
    }
};
