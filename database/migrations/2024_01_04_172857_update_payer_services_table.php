<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE payer_services SET payer_service_type = \'changehealthcare\' WHERE payer_service_type=\'Eligibility\'');

            Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
                $table->dropColumn('service');
                $table->foreignId('payer_service_id')->nullable()->constrained()->cascadeOnDelete();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('UPDATE payer_services SET payer_service_type = \'Eligibility\' WHERE payer_service_type=\'changehealthcare\'');

        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->string('service')->default('changehealthcare');
            $table->dropConstrainedForeignId('payer_service_id');
        });
    }
};
