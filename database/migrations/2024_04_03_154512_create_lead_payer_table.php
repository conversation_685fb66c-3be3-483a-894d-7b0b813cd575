<?php

use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PatientPayerStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_payer', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained()->cascadeOnDelete();
            $table->foreignId('payer_id')->constrained()->cascadeOnDelete();
            $table->string('policy_number', 30);
            $table->string('group_number', 20)->nullable();
            $table->smallInteger('priority')->default(PatientPayerPriorityEnum::PRIMARY->value);
            $table->string('status', 20)->default(PatientPayerStatusEnum::ACTIVE->value);
            $table->date('check_date')->nullable();
            $table->foreignId('check_by')->nullable()->constrained('users')->nullOnDelete();
            $table->string('user_defined_payer_type', 20)->nullable();
            $table->date('insurance_start_date')->nullable();
            $table->date('insurance_end_date')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

            $table->unique(['lead_id', 'payer_id']);
            $table->index(['lead_id', 'payer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_payer');
    }
};
