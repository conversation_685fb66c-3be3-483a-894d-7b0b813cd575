<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->date('check_date')->nullable();
            $table->foreignId('check_by')->nullable()->constrained('users')->nullOnDelete();
            $table->integer('deductible')->nullable();
            $table->integer('co_insurance')->nullable();
            $table->integer('max_out_of')->nullable();
            $table->text('comment')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->dropColumn(['check_date', 'deductible', 'co_insurance', 'max_out_of', 'comment']);
            $table->dropConstrainedForeignId('check_by');
        });
    }
};
