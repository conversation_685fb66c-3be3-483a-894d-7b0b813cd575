<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('filter_sets', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('view');
            $table->json('filters');
            $table->unsignedBigInteger('user_id');
            $table->softDeletes(); // For soft delete functionality
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('filter_sets');
    }
};
