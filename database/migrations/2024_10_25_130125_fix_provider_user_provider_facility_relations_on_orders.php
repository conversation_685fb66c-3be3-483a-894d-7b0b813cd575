<?php

use App\Actions\ShuttleHealth\FindOrCreateFacilityForProviderUser;
use App\Enums\OrderStatusEnum;
use App\Extensions\Logger;
use App\Models\Order;
use App\Models\Scopes\NotArchivedOrderScope;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            $findOrCreateFacilityAction = new FindOrCreateFacilityForProviderUser;
            $activeOrderStatuses = [
                OrderStatusEnum::DISTRIBUTOR_CANCELED->value,
                OrderStatusEnum::PROVIDER_CANCELED->value,
                OrderStatusEnum::MANUFACTURER_CANCELED->value,
                OrderStatusEnum::SHIPPED->value,
            ];

            Order::withoutGlobalScope(NotArchivedOrderScope::class)->with('providerUser', 'provider', 'facility')
                ->whereHas('providerUser')
                ->chunkById(500, function (Collection $order) use ($findOrCreateFacilityAction, $activeOrderStatuses) {
                    $order->each(function (Order $order) use ($findOrCreateFacilityAction, $activeOrderStatuses) {
                        if ($order->provider) {
                            if (!$order->providerUser->belongsToOrganization($order->provider)) {
                                Logger::warning('provider_user_id does not match provider_id. Trying to check facility. Order ID: ' . $order->id);

                                if (!$order->facility || $order->providerUser->id != $order->facility->user_id) {
                                    Logger::warning('Provider and Facility user_id does not match provider_user_id, trying to assign new facility. Order ID: ' . $order->id);

                                    if (in_array($order->status->value, $activeOrderStatuses)) {
                                        Logger::warning('Order status is not active. Skipping facility update. Order ID: ' . $order->id);

                                        return;
                                    }

                                    $facility = $findOrCreateFacilityAction->execute($order->providerUser, $order->distributor_id);
                                    $order->update([
                                        'facility_id' => $facility->id,
                                        'provider_id' => null,
                                    ]);
                                } else {
                                    $order->update(['provider_id' => null]);
                                }

                            } elseif ($order->facility) {
                                $order->update(['facility_id' => null]);
                            }
                        } elseif ($order->facility) {
                            if ($order->providerUser->id != $order->facility->user_id) {
                                Logger::warning('Facility user_id does not match provider_user_id, trying to assign new facility. Order ID: ' . $order->id);

                                if (in_array($order->status->value, $activeOrderStatuses)) {
                                    Logger::warning('Order status is not active. Skipping facility update. Order ID: ' . $order->id);

                                    return;
                                }

                                $facility = $findOrCreateFacilityAction->execute($order->providerUser, $order->distributor_id);
                                $order->update([
                                    'facility_id' => $facility->id,
                                    'provider_id' => null,
                                ]);
                            }
                        }
                    });
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
