<?php

use App\Enums\GlobalSettingGroupEnum;
use App\Enums\GlobalSettingNameEnum;
use App\Enums\ValueTypeEnum;
use App\Models\GlobalSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /* Global settings */
        $settings = [
            [
                'group' => GlobalSettingGroupEnum::RENEWAL_REQUEST_FAXES->value,
                'name' => GlobalSettingNameEnum::DAYS_TO_SEND_FAXES->value,
                'value_type' => ValueTypeEnum::INTEGER->value,
                'value' => 20,
            ],
            [
                'group' => GlobalSettingGroupEnum::RENEWAL_REQUEST_FAXES->value,
                'name' => GlobalSettingNameEnum::BUSINESS_DAYS_TO_RESEND_FAXES->value,
                'value_type' => ValueTypeEnum::INTEGER->value,
                'value' => 3,
            ],
        ];

        GlobalSetting::query()->insert($settings);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('global_settings')->truncate();
    }
};
