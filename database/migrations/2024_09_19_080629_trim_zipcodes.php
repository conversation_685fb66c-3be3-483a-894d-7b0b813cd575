<?php

use App\Models\ZipCode;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            $duplicateZipCodes = ZipCode::query()
                ->select([DB::raw('LEFT(zip_code, 5) as trimmed_zip_code'), DB::raw('COUNT(*) as duplicate_count')])
                ->groupBy(DB::raw('LEFT(zip_code, 5)'))
                ->havingRaw('COUNT(*) > 1')
                ->get();

            foreach ($duplicateZipCodes as $duplicate) {
                ZipCode::query()
                    ->where(DB::raw('LEFT(zip_code, 5)'), $duplicate->trimmed_zip_code)
                    ->limit($duplicate->duplicate_count - 1)
                    ->delete();
            }

            ZipCode::query()
                ->whereRaw('LENGTH(zip_code) > 5')
                ->update(['zip_code' => DB::raw('LEFT(zip_code, 5)')]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
