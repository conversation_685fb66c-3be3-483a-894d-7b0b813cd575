<?php

use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Models\PatientDocument;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //  PatientDocument::query()
        //  ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
        //  ->update(['source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER]);

        DB::table('order_documents')
            ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
            ->update(['source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // PatientDocument::query()
        // ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
        // ->update(['source' => OrderDocumentSourceEnum::SYSTEM]);

        DB::table('patient_documents')
            ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
            ->update(['source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER]);
    }
};
