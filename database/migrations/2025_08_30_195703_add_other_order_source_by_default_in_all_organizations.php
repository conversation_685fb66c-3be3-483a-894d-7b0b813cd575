<?php

use App\Enums\OrderSourceEnum;
use App\Models\Distributor;
use App\Models\Manufacturer;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $distributors = Distributor::with(['orders', 'orderSources'])->get();
        $otherOrderSource = OrderSourceEnum::OTHER->value;
        $otherOrderSourceSlug = Str::slug($otherOrderSource);

        foreach ($distributors as $distributor) {
            $distributor->orderSources()->updateOrCreate(
                [
                    'order_source_slug' => $otherOrderSourceSlug,
                    'organization_id' => $distributor->id,
                    'organization_type' => get_class($distributor),
                ],
                [
                    'order_source_name' => $otherOrderSource,
                    'order_source_slug' => $otherOrderSourceSlug,
                ],
            );

            $orderSource = $distributor->orderSources()->where('order_source_slug', $otherOrderSourceSlug)->first();

            if ($orderSource) {
                $orderIds = $distributor->orders()->whereNull('order_source_id')->pluck('id');

                if ($orderIds->isNotEmpty()) {
                    Order::whereIn('id', $orderIds)->update(['order_source_id' => $orderSource->id]);
                }
            }
        }

        $manufacturers = Manufacturer::with(['products.orders', 'orderSources'])->get();

        foreach ($manufacturers as $manufacturer) {
            $manufacturer->orderSources()->updateOrCreate(
                [
                    'order_source_slug' => $otherOrderSourceSlug,
                    'organization_id' => $manufacturer->id,
                    'organization_type' => get_class($manufacturer),
                ],
                [
                    'order_source_name' => $otherOrderSource,
                    'order_source_slug' => $otherOrderSourceSlug,
                ],
            );

            foreach ($manufacturer->products as $product) {
                foreach ($product->orders as $order) {
                    if (empty($order->order_source_id)) {
                        $orderSource = $manufacturer->orderSources()->where('order_source_slug', $otherOrderSourceSlug)->first();

                        if ($orderSource) {
                            $order->order_source_id = $orderSource->id;
                            $order->save();
                        }
                    }
                }
            }
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
