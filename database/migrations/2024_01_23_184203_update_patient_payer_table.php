<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->renameColumn('max_out_of', 'max_oop');
            $table->integer('in_deductible')->nullable();
            $table->integer('in_co_insurance')->nullable();
            $table->integer('in_max_oop')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->renameColumn('max_oop', 'max_out_of');
            $table->dropColumn(['in_deductible', 'in_co_insurance', 'in_max_oop']);
        });
    }
};
