<?php

use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PatientPayerStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('DELETE FROM migrations WHERE migration like \'2022_%\'');

        if (!Schema::hasTable('patient_payer')) {
            Schema::create('patient_payer', function (Blueprint $table) {
                $table->id();
                $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
                $table->foreignId('payer_id')->constrained()->cascadeOnDelete();
                $table->string('policy_number', 30);
                $table->string('group_number', 20)->nullable();
                $table->smallInteger('priority')->default(PatientPayerPriorityEnum::PRIMARY->value);
                $table->string('status')->default(PatientPayerStatusEnum::ACTIVE->value);
                $table->date('insurance_start_date')->nullable();
                $table->date('insurance_end_date')->nullable();

                $table->unique(['patient_id', 'payer_id']);
                $table->index('patient_id', 'patient_payer_patient_id_index');
                $table->index('payer_id', 'patient_payer_payer_id_index');
            });

            Schema::create('order_activity_logs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id')->constrained();
                $table->foreignId('user_id')->nullable()->constrained();
                $table->string('type');
                $table->json('metadata')->nullable();
                $table->dateTime('activity_at')->nullable(false);
                $table->timestamps();

                $table->index(['order_id', 'type'], 'order_activity_logs_order_id_type_index');
                $table->index(['user_id', 'type'], 'order_activity_logs_user_id_type_index');
            });

            Schema::create('product_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique();
                $table->longText('description')->nullable();
                $table->string('image_url')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });

            Schema::create('product_product_category', function (Blueprint $table) {
                $table->foreignId('product_id')->constrained()->cascadeOnDelete();
                $table->foreignId('product_category_id')->constrained()->cascadeOnDelete();

                $table->index('product_id', 'product_product_category_product_id_index');
                $table->index('product_category_id', 'product_product_category_product_category_id_index');
            });

            Schema::create('patient_provider', function (Blueprint $table) {
                $table->foreignId('provider_id')->constrained()->cascadeOnDelete();
                $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
                $table->string('medical_record_id')->nullable();
                $table->softDeletes();

                $table->index('provider_id', 'patient_provider_provider_id_index');
                $table->index('patient_id', 'patient_provider_patient_id_index');
            });

            Schema::create('distributor_patient', function (Blueprint $table) {
                $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
                $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
                $table->string('distributor_patient_id')->nullable();
                $table->softDeletes();

                $table->index('distributor_id', 'distributor_patient_distributor_id_index');
                $table->index('patient_id', 'distributor_patient_patient_id_index');
            });

            Schema::create('manufacturer_patient', function (Blueprint $table) {
                $table->foreignId('manufacturer_id')->constrained()->cascadeOnDelete();
                $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
                $table->string('manufacturer_patient_id')->nullable();
                $table->softDeletes();

                $table->index('manufacturer_id', 'manufacturer_patient_manufacturer_id_index');
                $table->index('patient_id', 'manufacturer_patient_patient_id_index');
            });

            Schema::create('product_groups', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique();
                $table->longText('description')->nullable();
                $table->string('image_url')->nullable();
                $table->timestamps();
            });

            Schema::create('product_product_group', function (Blueprint $table) {
                $table->foreignId('product_id')->constrained()->cascadeOnDelete();
                $table->foreignId('product_group_id')->constrained()->cascadeOnDelete();

                $table->index('product_id', 'product_product_group_product_id_index');
                $table->index('product_group_id', 'product_product_group_product_group_id_index');
            });

            Schema::create('medical_policy_forms', function (Blueprint $table) {
                $table->id();
                $table->json('form_schema');
                $table->string('form_name')->nullable();
                $table->text('form_description')->nullable();
                $table->timestamps();
            });

            Schema::create('medical_policy_responses', function (Blueprint $table) {
                $table->id();
                $table->foreignId('order_id');
                $table->foreignId('medical_policy_form_id');
                $table->json('form_data');
                $table->json('form')->nullable();
                $table->json('uploaded_files')->nullable();
                $table->timestamps();

                $table->unique(['order_id', 'medical_policy_form_id']);
                $table->index('order_id', 'medical_policy_responses_order_id_index');
                $table->index('medical_policy_form_id', 'medical_policy_responses_medical_policy_form_id_index');
            });

            Schema::create('patient_payer_eligibility_checks', function (Blueprint $table) {
                $table->id();
                $table->foreignId('patient_payer_id')->constrained('patient_payer')->cascadeOnDelete();
                $table->json('eligibility_response_json');
                $table->timestamps();
            });

            Schema::create('payer_aliases', function (Blueprint $table) {
                $table->id();
                $table->foreignId('payer_id');
                $table->string('alias');

                $table->unique(['payer_id', 'alias']);
            });

            Schema::create('addresses', function (Blueprint $table) {
                $table->id();
                $table->morphs('addressable');
                $table->string('type');
                $table->string('address_line_1');
                $table->string('address_line_2')->nullable();
                $table->string('city');
                $table->char('state', 2);
                $table->string('zip', 10);
                $table->timestamps();

                $table->unique(['addressable_id', 'addressable_type', 'type']);
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('patient_payer');
        Schema::dropIfExists('order_activity_logs');
        Schema::dropIfExists('product_categories');
        Schema::dropIfExists('product_product_category');
        Schema::dropIfExists('patient_provider');
        Schema::dropIfExists('distributor_patient');
        Schema::dropIfExists('manufacturer_patient');
        Schema::dropIfExists('product_groups');
        Schema::dropIfExists('product_product_group');
        Schema::dropIfExists('medical_policy_forms');
        Schema::dropIfExists('medical_policy_responses');
        Schema::dropIfExists('order_eligibility_checks');
        Schema::dropIfExists('payer_aliases');
        Schema::dropIfExists('addresses');
    }
};
