<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            $table->integer('patient_id')->nullable();
            $table->integer('provider_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sw_fax_logs', function (Blueprint $table) {
            $table->dropColumn('patient_id');
            $table->dropColumn('provider_user_id');
        });
    }
};
