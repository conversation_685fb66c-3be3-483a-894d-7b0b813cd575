<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->unsignedTinyInteger('retry_count')->default(0);
            $table->timestamp('failed_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->dropColumn('retry_count');
            $table->dropColumn('failed_at');
        });
    }
};
