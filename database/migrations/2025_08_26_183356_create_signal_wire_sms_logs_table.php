<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('signal_wire_sms_logs', function (Blueprint $table) {
            $table->id();
            $table->morphs('organization');
            $table->string('sid')->unique();
            $table->string('entity_type');
            $table->unsignedBigInteger('entity_id');
            $table->string('from');
            $table->string('to');
            $table->text('body');
            $table->string('status');
            $table->string('error_code')->nullable();
            $table->string('error_message')->nullable();
            $table->string('direction');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('signal_wire_sms_logs');
    }
};
