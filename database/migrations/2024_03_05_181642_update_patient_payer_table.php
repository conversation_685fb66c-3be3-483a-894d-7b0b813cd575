<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->integer('deductible_remaining')->nullable();
            $table->integer('in_deductible_remaining')->nullable();
            $table->integer('max_oop_remaining')->nullable();
            $table->integer('in_max_oop_remaining')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->dropColumn(['deductible_remaining', 'in_deductible_remaining', 'max_oop_remaining', 'in_max_oop_remaining']);
        });
    }
};
