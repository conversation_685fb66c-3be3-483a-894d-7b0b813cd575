<?php

use App\Enums\OrderSourceEnum;
use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Order;
use App\Models\OrderSource;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        OrderSource::truncate();
        Order::query()->update(['order_source_id' => null]);
        $distributors = Distributor::orderBy('id', 'desc')->get();

        foreach ($distributors as $distributor) {
            $defaultOrderSources = OrderSourceEnum::systemOrderSources();

            foreach ($defaultOrderSources as $defaultOrderSource) {
                $distributor->orderSources()->updateOrCreate(
                    [
                        'order_source_slug' => Str::slug($defaultOrderSource->value), // Fixed: use slug for slug field
                        'organization_id' => $distributor->id,
                        'organization_type' => get_class($distributor),
                    ],
                    [
                        'order_source_name' => $defaultOrderSource->value,
                        'order_source_slug' => Str::slug($defaultOrderSource->value),
                        'is_system' => true,
                        'is_active' => true,
                    ],
                );
            }

        }

        $manufacturers = Manufacturer::orderBy('id', 'desc')->get();

        foreach ($manufacturers as $manufacturer) {
            $defaultOrderSources = OrderSourceEnum::systemOrderSources();

            foreach ($defaultOrderSources as $defaultOrderSource) {
                $manufacturer->orderSources()->updateOrCreate(
                    [
                        'order_source_slug' => Str::slug($defaultOrderSource->value), // Fixed: use slug for slug field
                        'organization_id' => $manufacturer->id,
                        'organization_type' => get_class($manufacturer),
                    ],
                    [
                        'order_source_name' => $defaultOrderSource->value,
                        'order_source_slug' => Str::slug($defaultOrderSource->value),
                        'is_system' => true,
                        'is_active' => true,
                    ],
                );
            }

        }


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
