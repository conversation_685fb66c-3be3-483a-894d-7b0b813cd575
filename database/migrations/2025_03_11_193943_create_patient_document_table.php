<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_document', function (Blueprint $table) {
            $table->foreignId('patient_id')->constrained('patients')->cascadeOnDelete();
            $table->foreignId('document_id')->constrained('order_documents')->cascadeOnDelete();

            $table->unique(['patient_id', 'document_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_document');
    }
};
