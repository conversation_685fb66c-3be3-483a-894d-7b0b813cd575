<?php

use App\Enums\OrganizationSettingNameEnum;
use App\Enums\ValueTypeEnum;
use App\Models\Distributor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('organization_settings', function ($table) {
            $table->text('name', 100)->change();
        });

        if (!app()->isProduction()) {
            $distributor = Distributor::find(2);

            if (!$distributor) {
                return;
            }
            $clientId = encrypt('294766353344-a8tlnqtcjrdhhdsjn5eltvg7im2h79ku.apps.googleusercontent.com');
            $clientSecret = encrypt('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');
            $refreshToken = encrypt('1//04RW0Jb_r7MGVCgYIARAAGAQSNwF-L9IrkRx0so6lFtbFaeYVNn6JGsHstJ0YquklR6ZGnJKNRyCP8Wz89zX9BMSZhFQbJg7Ve-M');
            $developerToken = encrypt('9q-I4I0XMd5VplGRhanivw');
            $loginCustomerId = encrypt('4963037345');
            $customerId = encrypt('9190681747');
            $leadQualifiedConverstionActionId = encrypt('7155665939');
            $leadConvertedToOrderConversionActionId = encrypt('7155667172');
            $orderShippedConversionActionId = encrypt('7155667175');
            $leadUnqualifiedConversionActionId = encrypt('7155667178');
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $clientId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_SECRET,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $clientSecret,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_REFRESH_TOKEN,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $refreshToken,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_DEVELOPER_TOKEN,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $developerToken,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LOGIN_CUSTOMER_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $loginCustomerId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CUSTOMER_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $customerId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_QUALIFIED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadQualifiedConverstionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_CONVERTED_TO_ORDER_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadConvertedToOrderConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_ORDER_SHIPPED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $orderShippedConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_UNQUALIFIED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadUnqualifiedConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
        } else {
            $distributor = Distributor::find(2);

            if (!$distributor) {
                return;
            }
            $clientId = encrypt('294766353344-a8tlnqtcjrdhhdsjn5eltvg7im2h79ku.apps.googleusercontent.com');
            $clientSecret = encrypt('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');
            $refreshToken = encrypt('1//04RW0Jb_r7MGVCgYIARAAGAQSNwF-L9IrkRx0so6lFtbFaeYVNn6JGsHstJ0YquklR6ZGnJKNRyCP8Wz89zX9BMSZhFQbJg7Ve-M');
            $developerToken = encrypt('9q-I4I0XMd5VplGRhanivw');
            $loginCustomerId = encrypt('4963037345');
            $customerId = encrypt('9190681747');
            $leadQualifiedConverstionActionId = encrypt('7155665939');
            $leadConvertedToOrderConversionActionId = encrypt('7155667172');
            $orderShippedConversionActionId = encrypt('7155667175');
            $leadUnqualifiedConversionActionId = encrypt('7155667178');
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $clientId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_SECRET,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $clientSecret,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_REFRESH_TOKEN,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $refreshToken,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_DEVELOPER_TOKEN,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $developerToken,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LOGIN_CUSTOMER_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $loginCustomerId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CUSTOMER_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $customerId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_QUALIFIED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadQualifiedConverstionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_CONVERTED_TO_ORDER_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadConvertedToOrderConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_ORDER_SHIPPED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $orderShippedConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
            $distributor->organizationSettings()->updateOrCreate(
                [
                    'name' => OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_UNQUALIFIED_CONVERSION_ACTION_ID,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
                [
                    'value' => $leadUnqualifiedConversionActionId,
                    'value_type' => ValueTypeEnum::TEXT,
                    'organization_type' => Distributor::class,
                    'organization_id' => $distributor->id,
                ],
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $distributor = Distributor::find(2);

        if (!$distributor) {
            return;
        }

        $settingNames = [
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_ID,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CLIENT_SECRET,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_REFRESH_TOKEN,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_DEVELOPER_TOKEN,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LOGIN_CUSTOMER_ID,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CUSTOMER_ID,
            OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_CONVERSION_ACTION_ID,
        ];

        $distributor->organizationSettings()
            ->whereIn('name', $settingNames)
            ->where('organization_type', Distributor::class)
            ->where('organization_id', $distributor->id)
            ->delete();
    }
};
