<?php

use App\Models\Order;
use App\Models\Patient;
use App\Models\Scopes\NotArchivedOrderScope;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Order::withoutGlobalScope(NotArchivedOrderScope::class)->chunkById(100, function (Collection $orders) {
                $orders->each(function (Order $order) {
                    /** @var Patient $patient */
                    $patient = Patient::findOrFail($order->patient_id);
                    $order->global_patient_id = $patient->global_patient_id;
                    $order->save();
                });
            });

            Schema::table('orders', function (Blueprint $table) {
                $table->foreignId('global_patient_id')->change();
            });
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('global_patient_id')->nullable()->change();
        });
    }
};
