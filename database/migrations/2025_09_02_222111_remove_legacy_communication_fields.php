<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove legacy communication fields from leads table
        if (Schema::hasColumn('leads', 'communications')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->dropColumn('communications');
            });
        }

        // Remove legacy communication fields from patients table
        if (Schema::hasColumn('patients', 'mobile_communications')) {
            Schema::table('patients', function (Blueprint $table) {
                $table->dropColumn('mobile_communications');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add legacy communication fields to leads table
        if (!Schema::hasColumn('leads', 'communications')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->boolean('communications')->default(false)->after('communications_text');
            });
        }

        // Re-add legacy communication fields to patients table
        if (!Schema::hasColumn('patients', 'mobile_communications')) {
            Schema::table('patients', function (Blueprint $table) {
                $table->boolean('mobile_communications')->default(false)->after('mobile');
            });
        }
    }
};
