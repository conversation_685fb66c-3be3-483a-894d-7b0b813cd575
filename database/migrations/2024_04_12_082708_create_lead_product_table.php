<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_product', function (Blueprint $table) {
            $table->foreignId('lead_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->decimal('measure_count', 12, 6)->unsigned()->default(0);
            $table->string('measure_unit')->nullable();
            $table->integer('duration_count')->unsigned()->default(0);
            $table->string('duration_unit')->nullable();
            $table->string('narrative_unit')->nullable();
            $table->string('narrative_measure_unit')->nullable();
            $table->decimal('narrative_measure_count', 12, 6)->unsigned()->default(0);
            $table->string('serial_number')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

            $table->index('lead_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_product');
    }
};
