<?php

use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Order;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $distributors = Distributor::with(['orders'])->get();

        foreach ($distributors as $distributor) {
            $orders = Order::where('created_by_organization_id', $distributor->id)
                ->where('created_by_organization_type', Distributor::class)
                ->get();

            foreach ($orders as $order) {
                $orderSource = $order->source;
                $distributorOrderSource = $distributor->orderSources()
                    ->where('order_source_name', $orderSource)
                    ->first();
                Order::where('id', $order?->id)->where('created_by_organization_id', $distributor?->id)->update(['order_source_id' => $distributorOrderSource?->id]); //Update silently without any events
            }
        }

        $manufacturers = Manufacturer::with(['products'])->get();

        foreach ($manufacturers as $manufacturer) {
            $orders = Order::where('created_by_organization_id', $manufacturer->id)
                ->where('created_by_organization_type', Manufacturer::class)
                ->get();

            foreach ($orders as $order) {
                $orderSource = $order->source;
                $manufacturerOrderSource = $manufacturer->orderSources()
                    ->where('order_source_name', $orderSource)
                    ->first();
                Order::where('id', $order?->id)->where('created_by_organization_id', $manufacturer?->id)->update(['order_source_id' => $manufacturerOrderSource?->id]); //Update silently without any events
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
