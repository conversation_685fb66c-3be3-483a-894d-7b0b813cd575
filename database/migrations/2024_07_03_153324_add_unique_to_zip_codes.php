<?php

use App\Models\ZipCode;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove duplicate zip codes
        $duplicateZipCodes = ZipCode::select('zip_code')
            ->groupBy('zip_code')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('zip_code');

        foreach ($duplicateZipCodes as $zip_code) {
            $duplicateRecords = ZipCode::where('zip_code', $zip_code)->orderBy('id')->get();

            $duplicateRecords->skip(1)->each(function ($duplicate) {
                $duplicate->delete();
            });
        }

        Schema::table('zip_codes', function (Blueprint $table) {
            $table->unique(['zip_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('zip_codes', function (Blueprint $table) {
            $table->dropUnique(['zip_code']);
        });
    }
};
