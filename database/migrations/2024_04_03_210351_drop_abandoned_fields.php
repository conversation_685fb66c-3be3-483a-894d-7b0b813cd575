<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('patient_id');
        });
        Schema::table('order_document_requests', function (Blueprint $table) {
            $table->dropColumn('fax_file_id');
            $table->dropColumn('fax_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('patient_id')->nullable()->constrained();
        });
        Schema::table('order_document_requests', function (Blueprint $table) {
            $table->foreignId('fax_id')->nullable()->constrained('faxes')->nullOnDelete();
            $table->foreignId('fax_file_id')->nullable();
        });
    }
};
