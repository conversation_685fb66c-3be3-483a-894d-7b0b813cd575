<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_patient_document', function (Blueprint $table) {
            // $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_document_id')->constrained()->onDelete('cascade');
        });

        // Fetch patient_documents that have an order_id
        $documents = DB::table('patient_documents')
            ->whereNotNull('order_id')
            ->select('id as patient_document_id', 'order_id')
            ->get();

        // Insert into the pivot table
        foreach ($documents as $doc) {
            DB::table('order_patient_document')->insert([
                'order_id' => $doc->order_id,
                'patient_document_id' => $doc->patient_document_id,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // to clean up the pivot table before dropping it, this is optional
        DB::table('order_patient_document')->truncate();
        Schema::dropIfExists('order_patient_document');
    }
};
