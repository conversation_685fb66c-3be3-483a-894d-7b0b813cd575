<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sw_fax_logs', function (Blueprint $table) {
            $table->id();
            $table->string('fax_log_id');
            $table->string('to');
            $table->string('from');
            $table->string('status');
            $table->string('direction');
            $table->dateTime('fax_log_created_at');
            $table->json('fax_log_metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sw_fax_logs');
    }
};
