<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing constraint
        DB::statement('ALTER TABLE patients DROP CONSTRAINT IF EXISTS patients_gender_check');

        // Add the new constraint that includes 'U' for Unknown
        DB::statement('ALTER TABLE patients ADD CONSTRAINT patients_gender_check CHECK ((gender)::text = ANY ((ARRAY[\'M\'::character varying, \'F\'::character varying, \'U\'::character varying])::text[]))');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the updated constraint
        DB::statement('ALTER TABLE patients DROP CONSTRAINT IF EXISTS patients_gender_check');

        // Restore the original constraint (only M and F)
        DB::statement('ALTER TABLE patients ADD CONSTRAINT patients_gender_check CHECK ((gender)::text = ANY ((ARRAY[\'M\'::character varying, \'F\'::character varying])::text[]))');
    }
};
