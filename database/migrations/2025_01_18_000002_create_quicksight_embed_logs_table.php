<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateQuicksightEmbedLogsTable extends Migration
{
    public function up(): void
    {
        Schema::create('quicksight_embed_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('distributor_id')->constrained('distributors')->onDelete('cascade');
            $table->string('dashboard_id');
            $table->text('embed_url');  // Using text for potentially long URLs
            $table->ipAddress('ip_address');
            $table->jsonb('session_tags');
            $table->timestamp('expires_at');
            $table->timestamps();

            // Index for frequent queries
            $table->index(['distributor_id', 'created_at', 'dashboard_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quicksight_embed_logs');
    }
}
