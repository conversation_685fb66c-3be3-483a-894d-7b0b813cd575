<?php

use App\Enums\ActivityLogTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Models\ActivityLog;
use App\Models\Order;
use App\Models\Scopes\NotArchivedOrderScope;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const STATUSES_TO_RENAME = [
        'Pending Customer Feedback' => OrderStatusEnum::PENDING_CUSTOMER_CONTACT->value,
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->renameOrderStatuses(self::STATUSES_TO_RENAME);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->renameOrderStatuses(array_flip(self::STATUSES_TO_RENAME));
    }

    private function renameOrderStatuses(array $statusesToRename): void
    {
        DB::transaction(function () use ($statusesToRename) {
            foreach ($statusesToRename as $oldStatus => $newStatus) {
                Order::withoutGlobalScope(NotArchivedOrderScope::class)
                    ->where('status', $oldStatus)
                    ->update(['status' => $newStatus]);
            }

            ActivityLog::query()
                ->where('activityable_type', Order::class)
                ->where('type', ActivityLogTypeEnum::UPDATE_STATUS)
                ->chunkById(100, function ($activityLogs) use ($statusesToRename) {
                    /** @var ActivityLog $activityLog */
                    foreach ($activityLogs as $activityLog) {
                        $metadata = $activityLog->metadata;

                        if (!empty($metadata['status']) && isset($statusesToRename[$metadata['status']])) {
                            $metadata['status'] = $statusesToRename[$metadata['status']];

                            $activityLog->metadata = $metadata;
                            $activityLog->saveQuietly();
                        }
                    }
                });
        });
    }
};
