<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    public function __construct()
    {
        ini_set('memory_limit', '2048M');

        DB::disableQueryLog();
    }

    public function run(): void
    {
        $this->call(CMSDataSeeder::class);
        $this->call(RoleAndPermissionSeeder::class);
        $this->call(LanguageSeeder::class);

        echo 'Seeding Payers' . PHP_EOL;
        Artisan::call('seed-data:payers');

        if (App::environment(['local', 'development'])) {
            $this->call(DevDataSeeder::class);
        }
    }
}
