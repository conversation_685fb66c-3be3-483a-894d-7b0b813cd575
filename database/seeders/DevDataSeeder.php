<?php

namespace Database\Seeders;

use App\Enums\DistributorUserTypeEnum;
use App\Enums\ManufacturerUserTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductDurationUnitEnum;
use App\Enums\ProductMeasureUnitEnum;
use App\Enums\ProductNarrativeUnitEnum;
use App\Enums\ProviderUserTypeEnum;
use App\Enums\ShuttleUserTypeEnum;
use App\Models\Distributor;
use App\Models\DistributorUser;
use App\Models\Manufacturer;
use App\Models\ManufacturerUser;
use App\Models\NpiRecord;
use App\Models\Order;
use App\Models\Patient;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Provider;
use App\Models\ProviderUser;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;

class DevDataSeeder extends Seeder
{
    public function run(): void
    {

        // set up a master SH Admin User
        $this->createSHMasterUser();

        // set up a dummy SH manufacturer
        $manufacturer = $this->createSHManufacturer();

        // set up a dummy SH Distributor
        $distributor = $this->createSHDistributor();

        // setup dummy products for diabetes
        $diabetesProducts = $this->createProductsForSHDiabetes($manufacturer);
        $distributor->products()->saveMany($diabetesProducts);

        // setup dummy products for incontinence
        $incontinenceProducts = $this->createProductsForSHIncontinence($manufacturer);
        $distributor->products()->saveMany($incontinenceProducts);

        // setup dummy products for ostomy
        $ostomyProducts = $this->createProductsForSHOstomy($manufacturer);
        $distributor->products()->saveMany($ostomyProducts);

        // setup dummy products for urology
        $urologyProducts = $this->createProductsForSHUrology($manufacturer);
        $distributor->products()->saveMany($urologyProducts);

        // setup dummy products for wound care
        $woundCareProducts = $this->createProductsForSHWoundCare($manufacturer);
        $distributor->products()->saveMany($woundCareProducts);

        // set up two dummy SH Providers
        $provider1 = $this->createSHProvider1();
        $provider2 = $this->createSHProvider2();

        // create a doctor belonging to both providers
        $doctor = ProviderUser::factory([
            'first_name' => 'John',
            'middle_name' => '',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ])->has(NpiRecord::factory())->withRole(ProviderUserTypeEnum::STAFF)->create();

        $provider1->users()->attach($doctor);
        $provider2->users()->attach($doctor);

        $this->createNewOrdersForProvider($provider1, $doctor, $distributor, $diabetesProducts);
        $this->createNewOrdersForProvider($provider2, $doctor, $distributor, $diabetesProducts);


        // Map Cognito Ids
        Artisan::call('users:update-cognito-ids');

    }

    private function createNewOrdersForProvider(Provider $provider, ProviderUser $providerUser, Distributor $distributor, Collection $products): void
    {
        // create new orders without a distributor selected
        $orders = Order::factory([
            'type' => OrderTypeEnum::NEW_PRESCRIPTION->value,
            'status' => OrderStatusEnum::PENDING_DISTRIBUTOR_SELECTION->value,
        ])
            ->for($provider)
            ->for($providerUser)
            ->hasAttached($products, [
                'measure_count' => 1,
                'measure_unit' => ProductMeasureUnitEnum::EACHES->value,
                'duration_count' => 1,
                'duration_unit' => ProductDurationUnitEnum::LIFETIME->value,
                'narrative_unit' => ProductNarrativeUnitEnum::CHANGE_EVERY->value,
                'narrative_measure_unit' => ProductDurationUnitEnum::DAYS->value,
                'narrative_measure_count' => 1,
                'serial_number' => '1',
            ])
            ->count(2)
            ->create();

        foreach ($orders as $order) {
            Patient::factory()->for($order->globalPatient)->for($provider, 'organization')->create();
        }

        // create new orders with a distributor and without medical policy responses
        $orders = Order::factory([
            'type' => OrderTypeEnum::NEW_PRESCRIPTION->value,
            'status' => OrderStatusEnum::PENDING_CLINICAL_DOCUMENTATION->value,
        ])
            ->for($provider)
            ->for($providerUser)
            ->for($distributor)
            ->hasAttached($products, [
                'measure_count' => 1,
                'measure_unit' => ProductMeasureUnitEnum::EACHES->value,
                'duration_count' => 1,
                'duration_unit' => ProductDurationUnitEnum::LIFETIME->value,
                'narrative_unit' => ProductNarrativeUnitEnum::CHANGE_EVERY->value,
                'narrative_measure_unit' => ProductDurationUnitEnum::DAYS->value,
                'narrative_measure_count' => 1,
                'serial_number' => '1',
            ])
            ->count(8)
            ->create();

        foreach ($orders as $order) {
            $patient = Patient::factory()->for($order->globalPatient)->for($provider, 'organization')->create();
            $patient->linkToOrganization($distributor);
        }
    }

    private function createProductsForSHDiabetes(Manufacturer $manufacturer): Collection
    {
        $productCategory = ProductCategory::factory([
            'name' => 'Diabetes',
            'description' => 'Diabetes Products',
        ])->create();

        $cgm = Product::factory([
            'external_id' => 100000001,
            'name' => 'Shuttle CGM',
            'description' => 'Shuttle Health brand Continuous Glucose Monitor',
        ])
            ->for($manufacturer)
            ->create();

        $insulinPump = Product::factory([
            'external_id' => 100000002,
            'name' => 'Shuttle Insulin Pump',
            'description' => 'Shuttle Health brand Insulin Pump',
        ])
            ->for($manufacturer)
            ->create();

        $diabetesKit = Product::factory([
            'external_id' => 100000003,
            'name' => 'Shuttle Insulin Therapy Kit',
            'description' => 'Shuttle Health brand Insulin Therapy Kit',
        ])
            ->for($manufacturer)
            ->create();

        $products = collect([$cgm, $insulinPump, $diabetesKit]);

        $productCategory->products()->saveMany($products);

        return $products;
    }

    private function createProductsForSHIncontinence(Manufacturer $manufacturer): Collection
    {
        $productCategory = ProductCategory::factory([
            'name' => 'Incontinence',
            'description' => 'Incontinence Products',
        ])->create();

        $underPad = Product::factory([
            'external_id' => 200000001,
            'name' => 'Shuttle Underpad',
            'description' => 'Shuttle Health brand Underpad',
        ])
            ->for($manufacturer)
            ->create();

        $diaper = Product::factory([
            'external_id' => 200000002,
            'name' => 'Shuttle Diaper',
            'description' => 'Shuttle Health brand Diaper',
        ])
            ->for($manufacturer)
            ->create();

        $products = collect([$underPad, $diaper]);

        $productCategory->products()->saveMany($products);

        return $products;
    }

    private function createProductsForSHOstomy(Manufacturer $manufacturer): Collection
    {
        $productCategory = ProductCategory::factory([
            'name' => 'Ostomy',
            'description' => 'Ostomy Products',
        ])->create();

        $ostomySystem = Product::factory([
            'external_id' => '300000001',
            'name' => 'Shuttle Ostomy System',
            'description' => 'Shuttle Health brand Ostomy System',
        ])
            ->for($manufacturer)
            ->create();

        $products = collect([$ostomySystem]);

        $productCategory->products()->saveMany($products);

        return $products;
    }

    private function createProductsForSHUrology(Manufacturer $manufacturer): Collection
    {
        $productCategory = ProductCategory::factory([
            'name' => 'Urology',
            'description' => 'Urology Products',
        ])->create();

        $intermittentCatheter = Product::factory([
            'external_id' => '400000001',
            'name' => 'Shuttle Intermittent Catheter',
            'description' => 'Shuttle Health brand Intermittent Catheter',
        ])
            ->for($manufacturer)
            ->create();

        $foleyCatheter = Product::factory([
            'external_id' => '400000002',
            'name' => 'Shuttle Foley Catheter',
            'description' => 'Shuttle Health brand Foley Catheters',
        ])
            ->for($manufacturer)
            ->create();

        $products = collect([$intermittentCatheter, $foleyCatheter]);

        $productCategory->products()->saveMany($products);

        return $products;
    }

    private function createProductsForSHWoundCare(Manufacturer $manufacturer): Collection
    {
        $productCategory = ProductCategory::factory([
            'name' => 'Wound Care',
            'description' => 'Wound Care Products',
        ])->create();

        $gauze = Product::factory([
            'external_id' => '500000001',
            'name' => 'Shuttle Impregnated Gauze',
            'description' => 'Shuttle Health brand Impregnated Gauze',
        ])
            ->for($manufacturer)
            ->create();

        $basicDressing = Product::factory([
            'external_id' => '500000002',
            'name' => 'Shuttle Basic Dressing',
            'description' => 'Shuttle Health brand Basic Dressing',
        ])
            ->for($manufacturer)
            ->create();

        $compressionDressing = Product::factory([
            'external_id' => '*********',
            'name' => 'Shuttle Compression Dressing',
            'description' => 'Shuttle Health brand Compression Dressing',
        ])
            ->for($manufacturer)
            ->create();

        $products = collect([$gauze, $basicDressing, $compressionDressing]);

        $productCategory->products()->saveMany($products);

        return $products;
    }

    private function createSHDistributor(): Distributor
    {
        $accountOwner = DistributorUser::factory([
            'email' => '<EMAIL>',
            'first_name' => 'Distributor',
            'middle_name' => '',
            'last_name' => 'Admin',
        ])->withRole(DistributorUserTypeEnum::ADMINISTRATOR)->create();

        $distributor = Distributor::factory([
            'name' => 'SH Distribution',
        ])->withNpiRecord()->create();

        $distributor->users()->save($accountOwner, ['owner' => true]);

        $distributor->users()->saveMany(
            DistributorUser::factory()
                ->count(5)
                ->create(),
        );

        return $distributor;
    }

    private function createSHProvider1(): Provider
    {
        $accountOwner = ProviderUser::factory([
            'email' => '<EMAIL>',
            'first_name' => 'Provider',
            'middle_name' => '',
            'last_name' => 'Admin',
        ])->withRole(ProviderUserTypeEnum::ADMINISTRATOR)->create();

        $provider = Provider::factory([
            'name' => 'SH Health Care Providers',
        ])->withNpiRecord()->create();

        $provider->users()->save($accountOwner, ['owner' => true]);

        $provider->users()->saveMany(
            ProviderUser::factory()
                ->withRole(ProviderUserTypeEnum::STAFF)
                ->count(5)
                ->create(),
        );

        $provider->users()->saveMany(
            ProviderUser::factory()
                ->has(NpiRecord::factory())
                ->withRole(ProviderUserTypeEnum::STAFF)
                ->count(3)
                ->create(),
        );

        return $provider;
    }

    private function createSHProvider2(): Provider
    {
        $accountOwner = ProviderUser::factory([
            'email' => '<EMAIL>',
            'first_name' => 'Provider2',
            'middle_name' => '',
            'last_name' => 'Admin',
        ])->withRole(ProviderUserTypeEnum::ADMINISTRATOR)->create();

        $provider = Provider::factory([
            'name' => 'SH Health Care Providers #2',
        ])->withNpiRecord()->create();

        $provider->users()->save($accountOwner, ['owner' => true]);

        $provider->users()->saveMany(
            ProviderUser::factory()
                ->withRole(ProviderUserTypeEnum::STAFF)
                ->count(5)
                ->create(),
        );

        $provider->users()->saveMany(
            ProviderUser::factory()
                ->has(NpiRecord::factory())
                ->withRole(ProviderUserTypeEnum::STAFF)
                ->count(3)
                ->create(),
        );

        return $provider;
    }

    private function createSHManufacturer(): Manufacturer
    {
        $accountOwner = ManufacturerUser::factory([
            'email' => '<EMAIL>',
            'first_name' => 'Manufacturer',
            'middle_name' => '',
            'last_name' => 'Admin',
        ])->withRole(ManufacturerUserTypeEnum::ADMINISTRATOR)->create();

        $manufacturer = Manufacturer::factory(['name' => 'SH Manufacturing'])->create();

        $manufacturer->users()->save($accountOwner, ['owner' => true]);

        $manufacturer->users()->saveMany(
            ManufacturerUser::factory()
                ->count(5)
                ->create(),
        );

        return $manufacturer;
    }

    private function createSHMasterUser(): void
    {
        // create a SH Admin User
        User::factory([
            'email' => '<EMAIL>',
            'first_name' => 'Shuttle',
            'middle_name' => 'Health',
            'last_name' => 'Admin',
        ])->withAdminRole(ShuttleUserTypeEnum::ADMINISTRATOR)->create();
    }

}
