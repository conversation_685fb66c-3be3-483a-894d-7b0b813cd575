<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class CMSDataSeeder extends Seeder
{
    public function run(): void
    {
        // Seed Additional Datasets through Artisan Console Commands - CMS Data
        echo 'HCPCs (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-hcpcs');

        echo 'LCDs (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-lcds');

        echo 'HCPCs <-> LCDs (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-lcd-hcpcs');

        echo 'Articles (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-articles');

        echo 'LCDs <-> Articles (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-lcd-articles');

        echo 'ICD-10s (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:icd-10s');

        echo 'Articles <-> ICD-10s (memory usage: ' . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        Artisan::call('seed-data:cms-article-icd-10s');
    }

    private function getMemoryUsage(): float
    {
        return round(memory_get_usage(true) / 1024 / 1024);
    }
}
