<?php

namespace Database\Seeders;

use App\Enums\MessageConfigurationScenarioEnums;
use App\Models\Distributor;
use App\Models\MessageConfiguration;
use Illuminate\Database\Seeder;

class MessageConfigurationSeeder extends Seeder
{
    protected ?int $distributorId;

    public function __construct(?int $distributorId = null)
    {
        $this->distributorId = $distributorId;
    }

    public function run()
    {
        // If distributor ID is provided, run seeder for specific distributor only
        if ($this->distributorId) {
            $distributors = Distributor::where('id', $this->distributorId)->get();
        } else {
            // Run for all distributors
            $distributors = Distributor::all();
        }

        $scenarios = MessageConfigurationScenarioEnums::cases();

        foreach ($distributors as $distributor) {
            foreach ($scenarios as $scenario) {
                $conditions = $scenario->conditions();

                foreach ($conditions as $condition) {
                    $content = $this->getContentForScenarioCondition($scenario, $condition);

                    if ($content) {
                        $content = $this->replaceLegacyPlaceholders($content);
                    }

                    // Use firstOrCreate to avoid overwriting existing customized content
                    MessageConfiguration::firstOrCreate([
                        'organization_type' => Distributor::class,
                        'organization_id' => $distributor->id,
                        'scenario' => $scenario->value,
                        'condition' => $condition->value,
                    ], [
                        'content' => $content,
                        'placeholders' => null,
                        'active' => !empty($content),
                    ]);
                }
            }
        }
    }

    private function getContentForScenarioCondition($scenario, $condition)
    {
        // Load the translations array
        $translations = trans('communications');

        // Helper to map enum to translation key
        $map = [
            'PENDING_INITIAL_ORDER_REVIEW' => 'pending-initial-order-review',
            'PENDING_BENEFITS_INVESTIGATION' => 'pending-benefits-investigation',
            'PENDING_CUSTOMER_CONTACT' => 'pending-customer-feedback',
            'PENDING_DOCUMENT_COLLECTION' => 'pending-document-collection',
            'PENDING_PRE_AUTHORIZATION' => 'pending-pre-authorization',
            'PENDING_SHIPPING_CONFIRMATION' => 'pending-shipping-confirmation',
            'SHIPPED' => 'shipped_without_tracking',
            // Add more mappings as needed
        ];

        // ORDER_STATUS scenario
        if ($scenario->value === 'ORDER_STATUS') {
            $key = $map[$condition->name] ?? null;

            if ($key && isset($translations['order']['new-prescription'][$key])) {
                return $translations['order']['new-prescription'][$key];
            }
        }

        // ORDER_CANCELLED scenario
        if ($scenario->value === 'ORDER_CANCELLED') {
            $cancelled = $translations['order']['new-prescription']['cancelled'] ?? [];

            if ($condition->name === 'OUT_OF_NETWORK_WITH_THE_PAYER') {
                // Special case: skip, let RE_ROUTED scenario handle rerouted/not_rerouted
                return null;
            }

            if (isset($cancelled[$condition->name])) {
                return $cancelled[$condition->name];
            }
        }

        // RE_ROUTED scenario
        if ($scenario->value === 'RE_ROUTED') {
            $cancelled = $translations['order']['new-prescription']['cancelled'] ?? [];

            if (isset($cancelled['OUT_OF_NETWORK_WITH_THE_PAYER'])) {
                if ($condition->name === 'WITH_RE_ROUTE' && isset($cancelled['OUT_OF_NETWORK_WITH_THE_PAYER']['rerouted'])) {
                    return $cancelled['OUT_OF_NETWORK_WITH_THE_PAYER']['rerouted'];
                }

                if ($condition->name === 'WITHOUT_RE_ROUTE' && isset($cancelled['OUT_OF_NETWORK_WITH_THE_PAYER']['not_rerouted'])) {
                    return $cancelled['OUT_OF_NETWORK_WITH_THE_PAYER']['not_rerouted'];
                }
            }
        }

        // DEFAULT scenario
        if ($scenario->value === 'DEFAULT') {
            if ($condition->name === 'OPT_IN' && isset($translations['patient']['start'])) {
                return $translations['patient']['start'];
            }

            if ($condition->name === 'OPT_OUT' && isset($translations['patient']['stop'])) {
                return $translations['patient']['stop'];
            }

            if ($condition->name === 'HELP_REQUEST' && isset($translations['patient']['help'])) {
                return $translations['patient']['help'];
            }
        }

        // Fallback: null
        return null;
    }

    public function replaceLegacyPlaceholders($message)
    {
        // Replace legacy placeholders with new ones
        $placeholders = [
            ':distributorName:' => '{DISTRIBUTOR_NAME}',
            ':patientName' => '{PATIENT_NAME}',
            ':assignedDistributorName' => '{ASSIGNED_DISTRIBUTOR_NAME}',
            ':distributorPhone' => '{DISTRIBUTOR_PHONE}',
            ':organizationName' => '{ORGANIZATION_NAME}',
            ':trackingNumber' => '{TRACKING_NUMBER}',
            // Add more replacements as needed
        ];

        return strtr($message, $placeholders);
    }
}
