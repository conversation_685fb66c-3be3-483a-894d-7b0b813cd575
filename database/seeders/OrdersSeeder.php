<?php

namespace Database\Seeders;

use App\Models\Distributor;
use App\Models\District;
use App\Models\Manufacturer;
use App\Models\OrderTask;
use App\Models\Provider;
use App\Models\Region;
use App\Models\Territory;
use Carbon\Carbon;
use Database\Factories\OrderSeederFactory;
use Exception;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrdersSeeder extends Seeder
{
    public function __construct()
    {
        ini_set('memory_limit', '2048M');

        DB::disableQueryLog();
    }

    public function run(): void
    {
        $manufacturer = Manufacturer::find(env('MANUFACTURER_ID', 1));

        echo 'Seeding Distributors' . PHP_EOL;

        $distributorNames = ['HealthPlus', 'Aurora Medical', 'Genesis Healthcare'];
        $distributors = [];

        foreach ($distributorNames as $name) {
            $distributor = Distributor::firstWhere('name', $name);

            if (!$distributor) {
                $distributor = Distributor::factory(['name' => $name])->withGenericOwner()->create();
                $distributor->products()->sync($manufacturer->products);
            }

            $distributors[] = $distributor;
        }

        $distributorIds = collect($distributors)->pluck('id')->toArray();

        echo 'Seeding Regions' . PHP_EOL;

        for ($i = 1; $i <= 3; $i++) {
            DB::beginTransaction();
            try {
                Region::factory()->for($manufacturer)->create();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                echo "Iteration: $i, skipped (region factory)" . PHP_EOL;
            }

            echo "Iteration: $i (memory usage: " . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
        }

        echo 'Seeding Districts' . PHP_EOL;

        for ($i = 1; $i <= 20; $i++) {
            DB::beginTransaction();
            try {
                District::factory()->for($manufacturer)->withRandomExistingRegion($manufacturer)->create();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                echo "Iteration: $i, skipped (district factory)" . PHP_EOL;
            }

            if ($i % 25 == 0) {
                echo "Iteration: $i (memory usage: " . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
            }
        }

        echo 'Seeding Territories' . PHP_EOL;

        for ($i = 1; $i <= 200; $i++) {
            DB::beginTransaction();
            try {
                Territory::factory()
                    ->for($manufacturer)
                    ->withRandomExistingDistrict($manufacturer)
                    ->withZipCodes(random_int(1, 10))
                    ->create();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                echo "Iteration: $i, skipped (territory factory)" . PHP_EOL;
            }

            if ($i % 100 == 0) {
                echo "Iteration: $i (memory usage: " . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
            }
        }

        echo 'Seeding Providers' . PHP_EOL;

        $providers = collect();

        for ($i = 1; $i <= 200; $i++) {
            DB::beginTransaction();
            try {
                $zipCode = $manufacturer?->territories()?->inRandomOrder()?->first()?->zipCodes()?->inRandomOrder()?->first();
                $provider = Provider::factory()->withGenericOwner()->withAddress(['zip' => $zipCode->zip_code])->create();

                $providers->push($provider);

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                echo "Iteration: $i, skipped (provider factory)" . PHP_EOL;
            }

            if ($i % 100 == 0) {
                echo "Iteration: $i (memory usage: " . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
            }
        }

        echo 'Seeding Orders' . PHP_EOL;

        for ($i = 1; $i <= 25000; $i++) {
            DB::beginTransaction();
            try {
                $order = OrderSeederFactory::new()
                    ->withRandomCreatedAtDate(Carbon::now()->subMonths(3))
                    ->withRandomDistributorFromList($distributorIds)
                    ->withRandomProviderFromList($providers->pluck('id')->toArray())
                    ->withDistributorPatientAndManufacturerTerritoryZipCode($manufacturer)
                    ->withProviderPatient()
                    ->withLogStatusCreated()
                    ->withOrderShippingAndCancellationsAccordingToStatuses()
                    ->create();

                OrderTask::factory(['user_id' => $order->provider_user_id])->for($order)->count(3)->create();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                echo "Iteration: $i, skipped (order factory)" . PHP_EOL;
            }

            if ($i % 100 == 0) {
                echo "Iteration: $i (memory usage: " . $this->getMemoryUsage() . ' MB)' . PHP_EOL;
            }
        }
    }

    private function getMemoryUsage(): float
    {
        return round(memory_get_usage(true) / 1024 / 1024);
    }
}
