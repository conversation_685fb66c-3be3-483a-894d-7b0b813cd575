<?php

namespace Database\Seeders;

use App\Enums\UserPermissionEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $permissions = UserPermissionEnum::getValues();

        collect($permissions)->each(function ($permission) {
            $checkPermission = Permission::where('name', $permission)->first();

            if (!$checkPermission) {
                DB::table(config('permission.table_names.permissions'))->insert([
                    'name' => $permission,
                    'guard_name' => config('auth.defaults.guard'),
                ]);
            }

        });

        $organizations = [
            [
                'name' => 'provider',
                'roles' => [
                    [
                        'name' => 'provider-staff',
                        'title' => 'Office Staff',
                    ],
                    [
                        'name' => 'provider-administrator',
                        'title' => 'Office Administrator',
                    ],
                ],
            ],
            [
                'name' => 'distributor',
                'roles' => [
                    [
                        'name' => 'distributor-staff',
                        'title' => 'Operations Users',
                    ],
                    [
                        'name' => 'distributor-manager',
                        'title' => 'Operations Manager',
                    ],
                    [
                        'name' => 'distributor-administrator',
                        'title' => 'Administrator',
                    ],
                    [
                        'name' => 'distributor-external-user',
                        'title' => 'External (Manufacturer) User',
                    ],
                ],
            ],
            [
                'name' => 'manufacturer',
                'roles' => [
                    [
                        'name' => 'manufacturer-staff',
                        'title' => 'Office Staff',
                    ],
                    [
                        'name' => 'manufacturer-manager',
                        'title' => 'Office Manager',
                    ],
                    [
                        'name' => 'manufacturer-administrator',
                        'title' => 'Administrator',
                    ],
                    [
                        'name' => 'manufacturer-territory-manager',
                        'title' => 'Territory Manager',
                    ],
                    [
                        'name' => 'manufacturer-district-manager',
                        'title' => 'District Manager',
                    ],
                    [
                        'name' => 'manufacturer-region-manager',
                        'title' => 'Region Manager',
                    ],
                    [
                        'name' => 'manufacturer-sales-representative',
                        'title' => 'Sales Representative',
                    ],
                ],
            ],
            [
                'name' => 'shuttle-health',
                'roles' => [
                    [
                        'name' => 'shuttle-administrator',
                        'title' => 'Shuttle Health Administrator',
                    ],
                    [
                        'name' => 'shuttle-sales',
                        'title' => 'Shuttle Health Sales',
                    ],
                    [
                        'name' => 'shuttle-support',
                        'title' => 'Shuttle Health Support',
                    ],
                ],
            ],
            [
                'name' => 'none',
                'roles' => [
                    [
                        'name' => 'user',
                        'title' => 'User',
                    ],
                ],
            ],
        ];

        collect($organizations)->each(function ($organization) {
            collect($organization['roles'])->each(function ($role) {
                $exists = DB::table(config('permission.table_names.roles'))->where([
                    'name' => $role['name'],
                    'title' => $role['title'],
                ])->first();

                if (!$exists) {
                    $roleId = DB::table(config('permission.table_names.roles'))->insertGetId([
                        'name' => $role['name'],
                        'title' => $role['title'],
                        'guard_name' => config('auth.defaults.guard'),
                    ]);

                    if (isset($role['permissions'])) {
                        collect($role['permissions'])->each(function ($permission) use ($roleId) {
                            $permissionId = DB::table(config('permission.table_names.permissions'))
                                ->where('name', '=', $permission)
                                ->first()
                                ->id;

                            DB::table(config('permission.table_names.role_has_permissions'))->insert([
                                'role_id' => $roleId,
                                'permission_id' => $permissionId,
                            ]);
                        });
                    }
                }
            });
        });
    }
}
