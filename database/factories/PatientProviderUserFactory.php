<?php

namespace Database\Factories;

use App\Enums\PatientProviderUserAssociationType;
use App\Models\Distributor;
use App\Models\DistributorUser;
use App\Models\Patient;
use App\Models\ProviderUser;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PatientProviderUser>
 */
class PatientProviderUserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $distributor = Distributor::factory();

        return [
            'patient_id' => Patient::factory(),
            'updated_by' => DistributorUser::factory()->for($distributor),
            'provider_user_id' => ProviderUser::factory(),
            'association_type' => $this->faker->randomElement(PatientProviderUserAssociationType::cases()),
            'distributor_id' => $distributor,
        ];
    }
}
