<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SWFaxLogFactory extends Factory
{
    public function definition(): array
    {
        $faxLogId = $this->faker->uuid;
        $to = $this->faker->e164PhoneNumber;
        $from = $this->faker->e164PhoneNumber;
        $status = $this->faker->randomElement(['queued', 'sent', 'failed', 'received']);
        $direction = $this->faker->randomElement(['inbound', 'outbound']);
        $faxLogCreatedAt = $this->faker->dateTimeBetween('-1 month', 'now');

        return [
            'fax_log_id' => $faxLogId,
            'to' => $to,
            'from' => $from,
            'status' => $status,
            'direction' => $direction,
            'fax_log_created_at' => $faxLogCreatedAt,
            'fax_log_metadata' => json_encode([
                'fax_log_id' => $faxLogId,
                'to' => $to,
                'from' => $from,
                'status' => $status,
                'direction' => $direction,
                'fax_log_created_at' => $faxLogCreatedAt->format('Y-m-d H:i:s'), // or use ->toISOString()
            ]),
        ];
    }
}
