<?php

namespace Database\Factories;

use App\Models\DistributorEmailTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class DistributorEmailTemplateFactory extends Factory
{
    protected $model = DistributorEmailTemplate::class;

    public function definition()
    {
        return [
            'name' => $this->faker->sentence(3),
            'html_content' => '<h1>' . $this->faker->word . '</h1>',
            'json_content' => json_encode(['greeting' => $this->faker->word]),
            'type' => 'standard',
            // 'distributor_id' and 'created_by' must be set explicitly in tests!
            'distributor_id' => null,
            'created_by' => null,
            'updated_by' => null,
        ];
    }
}
