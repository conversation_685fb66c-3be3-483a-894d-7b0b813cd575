<?php

namespace Database\Factories;

use App\Enums\ActivityLogTypeEnum;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ActivityLogFactory extends Factory
{
    public function definition(): array
    {
        $type = $this->faker->randomElement(ActivityLogTypeEnum::cases());

        $metadata = match ($type) {
            ActivityLogTypeEnum::ADD_PRODUCT,
            ActivityLogTypeEnum::UPDATE_PRODUCT,
            ActivityLogTypeEnum::REMOVE_PRODUCT => ['product_id' => 'Product Id'],
            ActivityLogTypeEnum::CANCELED => ['cancellationReason' => 'No longer interested'],
            ActivityLogTypeEnum::MESSAGE => ['message' => 'This is a user message'],
            ActivityLogTypeEnum::UPDATE_DETAILS => ['orderData' => 'Array of field changes'],
            ActivityLogTypeEnum::UPDATE_DISTRIBUTOR => ['distributor_id' => 'Distributor Id'],
            ActivityLogTypeEnum::UPDATE_STATUS => ['status' => 'New Status'],
            ActivityLogTypeEnum::CUSTOM => ['type' => 'New type', 'description' => 'desc'],
            ActivityLogTypeEnum::UPDATE_QUALIFIED_DATE => ['from' => now()->toDateString(), 'to' => now()->addDay()->toDateString()],
            default => [],
        };

        return [
            'activityable_id' => Order::factory(),
            'activityable_type' => Order::class,
            'user_id' => User::factory(),
            'type' => $type,
            'activity_at' => Carbon::now(),
            'metadata' => $metadata,
        ];
    }
}
