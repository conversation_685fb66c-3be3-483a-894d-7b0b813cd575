<?php

namespace Database\Factories;

use App\Enums\FileTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class FileFactory extends Factory
{
    public function definition(): array
    {
        return [
            'uuid' => $this->faker->uuid(),
            'type' => $this->faker->randomElement(FileTypeEnum::cases()),
            'file_name' => $this->faker->name() . '.png',
            'relation_id' => null,
            'extension' => 'png',
        ];
    }
}
