<?php

namespace Database\Factories;

use App\Enums\CustomFieldTypeEnum;
use App\Models\DistributorCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomFieldFactory extends Factory
{
    public function definition(): array
    {
        return [
            'distributor_campaign_id' => DistributorCampaign::factory(),
            'display_name' => $this->faker->words(2, true),
            'system_name' => $this->faker->regexify('[a-zA-Z0-9_]{5,15}'),
            'field_type' => $this->faker->randomElement(CustomFieldTypeEnum::values()),
        ];
    }

    public function string(): CustomFieldFactory
    {
        return $this->state(function () {
            return [
                'field_type' => CustomFieldTypeEnum::TEXT,
            ];
        });
    }

    public function number(): CustomFieldFactory
    {
        return $this->state(function () {
            return [
                'field_type' => CustomFieldTypeEnum::NUMBER,
            ];
        });
    }

    public function date(): CustomFieldFactory
    {
        return $this->state(function () {
            return [
                'field_type' => CustomFieldTypeEnum::DATE,
            ];
        });
    }

    public function withUniqueNames(string $prefix = ''): CustomFieldFactory
    {
        return $this->state(function () use ($prefix) {
            $uniqueId = $this->faker->unique()->numberBetween(1000, 9999);

            return [
                'display_name' => $prefix . 'Display Field ' . $uniqueId,
                'system_name' => $prefix . 'field_' . $uniqueId,
            ];
        });
    }
}
