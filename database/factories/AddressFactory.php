<?php

namespace Database\Factories;

use App\Enums\AddressTypeEnum;
use App\Utils\AddressStateConverter;
use Illuminate\Database\Eloquent\Factories\Factory;

class AddressFactory extends Factory
{
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement(AddressTypeEnum::cases()),
            'address_line_1' => $this->faker->streetAddress(),
            'address_line_2' => 'Suite A',
            'city' => $this->faker->city(),
            'state' => $this->faker->randomElement(AddressStateConverter::getShortValuesList()),
            'zip' => $this->faker->postcode,
        ];
    }
}
