<?php

namespace Database\Factories;

use App\Enums\ManufacturerUserTypeEnum;
use App\Models\District;
use App\Models\Manufacturer;
use App\Models\ManufacturerUser;
use App\Models\Territory;
use App\Models\ZipCode;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class TerritoryFactory extends Factory
{
    public function definition(): array
    {
        $name = ucwords($this->faker->unique()->words(3, true));

        return [
            'name' => $name,
            'code' => Str::slug($name),
            'district_id' => District::factory(),
            'organization_id' => Manufacturer::factory(),
            'organization_type' => Manufacturer::class,
            'manager_id' => ManufacturerUser::factory()->withRole(ManufacturerUserTypeEnum::TERRITORY_MANAGER),
        ];
    }

    public function withRandomExistingDistrict(Manufacturer $manufacturer): TerritoryFactory
    {
        return $this->state(function () use ($manufacturer) {
            $district = District::where('manufacturer_id', $manufacturer->id)->inRandomOrder()->first();

            return [
                'district_id' => $district->id,
            ];
        });
    }

    public function withZipCodes(int $count = 1): TerritoryFactory
    {
        return $this->afterCreating(function (Territory $territory) use ($count) {
            $territory->zipCodes()->attach(ZipCode::factory()->count($count)->create());
        });
    }
}
