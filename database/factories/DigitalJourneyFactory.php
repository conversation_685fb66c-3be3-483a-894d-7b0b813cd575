<?php

namespace Database\Factories;

use App\Enums\DigitalJourneyTemplateTypeEnum;
use App\Models\DistributorCampaign;
use App\Models\MessageTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class DigitalJourneyFactory extends Factory
{
    public function definition()
    {
        $campaignFactory = DistributorCampaign::factory();

        return [
            'distributor_campaign_id' => $campaignFactory,
            'template_id' => MessageTemplate::factory()->for($campaignFactory),
            'template_type' => DigitalJourneyTemplateTypeEnum::MESSAGE->value,
            'days_from_created_date' => $this->faker->numberBetween(1, 10),
        ];
    }
}
