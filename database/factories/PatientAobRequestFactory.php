<?php

namespace Database\Factories;

use App\Enums\DistributorAobTemplateNameEnum;
use App\Models\DocumentRequest;
use App\Models\Patient;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PatientAobRequestFactory extends Factory
{
    public function definition()
    {
        return [
            'patient_id' => Patient::factory(),
            'document_request_id' => DocumentRequest::factory(),
            'token' => Str::uuid(),
            'aob_template_name' => $this->faker->randomElement(DistributorAobTemplateNameEnum::cases()),
        ];
    }
}
