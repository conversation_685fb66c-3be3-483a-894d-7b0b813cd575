<?php

namespace Database\Factories;

use App\Enums\OrderEscalationStatusEnum;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderEscalationFactory extends Factory
{
    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'assigned_order_id' => Order::factory(),
            'comment' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(OrderEscalationStatusEnum::cases()),
            'updated_by' => User::factory(),
            'assigned_distributor_at' => now(),
        ];
    }
}
