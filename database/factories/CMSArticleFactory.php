<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CMSArticle>
 */
class CMSArticleFactory extends Factory
{
    public function definition()
    {
        return [
            'cms_id' => $this->faker->numberBetween(1, 99999),
            'cms_version' => $this->faker->numberBetween(1, 99),
            'type' => 1,
            'title' => $this->faker->text(),
            'description' => $this->faker->text(),
            'status' => $this->faker->randomElement(['A']),
            'publish_date' => $this->faker->dateTime(),
            'effective_start_date' => $this->faker->dateTime(),
            'effective_end_date' => $this->faker->dateTime(),
            'update_date' => $this->faker->dateTime(),
        ];
    }
}
