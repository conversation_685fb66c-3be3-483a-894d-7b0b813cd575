<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderShippingFactory extends Factory
{
    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'tracking_number' => $this->faker->numberBetween(100000, 999999),
            'additional_info' => null,
            'shipped_at' => now(),
        ];
    }
}
