<?php

namespace Database\Factories;

use App\Enums\ManufacturerUserTypeEnum;
use App\Models\Manufacturer;
use App\Models\ManufacturerUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class RegionFactory extends Factory
{
    public function definition(): array
    {
        $name = ucwords($this->faker->unique()->words(1, true));

        return [
            'name' => $name,
            'code' => Str::slug($name),
            'organization_id' => Manufacturer::factory(),
            'organization_type' => Manufacturer::class,
            'manager_id' => ManufacturerUser::factory()->withRole(ManufacturerUserTypeEnum::REGION_MANAGER),
        ];
    }
}
