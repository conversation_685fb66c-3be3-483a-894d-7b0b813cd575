<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\MessageConfiguration;
use Illuminate\Database\Eloquent\Factories\Factory;

class MessageConfigurationFactory extends Factory
{
    protected $model = MessageConfiguration::class;

    public function definition()
    {
        return [
            'organization_type' => Distributor::class,
            'organization_id' => 1, // override as needed
            'scenario' => 'ORDER_CANCELLED',
            'condition' => 'DUPLICATE_ORDER',
            'content' => $this->faker->sentence,
            'placeholders' => null,
            'active' => true,
        ];
    }
}
