<?php

namespace Database\Factories;

use App\Models\MedicalPolicyForm;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MedicalPolicyResponse>
 */
class MedicalPolicyResponseFactory extends Factory
{
    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'medical_policy_form_id' => MedicalPolicyForm::factory(),
            'form_data' => [['type' => 'input', 'id' => 'input1', 'name' => 'variable1']],
        ];
    }
}
