<?php

namespace Database\Factories;

use App\Enums\OrderTaskStatusEnum;
use App\Enums\OrderTaskTypeEnum;
use App\Models\DistributorUser;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderTaskFactory extends Factory
{
    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'user_id' => DistributorUser::factory(),
            'request_id' => null, // Default to null for manual tasks
            'type' => $this->faker->randomElement(OrderTaskTypeEnum::cases()),
            'status' => $this->faker->randomElement(OrderTaskStatusEnum::cases()),
            'is_automated' => false, // Default to false for manual tasks
            'metadata' => [
                'task_name' => $this->faker->title,
                'initial_request_date' => $this->faker->date,
                'complete_date' => $this->faker->date,
            ],
        ];
    }
}
