<?php

namespace Database\Factories;

use App\Enums\FaxStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FaxStatusHistory>
 */
class FaxStatusHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $examplePayload = [
            'FaxSid' => $this->faker->uuid(),
            'AccountSid' => $this->faker->uuid(),
            'From' => $this->faker->e164PhoneNumber, // Example: +***********
            'To' => $this->faker->e164PhoneNumber, // Example: +***********
            'RemoteStationId' => $this->faker->e164PhoneNumber,
            'FaxStatus' => FaxStatusEnum::RECEIVED->value,
            'ApiVersion' => 'v1',
            'NumPages' => $this->faker->numberBetween(1, 10),
            'MediaSid' => $this->faker->uuid(),
            'MediaUrl' => $this->faker->url(),
            'Timestamp' => $this->faker->dateTimeThisYear()->format('Y-m-d\TH:i:s\Z'),
        ];

        return [
            'fax_sid' => $examplePayload['FaxSid'],
            'fax_status' => $examplePayload['FaxStatus'],
            'fax_status_timestamps' => $examplePayload['Timestamp'],
            'fax_status_description' => FaxStatusEnum::getFaxStatusDescription($examplePayload['FaxStatus']),
            'fax_metadata' => json_encode($examplePayload),
        ];
    }

}
