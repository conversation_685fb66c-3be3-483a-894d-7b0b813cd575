<?php

namespace Database\Factories;

use App\Models\FilterSet;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FilterSet>
 */
class FilterSetFactory extends Factory
{
    protected $model = FilterSet::class;

    public function definition(): array
    {
        return [
            'title' => Str::limit(fake()->sentence(6), 255),
            'description' => fake()->realText(250),
            'view' => 'dashboard',
            'filters' => json_encode([
                'status' => 'active',
                'roles' => ['admin', 'editor'],
            ]),
            'user_id' => User::factory(), // Ensure you have a UserFactory
        ];
    }
}
