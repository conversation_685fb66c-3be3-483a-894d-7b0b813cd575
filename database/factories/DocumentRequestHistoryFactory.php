<?php

namespace Database\Factories;

use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Models\DocumentRequest;
use App\Models\DocumentRequestHistory;
use App\Models\Fax;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DocumentRequestHistoryFactory extends Factory
{
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement(DocumentRequestHistoryTypeEnum::cases()),
            'details' => ['message' => $this->faker->word],
            'document_request_id' => DocumentRequest::factory(),
            'user_id' => User::factory(),
            'fax_id' => Fax::factory(),
            'activity_at' => $this->faker->dateTime(),
        ];
    }

    public function withLabDocument(): DocumentRequestHistoryFactory
    {
        return $this->afterCreating(function (DocumentRequestHistory $requestHistory) {
            PatientDocumentFactory::factory()->create([
                'order_id' => $requestHistory->order_id,
                'document_request_id' => $requestHistory->id,
                'type' => OrderDocumentTypeEnum::LAB,
                'source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER,
                'details' => [
                    'lab_names' => [
                        'labOne',
                        'labTwo',
                    ],
                ],
                'uploaded_by' => $requestHistory->user_id,
            ]);
        });
    }
}
