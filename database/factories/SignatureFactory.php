<?php

namespace Database\Factories;

use App\Enums\FileTypeEnum;
use App\Models\File;
use App\Models\Signature;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SignatureFactory extends Factory
{
    public function definition(): array
    {
        return [
            'full_name' => $this->faker->firstName() . $this->faker->firstName(),
            'signable_id' => User::factory(),
            'signable_type' => User::class,
        ];
    }

    public function withFile(): SignatureFactory
    {
        return $this->afterCreating(function (Signature $signature) {
            File::factory()->create([
                'relation_id' => $signature->id,
                'type' => FileTypeEnum::SIGNATURE_IMAGE,
                'extension' => 'png',
            ]);
        });
    }
}
