<?php

namespace Database\Factories;

use App\Enums\FaxDirectionEnum;
use App\Enums\FaxReviewStatusEnum;
use App\Enums\FaxStatusEnum;
use App\Enums\FileTypeEnum;
use App\Models\Distributor;
use App\Models\Fax;
use App\Models\File;
use Illuminate\Database\Eloquent\Factories\Factory;

class FaxFactory extends Factory
{
    public function definition(): array
    {
        return [
            'distributor_id' => Distributor::factory(),
            'fax_uuid' => $this->faker->uuid(),
            'review_status' => $this->faker->randomElement(FaxReviewStatusEnum::cases()),
            'fax_status' => $this->faker->randomElement(FaxStatusEnum::cases()),
            'direction' => $this->faker->randomElement(FaxDirectionEnum::cases()),
            'from' => '123456' . $this->faker->numberBetween(1000, 9999),
            'to' => '123456' . $this->faker->numberBetween(1000, 9999),
            'pages_count' => $this->faker->numberBetween(1, 5),
            'retry_count' => 0,
        ];
    }

    public function withFile(): FaxFactory
    {
        return $this->afterCreating(function (Fax $fax) {
            File::factory()->create([
                'relation_id' => $fax->id,
                'type' => FileTypeEnum::FAX,
                'extension' => 'pdf',
            ]);
        });
    }
}
