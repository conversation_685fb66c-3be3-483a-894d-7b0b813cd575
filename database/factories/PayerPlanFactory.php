<?php

namespace Database\Factories;

use App\Enums\PayerPlanTypeEnum;
use App\Models\Payer;
use Illuminate\Database\Eloquent\Factories\Factory;

class PayerPlanFactory extends Factory
{
    public function definition()
    {
        return [
            'payer_id' => Payer::factory(),
            'name' => $this->faker->company(),
            'payer_plan_type' => $this->faker->randomElement(PayerPlanTypeEnum::cases()),
        ];
    }
}
