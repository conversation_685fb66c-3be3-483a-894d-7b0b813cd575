<?php

namespace Database\Factories;

use App\Enums\ProviderUserPositionEnum;
use App\Enums\ProviderUserTypeEnum;
use App\Models\NpiRecord;
use App\Models\ProviderUser;

class ProviderUserFactory extends UserFactory
{
    // TODO: review use of entity_user_type, possibly delete
    public function definition(): array
    {
        return array_merge(
            parent::definition(),
            [
                'position_type' => $this->faker->randomElement(ProviderUserPositionEnum::cases()),
                'entity_user_type' => ProviderUser::class,
            ],
        );
    }

    public function withRole(ProviderUserTypeEnum $role): ProviderUserFactory
    {
        return $this->afterCreating(function (ProviderUser $user) use ($role) {
            $user->assignRole($role->value);
        });
    }

    public function withNpiRecord(array $attributes = []): ProviderUserFactory
    {
        return $this->afterCreating(function (ProviderUser $user) use ($attributes) {
            $npiRecord = NpiRecord::factory()->make($attributes);
            $user->npiRecord()->save($npiRecord);
        });
    }
}
