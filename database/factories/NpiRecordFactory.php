<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\Provider;
use App\Models\User;
use App\Utils\PhoneNumberConverter;
use App\Utils\ZipCodeConverter;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\Factory;

class NpiRecordFactory extends Factory
{
    public function definition()
    {
        return [
            'npi' => $this->faker->numberBetween(**********, **********),
            'attested_on' => CarbonImmutable::now()->subMinute(),
            'verified_on' => CarbonImmutable::now(),
            'addresses' => [
                [
                    'country_code' => 'US',
                    'country_name' => 'United States',
                    'address_purpose' => 'MAILING',
                    'address_type' => 'DOM',
                    'address_1' => $this->faker->streetAddress,
                    'city' => $this->faker->city,
                    'state' => $this->faker->stateAbbr,
                    'postal_code' => ZipCodeConverter::fromNumeric($this->faker->postcode),
                    'telephone_number' => PhoneNumberConverter::cleanPhoneNumber($this->faker->phoneNumber),
                    'fax_number' => PhoneNumberConverter::cleanPhoneNumber($this->faker->phoneNumber),
                ],
                [
                    'country_code' => 'US',
                    'country_name' => 'United States',
                    'address_purpose' => 'LOCATION',
                    'address_type' => 'DOM',
                    'address_1' => $this->faker->streetAddress,
                    'city' => $this->faker->city,
                    'state' => $this->faker->stateAbbr,
                    'postal_code' => ZipCodeConverter::fromNumeric($this->faker->postcode),
                    'telephone_number' => PhoneNumberConverter::cleanPhoneNumber($this->faker->phoneNumber),
                    'fax_number' => PhoneNumberConverter::cleanPhoneNumber($this->faker->phoneNumber),
                ],
            ],
        ];
    }

    public function provider($provider)
    {
        return $this->state(function (array $attributes) use ($provider) {
            return array_merge(
                $attributes,
                [
                    'npiable_type' => Provider::class,
                    'npiable_id' => $provider->id,
                ],
            );
        });
    }

    public function distributor($distributor)
    {
        return $this->state(function (array $attributes) use ($distributor) {
            return array_merge(
                $attributes,
                [
                    'npiable_type' => Distributor::class,
                    'npiable_id' => $distributor->id,
                ],
            );
        });
    }

    public function user(User $user)
    {
        return $this->state(function (array $attributes) use ($user) {
            return array_merge(
                $attributes,
                [
                    'npiable_type' => User::class,
                    'npiable_id' => $user->id,
                ],
            );
        });
    }
}
