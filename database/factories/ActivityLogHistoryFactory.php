<?php

namespace Database\Factories;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ActivityLogHistoryFactory extends Factory
{
    public function definition(): array
    {
        return [
            'activity_log_id' => ActivityLog::factory(),
            'user_id' => User::factory(),
            'activity_at' => Carbon::now(),
            'old_values' => ['metadata' => 'Old metadata'],
            'new_values' => ['metadata' => 'New metadata'],
        ];
    }
}
