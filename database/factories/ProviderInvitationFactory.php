<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\Provider;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProviderInvitationFactory extends Factory
{
    public function definition(): array
    {
        return [
            'distributor_id' => Distributor::factory(),
            'provider_id' => Provider::factory(),
            'invited_by' => User::factory(),
            'sent_at' => $this->faker->dateTime(),
        ];
    }
}
