<?php

namespace Database\Factories;

use App\Models\CustomField;
use App\Models\Lead;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeadCustomFieldFactory extends Factory
{
    public function definition(): array
    {
        return [
            'lead_id' => Lead::factory(),
            'custom_field_id' => CustomField::factory(),
            'value' => $this->faker->words(3, true),
        ];
    }

    public function withStringValue(): LeadCustomFieldFactory
    {
        return $this->state(function () {
            return [
                'value' => $this->faker->words(3, true),
            ];
        });
    }

    public function withNumberValue(): LeadCustomFieldFactory
    {
        return $this->state(function () {
            return [
                'value' => (string) $this->faker->numberBetween(1, 1000),
            ];
        });
    }

    public function withDateValue(): LeadCustomFieldFactory
    {
        return $this->state(function () {
            return [
                'value' => $this->faker->date(),
            ];
        });
    }

    public function withEmptyValue(): LeadCustomFieldFactory
    {
        return $this->state(function () {
            return [
                'value' => '',
            ];
        });
    }

    public function withLongValue(): LeadCustomFieldFactory
    {
        return $this->state(function () {
            return [
                'value' => $this->faker->text(500),
            ];
        });
    }
}
