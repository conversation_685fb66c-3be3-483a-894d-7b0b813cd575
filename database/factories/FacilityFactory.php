<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\ProviderUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class FacilityFactory extends Factory
{
    public function definition(): array
    {
        return [
            'distributor_id' => Distributor::factory()->withGenericOwner(),
            'user_id' => ProviderUser::factory(),
            'phone' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'fax' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'address_line_1' => $this->faker->streetAddress(),
            'address_line_2' => 'Suite A',
            'city' => $this->faker->city(),
            'state' => strtoupper(Str::random(rand(2, 2))),
            'zip' => substr($this->faker->postcode, 0, 5),
        ];
    }
}
