<?php

namespace Database\Factories;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\AddressTypeEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\OrganizationTypeEnum;
use App\Models\ActivityLog;
use App\Models\Distributor;
use App\Models\GlobalPatient;
use App\Models\Manufacturer;
use App\Models\Order;
use App\Models\OrderCancellation;
use App\Models\OrderShipping;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\ProviderUser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

/**
 * Special for dashboard order seeding
 */
class OrderSeederFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'status' => $this->faker->randomElement(OrderStatusEnum::cases()),
            'type' => $this->faker->randomElement(OrderTypeEnum::cases()),
            'global_patient_id' => GlobalPatient::factory(),
            'provider_id' => Provider::factory()->withGenericOwner(),
            'provider_user_id' => ProviderUser::factory(),
            'distributor_id' => Distributor::factory()->withGenericOwner(),
            'due_on' => todayTz()->addDays(45),
            'created_by' => ProviderUser::factory(),
        ];
    }

    public function withRandomCreatedAtDate(?Carbon $dateFrom = null, ?Carbon $dateTo = null): OrderSeederFactory
    {
        $dateFrom = $dateFrom ?: now()->subMonths(3);
        $dateTo = $dateTo ?: now();

        return $this->state(function () use ($dateFrom, $dateTo) {
            $randomDate = Carbon::createFromTimestamp(rand($dateFrom->timestamp, $dateTo->timestamp));

            return [
                'created_at' => $randomDate,
                'updated_at' => $randomDate,
            ];
        });
    }

    public function withLogStatusCreated(): OrderSeederFactory
    {
        return $this->afterCreating(function (Order $order) {
            ActivityLog::factory([
                'activityable_id' => $order->id,
                'activityable_type' => Order::class,
                'user_id' => $order->provider?->owner?->id,
                'type' => ActivityLogTypeEnum::CREATED,
                'activity_at' => $order->created_at,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])->create();
        });
    }

    public function withOrderShippingAndCancellationsAccordingToStatuses(): OrderSeederFactory
    {
        return $this->afterCreating(function (Order $order) {
            // use the following statuses to make order cancellations
            if (in_array($order->status, [
                OrderStatusEnum::DISTRIBUTOR_CANCELED,
                OrderStatusEnum::PROVIDER_CANCELED,
                OrderStatusEnum::MANUFACTURER_CANCELED,
            ])) {
                ActivityLog::factory()->for($order, 'activityable')->create([
                    'user_id' => $order->provider?->owner?->id,
                    'type' => ActivityLogTypeEnum::UPDATE_STATUS,
                    'metadata' => ['status' => $order['status']],
                ]);

                OrderCancellation::factory()->for($order)->create([
                    'created_at' => $order->created_at,
                    'updated_at' => $order->created_at,
                ]);
            }

            // use the following statuses to increase the number of shipped orders
            if (in_array($order->status, [
                OrderStatusEnum::APPROVED,
                OrderStatusEnum::NEEDS_MORE_INFORMATION,
                OrderStatusEnum::PENDING_BENEFITS_INVESTIGATION,
                OrderStatusEnum::PENDING_CLINICAL_DOCUMENTATION,
                OrderStatusEnum::PENDING_CUSTOMER_CONTACT,
                OrderStatusEnum::PENDING_DISTRIBUTOR_SELECTION,
                OrderStatusEnum::PENDING_DOCUMENT_COLLECTION,
                OrderStatusEnum::PENDING_FACILITY_ONBOARDING,
                OrderStatusEnum::PENDING_INITIAL_ORDER_REVIEW,
                OrderStatusEnum::PENDING_PATIENT_EVALUATION,
                OrderStatusEnum::PENDING_PRE_AUTHORIZATION,
            ])) {
                $shippingDate = $order->created_at->addDays(random_int(5, 10));

                OrderShipping::factory(['shipped_at' => $shippingDate])->for($order)->create();

                $order->update(['status' => OrderStatusEnum::SHIPPED]);

                ActivityLog::factory()->for($order, 'activityable')->create([
                    'user_id' => $order->provider?->owner?->id,
                    'type' => ActivityLogTypeEnum::UPDATE_STATUS,
                    'metadata' => ['status' => $order['status']],
                    'activity_at' => $shippingDate,
                ]);
            }
        });
    }

    public function withRandomDistributorFromList(array $distributorIds): OrderSeederFactory
    {
        return $this->state(function () use ($distributorIds) {
            $randomDistributorId = $distributorIds[array_rand($distributorIds)];

            return [
                'distributor_id' => $randomDistributorId,
            ];
        });
    }

    public function withRandomProviderFromList(array $providerIds): OrderSeederFactory
    {
        return $this->state(function () use ($providerIds) {
            $randomProviderId = Arr::random($providerIds);
            $provider = Provider::find($randomProviderId);

            $providerUser = $provider->users()->inRandomOrder()->first();

            return [
                'provider_id' => $provider->id,
                'provider_user_id' => $providerUser->id,
                'created_by' => $providerUser->id,
            ];
        });
    }

    public function withRandomExistingProvider(): OrderSeederFactory
    {
        return $this->state(function () {
            $provider = Provider::inRandomOrder()->first();
            $providerUser = $provider->users()->inRandomOrder()->first();

            return [
                'provider_id' => $provider->id,
                'provider_user_id' => $providerUser->id,
                'created_by' => $providerUser->id,
            ];
        });
    }

    public function withDistributorPatient(array $attributes = []): OrderSeederFactory
    {
        return $this->afterCreating(function (Order $order) use ($attributes) {
            Patient::factory([
                'global_patient_id' => $order->global_patient_id,
                'organization_type' => OrganizationTypeEnum::DISTRIBUTOR,
                'organization_id' => $order->distributor_id,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])
                ->withAddress(['type' => AddressTypeEnum::MAILING])
                ->withRandomExistingPrimaryPayer()
                ->create($attributes);
        });
    }

    public function withProviderPatient(array $attributes = []): OrderSeederFactory
    {
        return $this->afterCreating(function (Order $order) use ($attributes) {
            Patient::factory([
                'global_patient_id' => $order->global_patient_id,
                'organization_type' => OrganizationTypeEnum::PROVIDER,
                'organization_id' => $order->provider_id,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])
                ->withAddress(['type' => AddressTypeEnum::MAILING])
                ->withRandomExistingPrimaryPayer()
                ->create($attributes);
        });
    }

    public function withDistributorPatientAndManufacturerTerritoryZipCode(Manufacturer $manufacturer): OrderSeederFactory
    {
        return $this->afterCreating(function (Order $order) use ($manufacturer) {
            $zipCode = $manufacturer?->territories()?->inRandomOrder()?->first()?->zipCodes()?->inRandomOrder()?->first();

            Patient::factory([
                'global_patient_id' => $order->global_patient_id,
                'organization_type' => OrganizationTypeEnum::DISTRIBUTOR,
                'organization_id' => $order->distributor_id,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])
                ->withAddress([
                    'type' => AddressTypeEnum::MAILING,
                    'zip' => $zipCode->zip_code,
                ])
                ->withRandomExistingPrimaryPayer()
                ->create();
        });
    }
}
