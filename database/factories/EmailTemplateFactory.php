<?php

namespace Database\Factories;

use App\Models\DistributorCampaign;
use App\Models\DistributorEmailTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmailTemplateFactory extends Factory
{
    public function definition()
    {
        return [
            'distributor_campaign_id' => DistributorCampaign::factory(),
            'distributor_template_id' => DistributorEmailTemplate::factory(),
            'is_active' => $this->faker->boolean(),
            'email' => $this->faker->email(),
        ];
    }
}
