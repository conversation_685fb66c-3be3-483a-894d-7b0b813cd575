<?php

namespace Database\Factories;

use App\Enums\OrganizationTypeEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PatientStatus;
use App\Enums\PatientTimezoneEnum;
use App\Enums\PayerTypeEnum;
use App\Models\Address;
use App\Models\Distributor;
use App\Models\GlobalPatient;
use App\Models\Patient;
use App\Models\Payer;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends Factory<Patient> */
class PatientFactory extends Factory
{
    public function definition(): array
    {
        // Generate salutation as free text (30% chance of null)
        $salutation = null;

        if (rand(1, 10) > 3) {
            $salutations = ['Mr.', 'Mrs.', 'Ms.', 'Miss', 'Dr.', 'Prof.', 'Rev.', 'Hon.'];
            $salutation = $this->faker->randomElement($salutations);
        }

        // Generate suffix as free text (80% chance of null)
        $suffix = null;

        if (rand(1, 10) > 8) {
            $suffixes = ['Jr.', 'Sr.', 'II', 'III', 'IV', 'V', 'Esq.', 'MD', 'PhD', 'RN', 'CPA', 'DDS'];
            $suffix = $this->faker->randomElement($suffixes);
        }

        return [
            'salutation' => $salutation,
            'first_name' => $this->faker->firstName(),
            'middle_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'suffix' => $suffix,
            'date_of_birth' => $this->faker->dateTime(),
            'gender' => $this->faker->randomElement(['M', 'F']),
            'email' => $this->faker->email(),
            'phone_cell' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'sms_enabled' => $this->faker->boolean(),
            'email_enabled' => $this->faker->boolean(),
            'call_enabled' => $this->faker->boolean(),
            'timezone' => $this->faker->randomElement(PatientTimezoneEnum::cases()),
            'global_patient_id' => GlobalPatient::factory(),
            'organization_type' => OrganizationTypeEnum::DISTRIBUTOR,
            'organization_id' => Distributor::factory(),
            'status' => PatientStatus::ACTIVE,
        ];
    }

    public function withAddress(array $attributes = []): PatientFactory
    {
        return $this->afterCreating(function (Patient $patient) use ($attributes) {
            Address::factory([
                'addressable_type' => Patient::class,
                'addressable_id' => $patient->id,
            ])->create($attributes);
        });
    }

    public function withPrimaryPayer(array $attributes = [], array $pivotAttributes = []): PatientFactory
    {
        return $this->afterCreating(function (Patient $patient) use ($attributes, $pivotAttributes) {
            $payer = Payer::factory()->create($attributes);

            $defaultPivotAttributes = [
                'user_defined_payer_type' => $this->faker->randomElement(PayerTypeEnum::getValues()),
                'priority' => PatientPayerPriorityEnum::PRIMARY,
                'policy_number' => '**********',
                'group_number' => '**********',
            ];

            $patient->payers()->attach($payer, array_merge($defaultPivotAttributes, $pivotAttributes));
        });
    }

    public function withCaregiver(): PatientFactory
    {
        return $this->afterCreating(function (Patient $patient) {
            $patient->caregiver()->create([
                $patient->getForeignKey() => $patient->id,
                'name' => $this->faker->name(),
                'relationship' => 'Brother',
                'mobile' => '123456' . random_int(1000, 9999),
                'email' => $this->faker->email(),
            ]);
        });
    }

    public function withCreatedDaysAgo(int $daysAgo): PatientFactory
    {
        return $this->state(function () use ($daysAgo) {
            return [
                'created_at' => now()->subDays($daysAgo),
                'updated_at' => now()->subDays($daysAgo),
            ];
        });
    }

    public function withRandomExistingPrimaryPayer(): PatientFactory
    {
        return $this->afterCreating(function (Patient $patient) {
            $randomPayer = Payer::inRandomOrder()->first();

            $patient->payers()->attach($randomPayer, [
                'priority' => PatientPayerPriorityEnum::PRIMARY,
                'policy_number' => '**********',
                'group_number' => '**********',
            ]);
        });
    }
}
