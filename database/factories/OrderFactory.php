<?php

namespace Database\Factories;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\InternalSourceEnum;
use App\Enums\OrderSourceEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\OrganizationTypeEnum;
use App\Models\ActivityLog;
use App\Models\Distributor;
use App\Models\GlobalPatient;
use App\Models\NpiRecord;
use App\Models\Order;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\ProviderUser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    public function definition(): array
    {
        $provider = Provider::factory()->withGenericOwner()->create();
        $providerUserFactory = ProviderUser::factory()->hasAttached($provider)->has(NpiRecord::factory());

        return [
            'status' => OrderStatusEnum::PENDING_INITIAL_ORDER_REVIEW,
            'type' => OrderTypeEnum::NEW_PRESCRIPTION,
            'global_patient_id' => GlobalPatient::factory(),
            'provider_id' => $provider->id,
            'provider_user_id' => $providerUserFactory,
            'distributor_id' => Distributor::factory()->withGenericOwner(),
            'due_on' => now()->addDays(45),
            'created_by' => $providerUserFactory,
            'created_by_organization_type' => OrganizationTypeEnum::PROVIDER,
            'created_by_organization_id' => $provider->id,
            'source' => OrderSourceEnum::OTHER,
            'source_description' => $this->faker->text(10),
            'source_editable' => true,
            'internal_source' => InternalSourceEnum::DISTRIBUTOR,
        ];
    }

    public function withCreatedAt(Carbon $date): OrderFactory
    {
        return $this->state(function () use ($date) {
            return [
                'created_at' => $date->toDateString(),
                'updated_at' => $date->toDateString(),
            ];
        });
    }

    public function withCreatedDaysAgo(int $daysAgo): OrderFactory
    {
        return $this->state(function () use ($daysAgo) {
            return [
                'created_at' => now()->subDays($daysAgo),
                'updated_at' => now()->subDays($daysAgo),
            ];
        });
    }

    public function withRandomCreatedAtDate(?Carbon $dateFrom = null, ?Carbon $dateTo = null): OrderFactory
    {
        $dateFrom = $dateFrom ?: now()->subMonths(3);
        $dateTo = $dateTo ?: now();

        return $this->state(function () use ($dateFrom, $dateTo) {
            $randomDate = Carbon::createFromTimestamp(rand($dateFrom->timestamp, $dateTo->timestamp));

            return [
                'created_at' => $randomDate,
                'updated_at' => $randomDate,
            ];
        });
    }

    public function withLogStatusCreated(): OrderFactory
    {
        return $this->afterCreating(function (Order $order) {
            ActivityLog::factory([
                'activityable_id' => $order->id,
                'activityable_type' => Order::class,
                'user_id' => $order->provider?->owner?->id,
                'type' => ActivityLogTypeEnum::CREATED,
                'activity_at' => $order->created_at,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])->create();
        });
    }

    public function withDistributorPatient(array $attributes = []): OrderFactory
    {
        return $this->afterCreating(function (Order $order) use ($attributes) {
            Patient::factory([
                'global_patient_id' => $order->global_patient_id,
                'organization_type' => OrganizationTypeEnum::DISTRIBUTOR,
                'organization_id' => $order->distributor_id,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])
                ->withAddress()
                ->withRandomExistingPrimaryPayer()
                ->create($attributes);
        });
    }

    public function withProviderPatient(array $attributes = []): OrderFactory
    {
        return $this->afterCreating(function (Order $order) use ($attributes) {
            Patient::factory([
                'global_patient_id' => $order->global_patient_id,
                'organization_type' => OrganizationTypeEnum::PROVIDER,
                'organization_id' => $order->provider_id,
                'created_at' => $order->created_at,
                'updated_at' => $order->created_at,
            ])
                ->withAddress()
                ->withRandomExistingPrimaryPayer()
                ->create($attributes);
        });
    }

    public function withShipping(int $days): OrderFactory
    {
        return $this->afterCreating(function (Order $order) use ($days) {
            $order->update(['status' => OrderStatusEnum::SHIPPED]);

            ActivityLog::factory()->for($order, 'activityable')->create([
                'type' => ActivityLogTypeEnum::UPDATE_STATUS,
                'metadata' => ['status' => $order->status],
                'activity_at' => $order->created_at->addDays($days),
            ]);
        });
    }
}
