<?php

namespace Database\Factories;

use App\Enums\ManufacturerUserTypeEnum;
use App\Models\Manufacturer;
use App\Models\ManufacturerUser;
use App\Models\Region;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class DistrictFactory extends Factory
{
    public function definition(): array
    {
        $name = ucwords($this->faker->unique()->words(2, true));

        return [
            'name' => $name,
            'code' => Str::slug($name),
            'region_id' => Region::factory(),
            'organization_id' => Manufacturer::factory(),
            'organization_type' => Manufacturer::class,
            'manager_id' => ManufacturerUser::factory()->withRole(ManufacturerUserTypeEnum::DISTRICT_MANAGER),
        ];
    }

    public function withRandomExistingRegion(Manufacturer $manufacturer): DistrictFactory
    {
        return $this->state(function () use ($manufacturer) {
            $region = Region::where('manufacturer_id', $manufacturer->id)->inRandomOrder()->first();

            return [
                'region_id' => $region->id,
            ];
        });
    }
}
