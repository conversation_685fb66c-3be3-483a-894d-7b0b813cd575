<?php

namespace Database\Factories;

use App\Enums\PayerTypeEnum;
use App\Models\Payer;
use App\Models\PayerService;
use Illuminate\Database\Eloquent\Factories\Factory;

class PayerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'internal_type' => $this->faker->randomElement(PayerTypeEnum::cases()),
            'custom' => false,
        ];
    }

    public function withService(array $attributes = []): PayerFactory
    {
        return $this->afterCreating(function (Payer $payer) use ($attributes) {
            PayerService::factory()->for($payer)->create($attributes);
        });
    }
}
