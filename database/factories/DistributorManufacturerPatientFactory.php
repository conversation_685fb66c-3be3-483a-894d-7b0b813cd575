<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Patient;
use Illuminate\Database\Eloquent\Factories\Factory;

class DistributorManufacturerPatientFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'distributor_id' => Distributor::factory(),
            'patient_id' => Patient::factory(),
            'manufacturer_id' => Manufacturer::factory(),
            'manufacturer_patient_id' => $this->faker->randomNumber(5),
        ];
    }
}
