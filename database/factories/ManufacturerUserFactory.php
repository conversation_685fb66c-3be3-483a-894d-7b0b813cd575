<?php

namespace Database\Factories;

use App\Enums\ManufacturerUserTypeEnum;
use App\Models\ManufacturerUser;

class ManufacturerUserFactory extends UserFactory
{
    // TODO: review use of entity_user_type, possibly delete
    public function definition(): array
    {
        return array_merge(
            parent::definition(),
            ['entity_user_type' => ManufacturerUser::class],
        );
    }

    public function withRole(ManufacturerUserTypeEnum $role): ManufacturerUserFactory
    {
        return $this->afterCreating(function (ManufacturerUser $user) use ($role) {
            $user->assignRole($role->value);
        });
    }
}
