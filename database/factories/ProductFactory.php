<?php

namespace Database\Factories;

use App\Enums\ProductDurationUnitEnum;
use App\Enums\ProductMeasureUnitEnum;
use App\Enums\ProductNarrativeUnitEnum;
use App\Enums\ProductQuantityTypeEnum;
use App\Models\Manufacturer;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    public function definition()
    {
        return [
            'manufacturer_id' => Manufacturer::factory(),
            'external_id' => $this->faker->numberBetween(1000000000, 9999999999),
            'name' => $this->faker->name(),
            'description' => '',
            'quantity_type' => ProductQuantityTypeEnum::NARRATIVE->value,
            'default_narrative_unit' => ProductNarrativeUnitEnum::CHANGE_EVERY->value,
            'default_narrative_measure_unit' => ProductDurationUnitEnum::DAYS->value,
            'default_narrative_measure_count' => 1,
            'inclusion_narrative' => true,
            'narrative_text' => null,
            'default_measure_count' => 1,
            'default_measure_unit' => ProductMeasureUnitEnum::EACHES->value,
            'default_duration_count' => 1,
            'default_duration_unit' => ProductDurationUnitEnum::LIFETIME->value,
        ];
    }
}
