<?php

namespace Database\Factories;

use App\Models\Patient;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use OwenIt\Auditing\Models\Audit;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\OwenIt\Auditing\Models\Audit>
 */
class PatientAuditFactory extends Factory
{
    protected $model = Audit::class;

    public function definition(): array
    {
        return [
            'user_type' => User::class,
            'user_id' => User::factory(), // use factory for dynamic ID
            'event' => 'updated',
            'auditable_type' => Patient::class,
            'auditable_id' => Patient::factory(), // use factory for dynamic ID
            'old_values' => [
                'salutation' => 'Mrs.',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'suffix' => 'Jr.',
            ],
            'new_values' => [
                'salutation' => 'Mr.',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'suffix' => 'Sr.',
            ],
            'url' => $this->faker->url(),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'tags' => ['patient_update'], // JSON array, assuming json/jsonb column
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
