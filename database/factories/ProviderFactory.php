<?php

namespace Database\Factories;

use App\Enums\AddressTypeEnum;
use App\Enums\ProviderUserTypeEnum;
use App\Models\Address;
use App\Models\NpiRecord;
use App\Models\Provider;
use App\Models\ProviderUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProviderFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'phone' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'fax' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'created_by' => User::factory(),
        ];
    }

    public function withNpiRecord(): ProviderFactory
    {
        return $this->afterCreating(function (Provider $provider) {
            NpiRecord::factory()
                ->provider($provider)
                ->create();
        });
    }

    public function withGenericOwner(): ProviderFactory
    {
        return $this->afterCreating(function (Provider $provider) {
            $accountOwner = ProviderUser::factory()
                ->withRole(ProviderUserTypeEnum::ADMINISTRATOR)
                ->create();

            $provider->users()->save($accountOwner, ['owner' => true]);
        });
    }

    public function withAddress(array $attributes = []): ProviderFactory
    {
        return $this->afterCreating(function (Provider $provider) use ($attributes) {
            Address::factory([
                'type' => AddressTypeEnum::MAILING,
                'addressable_type' => Provider::class,
                'addressable_id' => $provider->id,
            ])->create($attributes);
        });
    }
}
