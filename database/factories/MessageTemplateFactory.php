<?php

namespace Database\Factories;

use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Models\DistributorCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class MessageTemplateFactory extends Factory
{
    public function definition()
    {
        return [
            'distributor_campaign_id' => DistributorCampaign::factory(),
            'name' => $this->faker->word(),
            'body' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(MessageTemplateStatusEnum::getValues()),
            'type' => $this->faker->randomElement(MessageTemplateTypeEnum::getValues()),
            'is_active' => $this->faker->boolean(),
            'phone_number' => (int) "123456{$this->faker->numberBetween(1000, 9999)}",
        ];
    }
}
