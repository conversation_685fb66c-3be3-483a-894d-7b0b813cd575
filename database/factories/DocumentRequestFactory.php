<?php

namespace Database\Factories;

use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Models\Distributor;
use App\Models\DocumentRequest;
use App\Models\DocumentRequestHistory;
use App\Models\GlobalPatient;
use App\Models\NpiRecord;
use App\Models\Provider;
use App\Models\ProviderUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DocumentRequestFactory extends Factory
{
    public function definition(): array
    {
        $provider = Provider::factory()->withGenericOwner()->create();
        $providerUserFactory = ProviderUser::factory()->hasAttached($provider)->has(NpiRecord::factory());

        return [
            'request_type' => $this->faker->randomElement(DocumentRequestRequestTypeEnum::cases()),
            'status' => $this->faker->randomElement(DocumentRequestStatusEnum::cases()),
            'type' => $this->faker->randomElement(DocumentRequestTypeEnum::cases()),
            'global_patient_id' => GlobalPatient::factory(),
            'provider_id' => $provider->id,
            'provider_user_id' => $providerUserFactory,
            'distributor_id' => Distributor::factory()->withGenericOwner(),
            'created_by' => User::factory(),
            'date_needed' => $this->faker->dateTime(),
            'follow_up_at' => $this->faker->dateTime(),
            'is_digital' => $this->faker->boolean(),
        ];
    }

    public function withHistory(?User $user = null): DocumentRequestFactory
    {
        return $this->afterCreating(function (DocumentRequest $documentRequest) use ($user) {
            DocumentRequestHistory::factory()->for($documentRequest)->create([
                'type' => DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG,
                'user_id' => $user ? $user->id : User::factory(),
                'activity_at' => $documentRequest->created_at,
            ]);
        });
    }
}
