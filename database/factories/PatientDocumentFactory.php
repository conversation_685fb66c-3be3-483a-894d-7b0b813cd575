<?php

namespace Database\Factories;

use App\Enums\FileTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Models\Distributor;
use App\Models\DocumentRequest;
use App\Models\File;
use App\Models\Order;
use App\Models\PatientDocument;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PatientDocumentFactory extends Factory
{
    public function definition(): array
    {
        return [
            'title' => $this->faker->word(),
            'type' => $this->faker->randomElement(OrderDocumentTypeEnum::cases()),
            'source' => $this->faker->randomElement(OrderDocumentSourceEnum::cases()),
            'order_id' => Order::factory(),
            'document_request_id' => DocumentRequest::factory(),
            'uploaded_by' => User::factory(),
            'expiration_date' => $this->faker->date(),
            'created_by_organization_type' => Distributor::class,
            'created_by_organization_id' => Distributor::factory(),
        ];
    }

    public function withFile(): PatientDocumentFactory
    {
        return $this->afterCreating(function (PatientDocument $orderDocument) {
            File::factory()->create([
                'relation_id' => $orderDocument->id,
                'type' => FileTypeEnum::ORDER_DOCUMENT,
                'extension' => 'pdf',
            ]);
        });
    }

    public function withOrderPivot(): PatientDocumentFactory
    {
        return $this->afterCreating(function (PatientDocument $patientDocument) {
            // If the document has an order_id, create the pivot table entry as well
            if ($patientDocument->order_id) {
                $order = Order::find($patientDocument->order_id);

                if ($order) {
                    $order->documents()->attach($patientDocument->id);
                }
            }
        });
    }
}
