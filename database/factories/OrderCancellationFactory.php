<?php

namespace Database\Factories;

use App\Enums\OrderCancellationReasonEnum;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderCancellationFactory extends Factory
{
    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'reason' => $this->faker->randomElement(OrderCancellationReasonEnum::cases()),
            'additional_info' => $this->faker->sentence(),
        ];
    }

    public function withCreatedDaysAgo(int $daysAgo): OrderCancellationFactory
    {
        return $this->state(function () use ($daysAgo) {
            return [
                'created_at' => now()->subDays($daysAgo),
                'updated_at' => now()->subDays($daysAgo),
            ];
        });
    }
}
