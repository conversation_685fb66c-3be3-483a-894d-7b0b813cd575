<?php

namespace Database\Factories;

use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\DistributorCampaignTypeEnum;
use App\Models\Distributor;
use App\Models\DistributorUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class DistributorCampaignFactory extends Factory
{
    public function definition()
    {
        $distributor = Distributor::factory()->withGenericOwner();

        return [
            'distributor_id' => $distributor,
            'created_by' => DistributorUser::factory()->hasAttached($distributor),
            'name' => $this->faker->sentence,
            'status' => $this->faker->randomElement(DistributorCampaignStatusEnum::getValues()),
            'type' => DistributorCampaignTypeEnum::LEAD, // NOTE: when order type will be implemented $this->faker->randomElement(DistributorCampaignTypeEnum::getValues()),
            'opt_in_text' => $this->faker->sentence,
            'opt_out_text' => $this->faker->sentence,
            'help_text' => $this->faker->sentence,
            'auto_reply_text' => $this->faker->sentence,
        ];
    }
}
