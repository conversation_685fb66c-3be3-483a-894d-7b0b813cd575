<?php

namespace Database\Factories;

use App\Models\CMSHCPC;
use Illuminate\Database\Eloquent\Factories\Factory;

class CMSHCPCFactory extends Factory
{
    private function generateCode(): string
    {
        return strtoupper($this->faker->randomLetter()) . $this->faker->numberBetween(1, 9999);
    }

    public function definition(): array
    {
        // NOTE; ensure there are no conflicts with generated HCPC Codes (was happening intermittently)
        $code = $this->generateCode();
        while (count(CMSHCPC::where('code', $code)->get())) {
            $code = $this->generateCode();
        }

        return [
            'code' => $code,
            'seq_num' => "00{$this->faker->numberBetween(1, 3)}0",
            'rec_id' => $this->faker->numberBetween(1, 9),
            'short_description' => $this->faker->jobTitle(),
            'long_description' => $this->faker->text(),
        ];
    }
}
