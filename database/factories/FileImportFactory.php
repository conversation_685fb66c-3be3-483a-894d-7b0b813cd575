<?php

namespace Database\Factories;

use App\Enums\FileImportStatusEnum;
use App\Enums\FileTypeEnum;
use App\Models\File;
use App\Models\FileImport;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class FileImportFactory extends Factory
{
    public function definition(): array
    {
        return [
            'file_type' => 'lab',
            'status' => FileImportStatusEnum::VALIDATED,
            'validation_time' => $this->faker->randomFloat(2, 0, 100),
            'execution_time' => $this->faker->randomFloat(2, 0, 100),
            'error_rows' => [],
            'metadata' => [],
            'created_by' => User::factory(),
        ];
    }

    public function withFile(array $attributes = []): FileImportFactory
    {
        return $this->afterCreating(function (FileImport $fileImport) use ($attributes) {
            File::factory()->create(array_merge([
                'relation_id' => $fileImport->id,
                'type' => FileTypeEnum::IMPORT,
                'extension' => 'csv',
            ], $attributes));
        });
    }
}
