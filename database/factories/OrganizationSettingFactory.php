<?php

namespace Database\Factories;

use App\Enums\OrganizationSettingNameEnum;
use App\Enums\ValueTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganizationSettingFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => OrganizationSettingNameEnum::DAYS_TO_SEND_FAXES,
            'value_type' => ValueTypeEnum::INTEGER,
            'value' => $this->faker->numberBetween(1, 100),
        ];
    }
}
