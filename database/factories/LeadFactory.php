<?php

namespace Database\Factories;

use App\Enums\LeadQualityRankEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\PatientGenderEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PatientPayerStatusEnum;
use App\Enums\PayerTypeEnum;
use App\Models\ActivityLog;
use App\Models\Address;
use App\Models\CustomField;
use App\Models\DistributorCampaign;
use App\Models\Lead;
use App\Models\LeadCustomField;
use App\Models\NpiRecord;
use App\Models\Payer;
use App\Models\Product;
use App\Models\ProviderUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeadFactory extends Factory
{
    public function definition()
    {
        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'gender' => $this->faker->randomElement(PatientGenderEnum::getValues()),
            'date_of_birth' => $this->faker->dateTime(),
            'email' => $this->faker->email(),
            'mobile' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'zip' => $this->faker->postcode,
            'sms_enabled' => false, // there is LeadObserver that will sent notification if communications are true on create
            'email_enabled' => false,
            'call_enabled' => false,
            'communications_text' => $this->faker->text(30),

            'payer_type' => $this->faker->randomElement(PayerTypeEnum::getValues()),
            'source' => $this->faker->text(20),
            'source_url' => $this->faker->url(),
            'marketing_id' => $this->faker->text(15),
            'created_date' => todayTz()->toDateString(),
            'follow_up_at' => todayTz()->toDateString(),
            'qualified_date' => todayTz()->toDateString(),
            'status' => LeadStatusEnum::OPEN->value,
            'quality_rank' => LeadQualityRankEnum::MEDIUM->value,
            'distributor_campaign_id' => DistributorCampaign::factory(),
            'provider_user_id' => ProviderUser::factory()->has(NpiRecord::factory()),
        ];
    }

    public function withAddress(array $attributes = []): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($attributes) {
            $address = Address::factory()->make($attributes);
            $lead->addresses()->save($address);
        });
    }

    public function withPrimaryPayer(array $attributes = [], array $pivotAttributes = []): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($attributes, $pivotAttributes) {
            $payer = Payer::factory()->create($attributes);

            $lead->payers()->attach($payer, array_merge([
                'user_defined_payer_type' => $this->faker->randomElement(PayerTypeEnum::getValues()),
                'priority' => PatientPayerPriorityEnum::PRIMARY,
                'policy_number' => '**********',
                'group_number' => '**********',
                'status' => PatientPayerStatusEnum::ACTIVE,
            ], $pivotAttributes));
        });
    }

    public function withProduct(array $attributes = [], array $pivotAttributes = []): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($attributes, $pivotAttributes) {
            $product = Product::factory()->create($attributes);

            $lead->products()->attach($product, $pivotAttributes);
        });
    }

    public function withActivityLog(array $attributes = []): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($attributes) {
            $activityLog = ActivityLog::factory()->make($attributes);
            $lead->activityLogs()->save($activityLog);
        });
    }

    public function withCustomFields(array $customFields = []): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($customFields) {
            if (empty($customFields)) {
                // Create 2 default custom fields with values
                CustomField::factory()
                    ->count(2)
                    ->for($lead->distributorCampaign)
                    ->create()
                    ->each(function ($customField) use ($lead) {
                        LeadCustomField::factory()
                            ->for($lead)
                            ->for($customField)
                            ->create();
                    });
            } else {
                // Use provided custom fields
                foreach ($customFields as $fieldData) {
                    $customField = $fieldData['custom_field'] ?? CustomField::factory()->for($lead->distributorCampaign)->create();
                    $value = $fieldData['value'] ?? $this->faker->words(3, true);

                    LeadCustomField::factory()
                        ->for($lead)
                        ->for($customField)
                        ->create(['value' => $value]);
                }
            }
        });
    }

    public function withSpecificCustomFields(array $fieldDefinitions): LeadFactory
    {
        return $this->afterCreating(function (Lead $lead) use ($fieldDefinitions) {
            foreach ($fieldDefinitions as $definition) {
                $customField = CustomField::factory()
                    ->for($lead->distributorCampaign)
                    ->create([
                        'display_name' => $definition['display_name'],
                        'system_name' => $definition['system_name'],
                        'field_type' => $definition['field_type'] ?? 'string',
                    ]);

                LeadCustomField::factory()
                    ->for($lead)
                    ->for($customField)
                    ->create(['value' => $definition['value'] ?? '']);
            }
        });
    }
}
