<?php

namespace Database\Factories;

use App\Enums\ShuttleUserTypeEnum;
use App\Models\NpiRecord;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'middle_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'mobile' => "123456{$this->faker->numberBetween(1000, 9999)}",
            'phone' => "123456{$this->faker->numberBetween(1000, 9999)}",
            'fax' => "123456{$this->faker->numberBetween(1000, 9999)}",
            'password' => Hash::make(config('auth.default_password')),
            'remember_token' => Str::random(10),
            'notifications' => $this->faker->boolean(),
            'is_system' => false,
            'cognito_id' => (string) Str::uuid(),
        ];
    }

    public function withAdminRole(ShuttleUserTypeEnum $role): UserFactory
    {
        return $this->afterCreating(function (User $user) use ($role) {
            $user->assignRole($role->value);
        });
    }

    public function withNpi(int $npi): UserFactory
    {
        return $this->afterCreating(function (User $user) use ($npi) {
            NpiRecord::factory()->user($user)->create(['npi' => $npi]);
        });
    }
}
