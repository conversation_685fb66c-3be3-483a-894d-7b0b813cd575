<?php

namespace Database\Factories;

use App\Enums\DistributorUserTypeEnum;
use App\Models\Distributor;
use App\Models\DistributorUser;
use App\Models\NpiRecord;
use Illuminate\Database\Eloquent\Factories\Factory;

class DistributorFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'phone' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'fax' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
            'assigned_fax' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(001, 999)}{$this->faker->numberBetween(0001, 9999)}",
        ];
    }

    public function withNpiRecord(): DistributorFactory
    {
        return $this->afterCreating(function (Distributor $distributor) {
            NpiRecord::factory()
                ->distributor($distributor)
                ->create();
        });
    }

    public function withGenericOwner(): DistributorFactory
    {
        return $this->afterCreating(function (Distributor $distributor) {
            $accountOwner = DistributorUser::factory()
                ->withRole(DistributorUserTypeEnum::ADMINISTRATOR)
                ->create();

            $distributor->users()->save($accountOwner, ['owner' => true]);
        });
    }
}
