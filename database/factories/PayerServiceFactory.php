<?php

namespace Database\Factories;

use App\Enums\PayerServiceTypesEnum;
use App\Models\Payer;
use Illuminate\Database\Eloquent\Factories\Factory;

class PayerServiceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'payer_id' => Payer::factory(),
            'payer_service_type' => $this->faker->randomElement([PayerServiceTypesEnum::CHANGEHEALTHCARE, PayerServiceTypesEnum::PVERIFY]),
            'eligibility_id' => $this->faker->numberBetween(0, 999999),
        ];
    }
}
