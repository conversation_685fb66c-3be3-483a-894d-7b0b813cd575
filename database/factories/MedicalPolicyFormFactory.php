<?php

namespace Database\Factories;

use App\Enums\OrderTypeEnum;
use App\Enums\PayerTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MedicalPolicyForm>
 */
class MedicalPolicyFormFactory extends Factory
{
    public function definition()
    {
        return [
            'form_schema' => [['type' => 'input', 'id' => 'input1', 'name' => 'variable1']],
            'form_name' => $this->faker->company,
            'form_description' => $this->faker->text,
            'order_type' => OrderTypeEnum::NEW_PRESCRIPTION,
            'payer_type' => PayerTypeEnum::COMMERCIAL,
        ];
    }
}
