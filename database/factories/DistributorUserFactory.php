<?php

namespace Database\Factories;

use App\Enums\DistributorUserTypeEnum;
use App\Models\DistributorUser;

class DistributorUserFactory extends UserFactory
{
    // TODO: review use of entity_user_type, possibly delete
    public function definition(): array
    {
        return array_merge(
            parent::definition(),
            ['entity_user_type' => DistributorUser::class],
        );
    }

    public function withRole(DistributorUserTypeEnum $role): DistributorUserFactory
    {
        return $this->afterCreating(function (DistributorUser $user) use ($role) {
            $user->assignRole($role->value);
        });
    }
}
