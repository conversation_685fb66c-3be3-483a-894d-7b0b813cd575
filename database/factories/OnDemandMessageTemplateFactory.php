<?php

namespace Database\Factories;

use App\Models\Distributor;
use App\Models\OnDemandMessageTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class OnDemandMessageTemplateFactory extends Factory
{
    protected $model = OnDemandMessageTemplate::class;


    public function definition()
    {
        return [
            'distributor_id' => Distributor::factory(),
            'name' => $this->faker->word(),
            'body' => $this->faker->sentence(),
            'is_active' => $this->faker->boolean(),
        ];
    }
}
