<?php

namespace Database\Factories;

use App\Models\Payer;
use App\Models\PharmacyPayer;
use Illuminate\Database\Eloquent\Factories\Factory;

class PharmacyPayerFactory extends Factory
{
    protected $model = PharmacyPayer::class;

    public function definition()
    {
        return [
            'payer_id' => Payer::factory(),
            'policy_number' => $this->faker->bothify('POL####'),
            'notes' => $this->faker->optional()->sentence,
            // payable_id and payable_type are set via ->for($model, 'payable') in tests
        ];
    }
}
