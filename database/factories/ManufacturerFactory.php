<?php

namespace Database\Factories;

use App\Enums\ManufacturerUserTypeEnum;
use App\Models\Manufacturer;
use App\Models\ManufacturerUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class ManufacturerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'phone' => "1{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(1000, 9999)}",
            'fax' => "{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(1000, 9999)}",
            'assigned_fax' => "{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(100, 999)}{$this->faker->numberBetween(1000, 9999)}",
        ];
    }

    public function withGenericOwner()
    {
        return $this->afterCreating(function (Manufacturer $manufacturer) {
            $accountOwner = ManufacturerUser::factory()
                ->withRole(ManufacturerUserTypeEnum::ADMINISTRATOR)
                ->create();

            $manufacturer->users()->save($accountOwner, ['owner' => true]);
        });
    }
}
