<?php

namespace Database\Factories;

use App\Enums\QuicksightEnum;
use App\Models\Distributor;
use App\Models\QuicksightUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QuicksightUser>
 */
class QuicksightUserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = QuicksightUser::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $aws_region = config('services.quicksight.region');

        return [
            'distributor_id' => Distributor::factory(),
            'user_id' => User::factory(),
            'arn' => "arn:aws:quicksight:{$aws_region}:" . $this->faker->numerify('############') . ':user/default/' . $this->faker->userName,
            'username' => $this->faker->userName,
            'role' => $this->faker->randomElement(QuicksightEnum::cases()),
            'metadata' => [
                'created_by' => 'factory',
                'environment' => 'test',
            ],
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the QuickSight user is a reader.
     */
    public function reader(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => QuicksightEnum::READER,
        ]);
    }

    /**
     * Indicate that the QuickSight user is an author.
     */
    public function author(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => QuicksightEnum::AUTHOR,
        ]);
    }

    /**
     * Indicate that the QuickSight user is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a QuickSight user for a specific distributor.
     */
    public function forDistributor(Distributor $distributor): static
    {
        return $this->state(fn (array $attributes) => [
            'distributor_id' => $distributor->id,
        ]);
    }

    /**
     * Create a QuickSight user for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }
}
