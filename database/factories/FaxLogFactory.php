<?php

namespace Database\Factories;

use App\Enums\DocumentRequestTypeEnum;
use App\Models\FaxLog;
use App\Models\Patient;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class FaxLogFactory extends Factory
{
    protected $model = FaxLog::class;

    public function definition(): array
    {
        return [
            'fax_log_id' => 'FAX_' . $this->faker->unique()->randomNumber(8),
            'to' => $this->faker->phoneNumber,
            'from' => $this->faker->phoneNumber,
            'status' => $this->faker->randomElement(['received', 'processed', 'failed']),
            'direction' => 'inbound',
            'fax_log_created_at' => $this->faker->dateTimeThisMonth,
            'fax_log_metadata' => [
                'pages' => $this->faker->numberBetween(1, 10),
                'size' => $this->faker->numberBetween(100000, 1000000),
            ],
            'document_type' => $this->faker->randomElement([
                DocumentRequestTypeEnum::CMN->value,
                DocumentRequestTypeEnum::CHART_NOTES->value,
                DocumentRequestTypeEnum::CUSTOM->value,
                DocumentRequestTypeEnum::LAB->value,
                'unidentified',
            ]),
            's3_bucket' => 'test-fax-bucket',
            's3_key' => 'unprocessed-faxes/' . $this->faker->uuid() . '.pdf',
            'ai_extracted_data' => [
                'patient' => [
                    'first_name' => $this->faker->firstName,
                    'last_name' => $this->faker->lastName,
                    'date_of_birth' => $this->faker->date('Y-m-d', '2000-01-01'),
                ],
                'extracted_at' => now()->toISOString(),
            ],
            'processing_notes' => null,
            'identified' => $this->faker->boolean(70),
            'match_method' => $this->faker->randomElement(['none', 'exact', 'fuzzy']),
            'match_score' => $this->faker->optional(0.7)->randomFloat(3, 0, 1),
            'idempotency_key' => $this->faker->uuid(),
            'audit_ref' => [
                'ingested_at' => now()->toISOString(),
                'source' => 'singlewire_api',
            ],
            'human_override_applied' => false,
        ];
    }

    public function identified(): static
    {
        return $this->state(fn (array $attributes) => [
            'identified' => true,
            'match_method' => $this->faker->randomElement(['exact', 'fuzzy']),
            'match_score' => $this->faker->randomFloat(3, 0.7, 1.0),
            'matched_at' => now(),
            'patient_id' => Patient::factory(),
        ]);
    }

    public function unidentified(): static
    {
        return $this->state(fn (array $attributes) => [
            'identified' => false,
            'match_method' => 'none',
            'match_score' => null,
            'matched_at' => null,
            'patient_id' => null,
        ]);
    }

    public function validated(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_accurate_identification' => $this->faker->boolean(85),
            'validated_by_user_id' => User::factory(),
            'validated_at' => now(),
        ]);
    }

    public function withCorrections(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_accurate_identification' => false,
            'feedback_notes' => $this->faker->sentence(),
            'corrected_patient_id' => Patient::factory(),
            'human_override_applied' => true,
            'validated_by_user_id' => User::factory(),
            'validated_at' => now(),
        ]);
    }

    public function withProcessingNotes(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_notes' => '[' . now()->format('Y-m-d H:i:s') . "] Started fax identification process\n" .
                                '[' . now()->format('Y-m-d H:i:s') . "] Successfully loaded AI extracted data\n" .
                                '[' . now()->format('Y-m-d H:i:s') . "] Patient matched successfully\n",
        ]);
    }

    public function cmn(): static
    {
        return $this->state(fn (array $attributes) => [
            'document_type' => DocumentRequestTypeEnum::CMN->value,
            'ai_extracted_data' => [
                'patient' => [
                    'name' => $this->faker->firstName . ' ' . $this->faker->lastName,
                    'sex' => $this->faker->randomElement(['Male', 'Female']),
                    'dateOfBirth' => $this->faker->date('m/d/Y', '2000-01-01'),
                    'address' => [
                        'street' => $this->faker->streetAddress,
                        'city' => $this->faker->city,
                        'state' => $this->faker->stateAbbr,
                        'zipCode' => $this->faker->postcode,
                        'phoneNumber' => $this->faker->phoneNumber,
                    ],
                ],
                'prescribedItems' => [
                    'insulinPump' => [
                        'model' => 'iLet insulin pump',
                        'orderStartDate' => $this->faker->date('m/d/Y'),
                        'lengthOfNeed' => 'Lifetime',
                        'cartridge' => 'Humalog U-100',
                        'infusionSetType' => 'Contact Detach',
                        'cgms' => [
                            'dexcomG6' => [
                                'sensors' => 'Dexcom G6 Sensors',
                                'transmitter' => 'Dexcom G6 Transmitter',
                            ],
                        ],
                    ],
                    'diagnosis' => [
                        'icd10DiagnosisCode' => 'E10.9',
                        'dateOfDiagnosis' => $this->faker->date('m/Y'),
                    ],
                ],
                'prescriberInformation' => [
                    'name' => 'Dr. ' . $this->faker->lastName,
                    'npi' => $this->faker->numerify('##########'),
                    'practiceName' => $this->faker->company . ' Medical Center',
                ],
            ],
        ]);
    }

    public function prescription(): static
    {
        return $this->state(fn (array $attributes) => [
            'document_type' => DocumentRequestTypeEnum::CUSTOM->value,
            'ai_extracted_data' => [
                'clinicInfo' => [
                    'name' => $this->faker->company . ' Medical Clinic',
                    'address' => $this->faker->address,
                    'phoneNumber' => $this->faker->phoneNumber,
                ],
                'prescriptionInfo' => [
                    'formId' => 'FORM-' . $this->faker->randomNumber(6),
                    'patientInfo' => [
                        'name' => $this->faker->firstName . ' ' . $this->faker->lastName,
                        'dob' => $this->faker->date('m/d/Y', '2000-01-01'),
                        'gender' => $this->faker->randomElement(['Male', 'Female']),
                    ],
                    'clinicianInfo' => [
                        'name' => 'Dr. ' . $this->faker->lastName,
                        'npi' => $this->faker->numerify('##########'),
                    ],
                ],
                'prescribedItems' => [
                    [
                        'itemCode' => $this->faker->randomElement(['E1390', 'E0570', 'E0607']),
                        'description' => $this->faker->randomElement(['Oxygen Concentrator', 'Nebulizer', 'Blood Glucose Monitor']),
                        'quantity' => $this->faker->numberBetween(1, 3),
                        'startDate' => $this->faker->date('m/d/Y'),
                        'lengthOfNeed' => '12 months',
                        'itemType' => 'medical equipment',
                    ],
                ],
            ],
        ]);
    }

    public function labReports(): static
    {
        return $this->state(fn (array $attributes) => [
            'document_type' => DocumentRequestTypeEnum::LAB->value,
            'ai_extracted_data' => [
                'patient' => [
                    'patientName' => $this->faker->firstName . ' ' . $this->faker->lastName,
                    'patientId' => $this->faker->numerify('PAT######'),
                    'dateOfBirth' => $this->faker->date('m/d/Y', '2000-01-01'),
                    'medicalRecordNumber' => $this->faker->numerify('MRN######'),
                    'homePhone' => $this->faker->phoneNumber,
                ],
                'testPanels' => [
                    [
                        'panelName' => 'Basic Metabolic Panel',
                        'tests' => [
                            [
                                'testName' => 'Glucose',
                                'testResult' => $this->faker->numberBetween(70, 120),
                                'testUnits' => 'mg/dL',
                                'referenceRange' => '70-110',
                                'abnormalFlag' => 'Normal',
                            ],
                            [
                                'testName' => 'Sodium',
                                'testResult' => $this->faker->numberBetween(135, 145),
                                'testUnits' => 'mmol/L',
                                'referenceRange' => '135-145',
                                'abnormalFlag' => 'Normal',
                            ],
                        ],
                    ],
                ],
                'individualTests' => [
                    'hemoglobinA1c' => $this->faker->randomFloat(1, 5.0, 8.0) . '%',
                    'calculatedAvgGlucose' => $this->faker->numberBetween(100, 200) . ' mg/dL',
                ],
            ],
        ]);
    }

    public function patientNotes(): static
    {
        return $this->state(fn (array $attributes) => [
            'document_type' => DocumentRequestTypeEnum::CHART_NOTES->value,
            'ai_extracted_data' => [
                'demographics' => [
                    'patientName' => $this->faker->firstName . ' ' . $this->faker->lastName,
                    'patientDOB' => $this->faker->date('m/d/Y', '2000-01-01'),
                    'patientAge' => $this->faker->numberBetween(18, 80) . ' yo',
                    'patientGender' => $this->faker->randomElement(['Male', 'Female']),
                    'accountNumber' => $this->faker->numerify('######'),
                    'address' => $this->faker->address,
                    'phoneNumber' => $this->faker->phoneNumber,
                    'insurance' => $this->faker->randomElement(['Blue Cross', 'Aetna', 'Medicare']),
                    'primaryCareProvider' => 'Dr. ' . $this->faker->lastName . ' MD',
                ],
                'encounterDetails' => [
                    'appointmentFacility' => $this->faker->company . ' Medical Center',
                    'appointmentDateTime' => $this->faker->dateTimeThisMonth->format('m/d/Y H:i'),
                    'encounterProvider' => 'Dr. ' . $this->faker->lastName . ' MD',
                    'visitType' => 'Office Visit',
                    'reasonForVisit' => 'Diabetes follow-up',
                ],
                'diabetesCare' => [
                    'diabetesManagement' => [
                        'insulinPump' => 'iLet insulin pump - functioning well',
                        'cgmType' => 'Dexcom G6',
                    ],
                ],
            ],
        ]);
    }
}
