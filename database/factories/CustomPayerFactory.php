<?php

namespace Database\Factories;

use App\Enums\CustomPayerReasonEnum;
use App\Enums\CustomPayerStatusEnum;
use App\Enums\OrganizationTypeEnum;
use App\Models\Distributor;
use App\Models\Payer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomPayerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'payer_id' => Payer::factory(),
            'created_by' => User::factory(),
            'organization_type' => OrganizationTypeEnum::DISTRIBUTOR,
            'organization_id' => Distributor::factory(),
            'status' => $this->faker->randomElement(CustomPayerStatusEnum::cases()),
            'reason' => $this->faker->randomElement(CustomPayerReasonEnum::cases()),
            'comment' => $this->faker->sentence(),
        ];
    }

    public function withCreatedDaysAgo(int $daysAgo): CustomPayerFactory
    {
        return $this->state(function () use ($daysAgo) {
            return [
                'created_at' => now()->subDays($daysAgo),
                'updated_at' => now()->subDays($daysAgo),
            ];
        });
    }
}
