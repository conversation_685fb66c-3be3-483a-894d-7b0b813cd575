APP_NAME="Shuttle Health"
APP_ENV=testing
APP_KEY=
APP_DEBUG=false

APP_DOMAIN="shuttle.health"
API_HOSTNAME="api-local.${APP_DOMAIN}"
APP_URL="https://${API_HOSTNAME}"
APP_HTTP_PORT=80
APP_HTTPS_PORT=443

PROVIDER_CLIENT="hcp-local.${APP_DOMAIN}"
DISTRIBUTOR_CLIENT="dme-local.${APP_DOMAIN}"
MANUFACTURER_CLIENT="mfr-local.${APP_DOMAIN}"
SH_ADMIN_CLIENT="sha-local..${APP_DOMAIN}"
CORS_ORIGINS="${PROVIDER_CLIENT},${DISTRIBUTOR_CLIENT},${MANUFACTURER_CLIENT},${SH_ADMIN_CLIENT}"

SESSION_DOMAIN=".${APP_DOMAIN}"
SESSION_DRIVER=cookie

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=test_shuttle_health
DB_USERNAME=test_shuttle_health
DB_PASSWORD=test_shuttle_health

BROADCAST_DRIVER=log
CACHE_DRIVER=array
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=array
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

PUBLIC_CONTENT_DISK=local-public-content
PRIVATE_CONTENT_DISK=local-private-content
DOCUMENTATION_DISK=local-documentation

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-2
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_BUCKET_PUBLIC_CONTENT=
AWS_BUCKET_PRIVATE_CONTENT=
AWS_BUCKET_DOCUMENTATION=

CDN_URL=https://example.cdn

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

CHANGE_HEALTHCARE_CLIENT_ID=
CHANGE_HEALTHCARE_SECRET=

SIGNALWIRE_FROM_NUMBER=
SIGNALWIRE_PROJECT_ID=
SIGNALWIRE_SPACE_URL=
SIGNALWIRE_TOKEN=

TWILIO_ACCOUNT_SID=
TWILIO_MESSAGING_SID=
TWILIO_AUTH_TOKEN=

PRIMARY_COMMUNICATIONS_SERVICE=local

DEFAULT_SEED_PASSWORD=Password123!
