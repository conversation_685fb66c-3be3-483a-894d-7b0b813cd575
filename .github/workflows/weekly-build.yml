name: Weekly build for base image

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * SAT'

env:
  PROJECT_NAME: shuttle-health

jobs:

  build:

    name: 'Build'

    runs-on: ubuntu-latest

    timeout-minutes: 10

    permissions:
      id-token: write
      contents: read

    steps:

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        id: qemu
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Build and push php-fpm alpine image
        uses: docker/build-push-action@v6
        env:
          TAG: ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:8.3-fpm-alpine3.21
        with:
          push: true
          provenance: false
          tags: ${{ env.TAG }}
          file: tools/php-fpm/Dockerfile.alpine-base
          context: .