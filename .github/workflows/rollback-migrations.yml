name: Rollback migrations
run-name: ${{ format('Rollback migrations for {0} environment by @{1}', inputs.environment, github.actor) }}

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Environment for rollback'
        required: true
        default: 'dev'
        options:
          - dev
          - qa
          - preprod
      migrations_count:
        type: number
        description: 'Migrations count (optional)'
        required: false

jobs:

  rollback-migrations:

    name: 'Rollback migrations for ${{ github.event.inputs.environment }}'

    runs-on: ubuntu-latest

    timeout-minutes: 30

    env:
      TASK_DEFINITION: ${{ github.event.inputs.environment }}-shuttle-health-api
      CONTAINER_NAME: shuttle-health-api
      ECS_CLUSTER: ${{ github.event.inputs.environment }}-shuttle-health-ecs

    permissions:
      id-token: write
      contents: read

    environment:
      name: ${{ github.event.inputs.environment }}

    steps:

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Get task arn
        id: get-task-arn
        run: |
          TASK_ARN=$(aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }} --service-name ${{ env.TASK_DEFINITION }}-service --query taskArns[0] | jq -r ".")
          echo "Task ARN: $TASK_ARN"
          echo "::set-output name=task_arn::$TASK_ARN"

      - name: Get task id
        id: get-task-id
        run: |
          TASK_ID=$(echo ${{ steps.get-task-arn.outputs.task_arn }} | cut -d'/' -f 3)
          echo "Task ID: $TASK_ID"
          echo "::set-output name=task_id::$TASK_ID"

      - name: Prepare rollback command
        id: prepare-rollback-command
        run: |
          if [ -z "${{ github.event.inputs.migrations_count }}" ]; then
            ROLLBACK_COMMAND="php artisan migrate:rollback --force"
          else
            ROLLBACK_COMMAND="php artisan migrate:rollback --force --step=${{ github.event.inputs.migrations_count }}"
          fi
          echo "Rollback command: $ROLLBACK_COMMAND"
          echo "::set-output name=rollback_command::$ROLLBACK_COMMAND"

      - name: Install expect package needed for interactive command execution
        run: |
          sudo apt-get update && sudo apt-get install -y expect

      - name: Execute rollback migrations
        run: |
          unbuffer aws ecs execute-command --cluster ${{ env.ECS_CLUSTER }} --task ${{ steps.get-task-id.outputs.task_id }} --container ${{ env.CONTAINER_NAME }} --command "${{ steps.prepare-rollback-command.outputs.rollback_command }}" --interactive