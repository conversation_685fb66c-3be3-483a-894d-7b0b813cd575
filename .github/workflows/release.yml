name: Release Workflow

on:
  push:
    tags:
      - "v*"

env:
  PROJECT_NAME: shuttle-health

jobs:

  build:

    name: 'Build release version'

    if: github.event_name == 'push'

    runs-on: ubuntu-latest

    timeout-minutes: 10

    permissions:
      id-token: write
      contents: read

    steps:

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        id: qemu
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr
        
      - name: Build and push images
        uses: docker/build-push-action@v6
        env:
          TAG: ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:${{ github.ref_name }}
        with:
          push: true
          provenance: false
          tags: ${{ env.TAG }}
          build-args: |
            APP_VERSION=${{ github.ref_name }}
            SENTRY_RELEASE=${{ github.ref_name }}
          file: tools/php-fpm/Dockerfile.alpine
          context: .

    outputs:
      project_name: ${{ env.PROJECT_NAME }}

  deploy-to-prod:

    name: 'Deploy to prod'

    needs: [ build ]

    runs-on: ubuntu-latest

    timeout-minutes: 30

    env:
      TASK_DEFINITION_API: prod-${{ needs.build.outputs.project_name }}-api
      TASK_DEFINITION_SCHEDULER: prod-${{ needs.build.outputs.project_name }}-scheduler
      TASK_DEFINITION_QUEUE: prod-${{ needs.build.outputs.project_name }}-queue
      CONTAINER_NAME_API: ${{ needs.build.outputs.project_name }}-api
      CONTAINER_NAME_SCHEDULER: ${{ needs.build.outputs.project_name }}-scheduler
      CONTAINER_NAME_QUEUE: ${{ needs.build.outputs.project_name }}-queue
      ECS_CLUSTER: prod-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ github.ref_name }}

    permissions:
      id-token: write
      contents: read

    environment:
      name: prod

    steps:

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr
        
      - name: Download task definitions
        run: |
          aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_API} --query taskDefinition > ${TASK_DEFINITION_API}.json
          aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_SCHEDULER} --query taskDefinition > ${TASK_DEFINITION_SCHEDULER}.json
          aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_QUEUE} --query taskDefinition > ${TASK_DEFINITION_QUEUE}.json

      - name: Fill in the new image ID in the Amazon ECS task definition [API]
        id: patch-task-definition-api
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION_API }}.json
          container-name: ${{ env.CONTAINER_NAME_API }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:${{ env.IMAGE_ID }}

      - name: Fill in the new image ID in the Amazon ECS task definition [SCHEDULER]
        id: patch-task-definition-scheduler
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION_SCHEDULER }}.json
          container-name: ${{ env.CONTAINER_NAME_SCHEDULER }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:${{ env.IMAGE_ID }}

      - name: Fill in the new image ID in the Amazon ECS task definition [QUEUE]
        id: patch-task-definition-queue
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION_QUEUE }}.json
          container-name: ${{ env.CONTAINER_NAME_QUEUE }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:${{ env.IMAGE_ID }}

      - name: Deploy Amazon ECS task definition [API]
        id: deploy-task-definition-api
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition-api.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION_API }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment [API]
        id: verify-ecs-deployment-api
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION_API }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition-api.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi

      - name: Deploy Amazon ECS task definition [SCHEDULER]
        id: deploy-task-definition-scheduler
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition-scheduler.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION_SCHEDULER }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment [SCHEDULER]
        id: verify-ecs-deployment-scheduler
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION_SCHEDULER }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition-scheduler.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi

      - name: Deploy Amazon ECS task definition [QUEUE]
        id: deploy-task-definition-queue
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition-queue.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION_QUEUE }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment [QUEUE]
        id: verify-ecs-deployment-queue
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION_QUEUE }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition-queue.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi