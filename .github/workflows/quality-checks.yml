name: Quality Checks

on:
  pull_request:

jobs:
  pint:
    name: "Pint (Code Style)"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: mbstring, dom, fileinfo, pgsql
          coverage: none
      - name: Install dependencies
        run: composer install --no-progress --no-interaction
      - name: Run Pint check
        run: ./vendor/bin/pint --test
      - name: Show fix instructions
        if: failure()
        run: |
          echo "❌ Pint failed. Run locally to fix:"
          echo "   ./vendor/bin/pint"
  tests:
    name: "PHP Unit Tests"
    runs-on: ubuntu-latest

    env:
      BROADCAST_DRIVER: log
      CACHE_DRIVER: redis
      QUEUE_CONNECTION: sync
      SESSION_DRIVER: redis
      DB_CONNECTION: pgsql
      DB_HOST: localhost
      DB_PASSWORD: test_shuttle_health
      DB_USERNAME: test_shuttle_health
      DB_DATABASE: test_shuttle_health

    services:
      postgres:
        image: postgres:15   # ✅ fixed version
        env:
          POSTGRES_USER: test_shuttle_health
          POSTGRES_PASSWORD: test_shuttle_health
          POSTGRES_DB: test_shuttle_health
        ports:
          - 5432/tcp
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 3

      redis:
        image: redis:6-alpine   # ✅ reverted for consistency
        ports:
          - 6379/tcp
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: mbstring, dom, fileinfo, pgsql
          coverage: pcov

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Prepare the application
        run: |
          cp .github/test.env .env
          php artisan key:generate
          php artisan passport:keys
      - name: Clear Config
        run: php artisan config:clear

      - name: Run Migration
        run: php artisan migrate -v
        env:
          DB_PORT: ${{ job.services.postgres.ports[5432] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Add pdfid.py to PATH
        run: echo "$(pwd)/tools/php-fpm/pdfid" >> "$GITHUB_PATH"

      - name: Verify pdfid.py is in PATH
        run: |
          chmod +x $(pwd)/tools/php-fpm/pdfid/pdfid.py
          pdfid.py --help
      - name: Run PHPUnit
        run: vendor/bin/phpunit --coverage-text
        env:
          DB_PORT: ${{ job.services.postgres.ports[5432] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Show fix instructions
        if: failure()
        run: |
          echo "❌ Tests failed. Run locally with:"
          echo "   vendor/bin/phpunit"
          
