name: Lint

on:
    workflow_dispatch
# on:
#   push:
#     branches: [ main ]
#   pull_request:

jobs:

  laravel-pint:

    name: <PERSON><PERSON>

    runs-on: ubuntu-latest

    steps:

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: posix, dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          coverage: none

      - name: Install dependencies
        run: composer install --no-interaction --no-suggest --ignore-platform-reqs

      - name: PHPCS lint
        run: vendor/bin/pint --test
