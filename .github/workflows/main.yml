name: Main Workflow

on:
  push:
    branches: [develop, release, integration]

env:
  PROJECT_NAME: shuttle-health

jobs:

  setup-environment:
    name: 'Setup environment'
    runs-on: ubuntu-latest
    steps:
      - name: Get branch names
        run: |
          echo "Current branch name: ${{ github.ref_name }}"
          echo "Default branch name: ${{ github.event.repository.default_branch }}"
      - name: Setup environment
        id: setup-environment
        run: |
          branch=""
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            branch="${{ github.base_ref }}"
          elif [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            branch="${{ github.event.inputs.environment }}"
          elif [ "${{ github.event_name }}" == "push" ]; then
            branch="${{ github.ref_name }}"
          fi
          echo "Branch: ${branch}"
          if [ "${branch}" == "integration" ]; then
            echo "environment=dev" >> $GITHUB_OUTPUT
          elif [ "${branch}" == "develop" ]; then
            echo "environment=qa" >> $GITHUB_OUTPUT
          elif [ "${branch}" == "release" ]; then
            echo "environment=preprod" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
            echo "environment=prod" >> $GITHUB_OUTPUT
          fi
      - name: Fail if environment result is empty
        if: ${{ steps.setup-environment.outputs.environment == '' }}
        run: |
          echo "[ERROR] Environment is not set."
          exit 1
    outputs:
      project_name: ${{ env.PROJECT_NAME }}
      environment: ${{ steps.setup-environment.outputs.environment }}

  build:
    name: 'Build'
    needs: [ setup-environment ]
    runs-on: ubuntu-latest
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        id: qemu
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Set ECR image tag(s)
        id: set-tag
        run: |
          environment="${{ needs.setup-environment.outputs.environment }}"
          if [ "$environment" = "dev" ]; then
            echo "ECR_IMAGE_TAG=dev-latest" >> $GITHUB_OUTPUT
            echo "ECR_IMAGE_FULL=${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:dev-latest" >> $GITHUB_OUTPUT
          elif [ "$environment" = "qa" ]; then
            echo "ECR_IMAGE_TAG=qa-latest" >> $GITHUB_OUTPUT
            echo "ECR_IMAGE_FULL=${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:qa-latest" >> $GITHUB_OUTPUT
          elif [ "$environment" = "preprod" ]; then
            echo "ECR_IMAGE_TAG=preprod-latest" >> $GITHUB_OUTPUT
            echo "ECR_IMAGE_FULL=${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:preprod-latest" >> $GITHUB_OUTPUT
          else
            echo "ECR_IMAGE_TAG=sha-${{ github.sha }}" >> $GITHUB_OUTPUT
            echo "ECR_IMAGE_FULL=${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:sha-${{ github.sha }}" >> $GITHUB_OUTPUT
          fi

      - name: Build and push images
        uses: docker/build-push-action@v6
        with:
          push: true
          provenance: false
          tags: ${{ steps.set-tag.outputs.ECR_IMAGE_FULL }}
          build-args: |
            APP_VERSION=${{ github.sha }}
            SENTRY_RELEASE=${{ github.sha }}
          file: tools/php-fpm/Dockerfile.alpine
          context: .

    outputs:
      project_name: ${{ needs.setup-environment.outputs.project_name }}
      environment: ${{ needs.setup-environment.outputs.environment }}
      ecr_image_full: ${{ steps.set-tag.outputs.ECR_IMAGE_FULL }}
      ecr_image_tag: ${{ steps.set-tag.outputs.ECR_IMAGE_TAG }}

  deploy-api:
    name: 'Deploy api to ${{ needs.build.outputs.environment }}'
    needs: [ build ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-api
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-api
      ECS_CLUSTER: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ needs.build.outputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}
    
      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi

  deploy-scheduler:
    name: 'Deploy scheduler to ${{ needs.build.outputs.environment }}'
    needs: [ build, deploy-api ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-scheduler
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-scheduler
      ECS_CLUSTER: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ needs.build.outputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}
    
      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi

  deploy-queue:
    name: 'Deploy queue to ${{ needs.build.outputs.environment }}'
    needs: [ build, deploy-api ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-queue
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-queue
      ECS_CLUSTER: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ needs.build.outputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}
    
      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              echo "Please review AWS Logs or consult with Engineering Team."
              exit 1
            fi
  deploy-patient-queue:
      name: 'Deploy patient queue to ${{ github.event.inputs.environment }}'
      needs: [ build, deploy-api ]
      runs-on: ubuntu-latest
      timeout-minutes: 30
      env:
        TASK_DEFINITION: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-patient-communication-queue
        CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-patient-communication-queue
        ECS_CLUSTER: ${{ needs.build.outputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
        IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
        IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
      permissions:
        id-token: write
        contents: read
      environment:
        name: ${{ github.event.inputs.environment }}
      steps:
        - name: Configure AWS Credentials
          uses: aws-actions/configure-aws-credentials@v4
          with:
            role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
            aws-region: ${{ vars.AWS_DEFAULT_REGION }}

        - name: Login to Amazon ECR
          uses: aws-actions/amazon-ecr-login@v2
          id: login-ecr

        - name: Download task definitions
          run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

        - name: Fill in the new image ID in the Amazon ECS task definition
          id: patch-task-definition
          uses: aws-actions/amazon-ecs-render-task-definition@v1
          with:
            task-definition: ${{ env.TASK_DEFINITION }}.json
            container-name: ${{ env.CONTAINER_NAME }}
            image: ${{ env.IMAGE_FULL }}

        - name: Deploy Amazon ECS task definition
          id: deploy-task-definition
          uses: aws-actions/amazon-ecs-deploy-task-definition@v2
          with:
            task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
            service: ${{ env.TASK_DEFINITION }}-service
            cluster: ${{ env.ECS_CLUSTER }}
            wait-for-service-stability: true

        - name: Verify Amazon ECS deployment
          id: verify-ecs-deployment
          env:
            ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
            ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
          run: |
            ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
            EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
            echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
            echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
            if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
              echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
              exit 1
            fi
