name: Manual Workflow
run-name: ${{ format('Manual deploy to {0} by @{1} from {2}', inputs.environment, github.actor, github.ref_name) }}

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Environment to deploy'
        required: true
        default: 'dev'
        options:
          - dev
          - qa
          - preprod

env:
  PROJECT_NAME: shuttle-health

jobs:

  build:
    name: 'Build'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        id: qemu
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Set ECR image tag(s)
        id: set-tag
        run: |
          environment="${{ github.event.inputs.environment }}"
          if [ "$environment" = "dev" ]; then
            tag="dev-latest"
          elif [ "$environment" = "qa" ]; then
            tag="qa-latest"
          elif [ "$environment" = "preprod" ]; then
            tag="preprod-latest"
          else
            tag="sha-${{ github.sha }}"
          fi
          echo "ECR_IMAGE_TAG=$tag" >> $GITHUB_OUTPUT
          echo "ECR_IMAGE_FULL=${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:$tag" >> $GITHUB_OUTPUT

      - name: Build and push images
        uses: docker/build-push-action@v6
        with:
          push: true
          provenance: false
          tags: ${{ steps.set-tag.outputs.ECR_IMAGE_FULL }}
          build-args: |
            APP_VERSION=${{ github.sha }}
            SENTRY_RELEASE=${{ github.sha }}
          file: tools/php-fpm/Dockerfile.alpine
          context: .

    outputs:
      project_name: ${{ env.PROJECT_NAME }}
      ecr_image_full: ${{ steps.set-tag.outputs.ECR_IMAGE_FULL }}
      ecr_image_tag: ${{ steps.set-tag.outputs.ECR_IMAGE_TAG }}

  deploy-api:
    name: 'Deploy api to ${{ github.event.inputs.environment }}'
    needs: [ build ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-api
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-api
      ECS_CLUSTER: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ github.event.inputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}

      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
          ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
          EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
          echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
          echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
          if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
            echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
            exit 1
          fi

  deploy-scheduler:
    name: 'Deploy scheduler to ${{ github.event.inputs.environment }}'
    needs: [ build, deploy-api ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-scheduler
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-scheduler
      ECS_CLUSTER: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ github.event.inputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}

      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
          ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
          EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
          echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
          echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
          if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
            echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
            exit 1
          fi

  deploy-queue:
    name: 'Deploy queue to ${{ github.event.inputs.environment }}'
    needs: [ build, deploy-api ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-queue
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-queue
      ECS_CLUSTER: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ github.event.inputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}

      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
          ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
          EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
          echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
          echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
          if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
            echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
            exit 1
          fi
  deploy-patient-queue:
    name: 'Deploy patient queue to ${{ github.event.inputs.environment }}'
    needs: [ build, deploy-api ]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      TASK_DEFINITION: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-patient-communication-queue
      CONTAINER_NAME: ${{ needs.build.outputs.project_name }}-patient-communication-queue
      ECS_CLUSTER: ${{ github.event.inputs.environment }}-${{ needs.build.outputs.project_name }}-ecs
      IMAGE_ID: ${{ needs.build.outputs.ecr_image_tag }}
      IMAGE_FULL: ${{ needs.build.outputs.ecr_image_full }}
    permissions:
      id-token: write
      contents: read
    environment:
      name: ${{ github.event.inputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.IAM_GITHUB_OIDC_ROLE }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Download task definitions
        run: aws ecs describe-task-definition --task-definition ${TASK_DEFINITION} --query taskDefinition > ${TASK_DEFINITION}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: patch-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE_FULL }}

      - name: Deploy Amazon ECS task definition
        id: deploy-task-definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.patch-task-definition.outputs.task-definition }}
          service: ${{ env.TASK_DEFINITION }}-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Verify Amazon ECS deployment
        id: verify-ecs-deployment
        env:
          ECS_CLUSTER: ${{ env.ECS_CLUSTER }}
          ECS_SERVICE: ${{ env.TASK_DEFINITION }}-service
        run: |
          ECS_TASK_DEFINITION_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
          EXPECTED_ECS_TASK_DEFINITION_ARN=${{ steps.deploy-task-definition.outputs.task-definition-arn }}
          echo "Current task arn: $ECS_TASK_DEFINITION_ARN"
          echo "Expected task arn: $EXPECTED_ECS_TASK_DEFINITION_ARN"
          if [ "$ECS_TASK_DEFINITION_ARN" != "$EXPECTED_ECS_TASK_DEFINITION_ARN" ]; then
            echo "Deployment process failed. Expected ECS Task Definition ARN: $EXPECTED_ECS_TASK_DEFINITION_ARN but got $ECS_TASK_DEFINITION_ARN"
            exit 1
          fi

