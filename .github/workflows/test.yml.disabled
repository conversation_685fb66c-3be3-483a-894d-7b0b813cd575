# GitHub Action for <PERSON><PERSON> with PostgreSQL and Redis
name: Test

on:
  workflow_dispatch:
# on:
#   pull_request:

jobs:
  laravel:
    name: <PERSON><PERSON> (PHP ${{ matrix.php-versions }})
    runs-on: ubuntu-latest

    env:
      BROADCAST_DRIVER: log
      CACHE_DRIVER: redis
      QUEUE_CONNECTION: sync
      SESSION_DRIVER: redis
      DB_CONNECTION: pgsql
      DB_HOST: localhost
      DB_PASSWORD: test_shuttle_health
      DB_USERNAME: test_shuttle_health
      DB_DATABASE: test_shuttle_health

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_USER: test_shuttle_health
          POSTGRES_PASSWORD: test_shuttle_health
          POSTGRES_DB: test_shuttle_health
        ports:
          - 5432/tcp
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 3

      redis:
        image: redis
        ports:
          - 6379/tcp
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    strategy:
      fail-fast: false
      matrix:
        php-versions: ['8.3']

    steps:

      - name: Checkout
        uses: actions/checkout@v4

      # Docs: https://github.com/shivammathur/setup-php
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, dom, fileinfo, pgsql
          coverage: pcov

      #- name: Get composer cache directory
      #  id: composer-cache
      #  run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      #- name: Cache composer dependencies
      #  uses: actions/cache@v2
      #  with:
      #    path: ${{ steps.composer-cache.outputs.dir }}
      #    # Use composer.json for key, if composer.lock is not committed.
      #    # key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
      #    key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      #    restore-keys: ${{ runner.os }}-composer-

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Prepare the application
        run: |
          cp .github/test.env .env
          php artisan key:generate
          php artisan passport:keys

      - name: Clear Config
        run: php artisan config:clear

      - name: Run Migration
        run: php artisan migrate -v
        env:
          DB_PORT: ${{ job.services.postgres.ports[5432] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Add pdfid.py to PATH
        run: echo "$(pwd)/tools/php-fpm/pdfid" >> "$GITHUB_PATH"

      - name: Verify pdfid.py is in PATH
        run: |
          chmod +x $(pwd)/tools/php-fpm/pdfid/pdfid.py
          pdfid.py --help

      - name: Test with phpunit
        run: vendor/bin/phpunit --coverage-text
        env:
          DB_PORT: ${{ job.services.postgres.ports[5432] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}
