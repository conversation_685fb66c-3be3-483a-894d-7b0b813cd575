<?php

namespace App\Providers;

use App\Contracts\SignalWireFaxLogClientServiceInterface;
use App\Services\SignalWire\SignalWireFaxLogClientService;
use Illuminate\Support\ServiceProvider;

class SignalWireMessageLogServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SignalWireFaxLogClientServiceInterface::class, function ($app) {
            return new SignalWireFaxLogClientService(
                spaceUrl: config('services.signal-wire.space-url'),
                projectId: config('services.signal-wire.project-id'),
                apiToken: config('services.signal-wire.token'),
            );
        });

    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
