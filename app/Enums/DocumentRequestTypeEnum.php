<?php

namespace App\Enums;

use Illuminate\Support\Collection;

enum DocumentRequestTypeEnum: string
{
    use BaseEnumTrait;

    case APPOINTMENT_CONFIRMATION = 'appointment_confirmation';
    case CMN = 'cmn';
    case CHART_NOTES = 'chart_notes';
    case LAB = 'lab';
    case CUSTOM = 'custom';
    case AOB = 'aob';

    public static function getOnDemandValues(): Collection
    {
        return collect([
            self::CMN->value,
            self::CHART_NOTES->value,
            self::LAB->value,
            self::CUSTOM->value,
        ]);
    }

    public function getDocumentType(): OrderDocumentTypeEnum
    {
        return match ($this) {
            self::APPOINTMENT_CONFIRMATION => OrderDocumentTypeEnum::APPOINTMENT_CONFIRMATION,
            self::CMN => OrderDocumentTypeEnum::CMN,
            self::LAB => OrderDocumentTypeEnum::LAB,
            self::CHART_NOTES => OrderDocumentTypeEnum::CHART_NOTES,
            self::CUSTOM => OrderDocumentTypeEnum::CUSTOM,
            self::AOB => OrderDocumentTypeEnum::AOB,
        };
    }
}
