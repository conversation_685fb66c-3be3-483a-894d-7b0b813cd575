<?php

namespace App\Traits;

use App\Notifications\Sms\AobDocumentRequestNotification;
use App\Notifications\Sms\CommunicationStart;
use App\Notifications\Sms\CommunicationStop;
use App\Notifications\Sms\InsuranceCardRequestSms;
use App\Notifications\Sms\LeadInsuranceCardRequestSms;
use App\Notifications\Sms\OrderShipped;
use App\Notifications\Sms\OrderSigned;
use App\Notifications\Sms\PendingBenefitsInvestigation;
use App\Notifications\Sms\PendingCustomerFeedback;
use App\Notifications\Sms\PendingDocumentCollection;
use App\Notifications\Sms\PendingInitialOrderReview;
use App\Notifications\Sms\PendingPreAuthorization;
use App\Notifications\Sms\PendingShippingConfirmation;
use App\Notifications\Sms\Sms;

trait IdentifiesMessageType
{
    /**
     * Get the message type based on the notification class and context.
     *
     * @param object $notification
     * @return string
     */
    public function getMessageType(object $notification): string
    {
        // Communication control messages
        if ($notification instanceof CommunicationStart || $notification instanceof CommunicationStop) {
            return 'communication_control';
        }

        // Insurance card requests
        if ($notification instanceof InsuranceCardRequestSms || $notification instanceof LeadInsuranceCardRequestSms) {
            return 'insurance_card_request';
        }

        // AoB form requests
        if ($notification instanceof AobDocumentRequestNotification) {
            return 'aob_form_request';
        }

        // Order status updates
        $orderStatusNotifications = [
            OrderShipped::class,
            OrderSigned::class,
            PendingInitialOrderReview::class,
            PendingBenefitsInvestigation::class,
            PendingCustomerFeedback::class,
            PendingDocumentCollection::class,
            PendingPreAuthorization::class,
            PendingShippingConfirmation::class,
        ];

        $notificationClass = get_class($notification);

        if (in_array($notificationClass, $orderStatusNotifications)) {
            return 'order_status_update';
        }

        // Generic SMS - need to check context
        if ($notification instanceof Sms) {
            // Check if it's forced real-time
            if (method_exists($notification, 'isForceRealTime') && $notification->isForceRealTime()) {
                // Check if it has lead context - likely Day-0 digital journey
                if (property_exists($notification, 'lead') && $notification->lead) {
                    return 'digital_journey_day_0';
                }

                // Otherwise, try to determine if it's ad hoc or on-demand based on userId
                return $notification->getUserId() ? 'ad_hoc' : 'on_demand';
            }

            // Non-forced real-time messages
            if (property_exists($notification, 'patient') && $notification->patient) {
                // Has patient context - could be order status or other types
                return 'unknown';
            }

            if (property_exists($notification, 'lead') && $notification->lead) {
                // Lead-based message without forceRealTime - likely other days digital journey
                return 'digital_journey_other_days';
            }
        }

        // For non-Sms notifications, check if they have lead property
        if (property_exists($notification, 'lead') && $notification->lead) {
            return 'digital_journey_other_days';
        }

        // Default to unknown
        return 'unknown';
    }
}
