<?php

namespace App\Traits;

use App\Models\Distributor;
use App\Models\Manufacturer;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

trait HasOrganizationCommunicationWindowOpen
{
    public function isCommunicationWindowOpen(Distributor|Manufacturer $organization, ?string $patientTimezone = null): bool
    {
        if (config('app.bypass_queue_communication_window_range')) {
            return true;
        }

        $organizationSettings = $organization->organizationSettings;

        if (empty($organizationSettings)) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when no organization settings
            return $this->isWithinDefaultWindow($patientTimezone);
        }

        $settings = Cache::remember(
            "organization:{$organization->id}:settings",
            now()->addMinutes(120),
            function () use ($organization) {
                return $organization->organizationSettings()
                    ->get()
                    ->mapWithKeys(function ($item) {
                        return [
                            $item->name->value => $item->value,
                        ];
                    })->toArray();
            },
        );

        if (empty($settings)) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when no settings found
            return $this->isWithinDefaultWindow($patientTimezone);
        }

        // ✅ Use patient timezone if provided, otherwise fall back to organization timezone
        // This ensures consistency with PatientCommunicationWindowService
        $timezone = $patientTimezone ?? config('app.sh_timezone', 'UTC');

        $now = Carbon::now($timezone);
        $day = strtolower($now->format('l')); // monday, tuesday, etc.

        // ✅ 1. Check if this day is enabled
        if (
            ($settings["communication_days_{$day}"] ?? 'false') !== 'true'
            && ($settings["communication_days_{$day}"] ?? 'false') !== true
        ) {
            return false;
        }


        // ✅ 2. Check time range
        $startRaw = $settings['communication_start_time'] ?? null;
        $endRaw = $settings['communication_end_time'] ?? null;


        if (!$startRaw || !$endRaw) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when no time range configured
            return $this->isWithinDefaultWindow($patientTimezone);
        }

        try {
            $start = Carbon::createFromFormat('H:i', $startRaw, $timezone);
            $end = Carbon::createFromFormat('H:i', $endRaw, $timezone);
        } catch (\Exception $e) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when invalid time values
            return $this->isWithinDefaultWindow($patientTimezone);
        }


        // ✅ Allow only if current time is inside window
        return $now->between($start, $end);
    }

    public function delayTimeInSeconds(Distributor|Manufacturer $organization, Carbon $targetDate, ?string $patientTimezone = null): int
    {
        if (config('app.bypass_queue_communication_window_range')) {
            return 0;
        }

        $settings = Cache::remember(
            "organization:{$organization->id}:settings",
            now()->addMinutes(5),
            fn () => $organization->organizationSettings()
                ->get()
                ->mapWithKeys(fn ($item) => [$item->name->value => $item->value])
                ->toArray(),
        );

        if (empty($settings)) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when no settings found
            return $this->calculateDefaultWindowDelay($targetDate, $patientTimezone);
        }

        // ✅ Use patient timezone if provided, otherwise fall back to organization timezone
        // This ensures consistency with PatientCommunicationWindowService
        $timezone = $patientTimezone ?? config('app.sh_timezone', 'UTC');
        $now = Carbon::now($timezone);

        $startRaw = $settings['communication_start_time'] ?? null;
        $endRaw = $settings['communication_end_time'] ?? null;

        if (!$startRaw || !$endRaw) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when no time range configured
            return $this->calculateDefaultWindowDelay($targetDate, $patientTimezone);
        }

        // Helpers to build start/end on a given date
        $buildStart = fn (Carbon $d) => Carbon::createFromFormat('H:i', $startRaw, $timezone)
            ->setDate($d->year, $d->month, $d->day);

        $buildEnd = fn (Carbon $d) => Carbon::createFromFormat('H:i', $endRaw, $timezone)
            ->setDate($d->year, $d->month, $d->day);

        // ✅ Validate time formats
        try {
            Carbon::createFromFormat('H:i', $startRaw, $timezone);
            Carbon::createFromFormat('H:i', $endRaw, $timezone);
        } catch (\Exception $e) {
            // ✅ Fallback: Use default 8:00 AM to 8:00 PM window when invalid time values
            return $this->calculateDefaultWindowDelay($targetDate, $patientTimezone);
        }

        $sendDate = $targetDate->copy();
        $finalSend = null;

        // Look up to 7 days ahead
        for ($i = 0; $i < 7; $i++) {
            $dayName = strtolower($sendDate->format('l'));

            if (!$this->dayIsEnabled($dayName, $settings)) {
                $sendDate->addDay();

                continue;
            }

            $start = $buildStart($sendDate);
            $end = $buildEnd($sendDate);

            // Candidate = targetDate’s time-of-day applied to this day
            $candidate = $sendDate->copy()->setTime(
                $targetDate->hour,
                $targetDate->minute,
                $targetDate->second,
            );

            if ($candidate->lt($start)) {
                // Before window → snap to start
                $finalSend = $start;
            } elseif ($candidate->gt($end)) {
                // After window → try next enabled day
                $sendDate->addDay();

                continue;
            } else {
                // Inside window → use candidate
                $finalSend = $candidate;
            }

            // Don’t schedule in the past
            if ($finalSend->lt($now)) {
                if ($now->between($start, $end)) {
                    $finalSend = $now; // still in today’s window → send now
                } else {
                    $sendDate->addDay();
                    $finalSend = null;

                    continue;
                }
            }

            break; // found valid $finalSend
        }

        // Fallback: next enabled day at window start
        if (!$finalSend) {
            for ($i = 0; $i < 7; $i++) {
                $dayName = strtolower($sendDate->format('l'));

                if ($this->dayIsEnabled($dayName, $settings)) {
                    $finalSend = $buildStart($sendDate);
                    break;
                }
                $sendDate->addDay();
            }
        }

        return max(0, $now->diffInSeconds($finalSend, false));
    }

    private function dayIsEnabled(string $day, array $settings): bool
    {
        $val = $settings["communication_days_{$day}"] ?? 'false';

        return $val === true || $val === 'true';
    }

    /**
     * Check if current time is within default communication window (8:00 AM to 8:00 PM)
     * Used as fallback when no organization settings are available
     */
    private function isWithinDefaultWindow(?string $patientTimezone = null): bool
    {
        // ✅ Use patient timezone if provided, otherwise fall back to application timezone
        $timezone = $patientTimezone ?? config('app.sh_timezone', 'UTC');

        $now = Carbon::now($timezone);

        // Default window: 8:00 AM to 8:00 PM
        $start = Carbon::createFromFormat('H:i', '08:00', $timezone);
        $end = Carbon::createFromFormat('H:i', '20:00', $timezone);

        return $now->between($start, $end);
    }

    /**
     * Calculate delay for default communication window (8:00 AM to 8:00 PM)
     * Used as fallback when no organization settings are available
     */
    private function calculateDefaultWindowDelay(Carbon $targetDate, ?string $patientTimezone = null): int
    {
        // ✅ Use patient timezone if provided, otherwise fall back to application timezone
        $timezone = $patientTimezone ?? config('app.sh_timezone', 'UTC');
        $now = Carbon::now($timezone);

        // Default window: 8:00 AM to 8:00 PM
        $start = Carbon::createFromFormat('H:i', '08:00', $timezone);
        $end = Carbon::createFromFormat('H:i', '20:00', $timezone);

        // Set the date for start and end times
        $start->setDate($targetDate->year, $targetDate->month, $targetDate->day);
        $end->setDate($targetDate->year, $targetDate->month, $targetDate->day);

        // If target date is today and we're within the window, send now
        if ($targetDate->isToday() && $now->between($start, $end)) {
            return 0;
        }

        // If target date is today but we're before the window, delay to window start
        if ($targetDate->isToday() && $now->lt($start)) {
            return max(0, $now->diffInSeconds($start, false));
        }

        // If target date is today but we're after the window, delay to next day's window start
        if ($targetDate->isToday() && $now->gt($end)) {
            $nextDayStart = $start->copy()->addDay();

            return max(0, $now->diffInSeconds($nextDayStart, false));
        }

        // For future dates, delay to the window start on that day
        if ($targetDate->gt($now)) {
            return max(0, $now->diffInSeconds($start, false));
        }

        // Default: no delay
        return 0;
    }

}
