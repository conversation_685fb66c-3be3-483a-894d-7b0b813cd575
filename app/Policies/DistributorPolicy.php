<?php

namespace App\Policies;

use App\Enums\DistributorUserTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\UserPermissionEnum;
use App\Models\Distributor;
use App\Models\DistributorCampaign;
use App\Models\District;
use App\Models\Fax;
use App\Models\Lead;
use App\Models\Order;
use App\Models\OrderSource;
use App\Models\Patient;
use App\Models\PatientDocument;
use App\Models\Region;
use App\Models\Territory;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class DistributorPolicy
{
    use HandlesAuthorization;

    public function read(User $user, Distributor $distributor): bool
    {
        return $user->belongsToOrganization($distributor);
    }

    public function write(User $user, Distributor $distributor): bool
    {
        return $user->belongsToOrganization($distributor)
            && $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR);
    }

    public function readOrder(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id
                && in_array($order->status, $order->getAvailableInDmeStatuses());

            if ($allowed && $user->hasRole(DistributorUserTypeEnum::EXTERNAL_USER)) {
                $role = $user->roles->where('name', DistributorUserTypeEnum::EXTERNAL_USER->value)->firstOrFail();
                $allowed = $order->products()->where('manufacturer_id', $role->pivot->manufacturer_id)->exists();
            }
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized'));
    }

    public function createOrder(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function writeOrder(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id
                && in_array($order->status, $order->getAvailableInDmeStatuses());
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function readOrderSource(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function createOrderSource(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function updateOrderSource(User $user, Distributor $distributor, OrderSource $orderSource): Response
    {
        $belongsToOrg = $user->belongsToOrganization($distributor)
            && $orderSource->organization_type === get_class($distributor)
            && $orderSource->organization_id === $distributor->id;

        $allowed = $belongsToOrg && $user->hasAnyRole([
            DistributorUserTypeEnum::STAFF,
            DistributorUserTypeEnum::ADMINISTRATOR,
            DistributorUserTypeEnum::EXTERNAL_USER,
        ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function archiveOrder(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id
                && in_array($order->status, $order->getAvailableInDmeStatuses());
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function assignExternalManufacturerUser(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasRole([
                DistributorUserTypeEnum::EXTERNAL_USER,
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id && in_array($order->status, $order->getAvailableInDmeStatuses());
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_assign'));
    }

    public function updateOrder(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id
                && in_array($order->status, $order->getAvailableInDmeStatuses());
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_write'));
    }

    public function readPatient(User $user, Distributor $distributor, ?Patient $patient = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        if ($allowed && $patient) {
            $allowed = $patient->belongsToOrganization($distributor);
        }

        return $allowed ? $this->allow() : $this->deny(__('errors.patient.unauthorized'));
    }

    public function writePatient(User $user, Distributor $distributor, ?Patient $patient = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        if ($allowed && $patient) {
            $allowed = $patient->belongsToOrganization($distributor);
        }

        return $allowed ? $this->allow() : $this->deny(__('errors.patient.unauthorized_write'));
    }

    public function assignUser(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id;
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order.unauthorized_assign'));
    }

    public function readUser(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasAnyRole([
            DistributorUserTypeEnum::STAFF,
            DistributorUserTypeEnum::ADMINISTRATOR,
            DistributorUserTypeEnum::EXTERNAL_USER,
        ]);

        return $allowed ? $this->allow() : $this->deny(__('errors.user.unauthorized'));
    }

    public function readWriteProviderUser(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasAnyRole([
            DistributorUserTypeEnum::STAFF,
            DistributorUserTypeEnum::ADMINISTRATOR,
            DistributorUserTypeEnum::EXTERNAL_USER,
        ]);

        return $allowed ? $this->allow() : $this->deny(__('errors.user.unauthorized'));
    }

    public function readWriteProvider(User $user, Distributor $distributor): Response
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasAnyRole([
            DistributorUserTypeEnum::STAFF,
            DistributorUserTypeEnum::ADMINISTRATOR,
            DistributorUserTypeEnum::EXTERNAL_USER,
        ]);

        return $allowed ? $this->allow() : $this->deny(__('errors.user.unauthorized'));
    }

    public function writeUser(User $user, Distributor $distributor): Response
    {
        return $user->belongsToOrganization($distributor) && $user->hasRole(
            DistributorUserTypeEnum::ADMINISTRATOR,
        ) ? $this->allow() : $this->deny(__('errors.user.unauthorized_write'));
    }

    public function deleteOrderDocument(User $user, Distributor $distributor, PatientDocument $document): Response
    {
        $allowed = $document->source === OrderDocumentSourceEnum::DISTRIBUTOR_USER
            && !empty($document->uploaded_by)
            && $user->belongsToOrganization($distributor)
            && $document->uploadedBy->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order_document.unauthorized_delete'));
    }

    public function readDocumentRequest(
        User $user,
        Distributor $distributor,
        ?int $documentRequestId = null,
    ): Response {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed && $documentRequestId) {
            $allowed = $distributor->documentRequests()->where('document_requests.id', $documentRequestId)->exists();
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.document_request.unauthorized'));
    }

    public function writeDocumentRequest(
        User $user,
        Distributor $distributor,
        ?int $documentRequestId = null,
    ): Response {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF->value,
                DistributorUserTypeEnum::ADMINISTRATOR->value,
            ]);

        if ($allowed && $documentRequestId) {
            $allowed = $distributor->documentRequests()->where('document_requests.id', $documentRequestId)->exists();
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.document_request.unauthorized_write'));
    }

    public function writeFax(User $user, Distributor $distributor, Fax $fax): Response
    {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed && $fax) {
            $allowed = $fax->distributor_id === $distributor->id;
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.common.unauthorized_write', ['entity' => 'fax']));
    }

    public function readCampaignRelation(User $user, Distributor $distributor, DistributorCampaign $campaign, $relation = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed) {
            $allowed = $campaign->distributor_id === $distributor->id;

            if ($allowed && $relation) {
                $allowed = $relation->distributor_campaign_id === $campaign->id;
            }
        }

        return $allowed;
    }

    public function writeCampaignRelation(User $user, Distributor $distributor, DistributorCampaign $campaign, $relation = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR->value);

        if ($allowed) {
            $allowed = $campaign->distributor_id === $distributor->id;

            if ($allowed && $relation) {
                $allowed = $relation->distributor_campaign_id === $campaign->id;
            }
        }

        return $allowed;
    }

    public function deleteCampaignRelation(User $user, Distributor $distributor, DistributorCampaign $campaign, $relation = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR->value);

        if ($allowed) {
            $allowed = $campaign->distributor_id === $distributor->id;

            if ($allowed && $relation) {
                $allowed = $relation->distributor_campaign_id === $campaign->id;
            }
        }

        return $allowed;
    }

    public function readCampaign(User $user, Distributor $distributor, ?DistributorCampaign $campaign = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed && $campaign) {
            $allowed = $campaign->distributor_id === $distributor->id;
        }

        return $allowed;
    }

    public function writeCampaign(User $user, Distributor $distributor, ?DistributorCampaign $campaign = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor) && $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR->value);

        if ($allowed && $campaign) {
            $allowed = $campaign->distributor_id === $distributor->id;
        }

        return $allowed;
    }

    public function readWriteLeadRelation(User $user, Distributor $distributor, DistributorCampaign $campaign, ?Lead $lead = null): bool
    {
        $allowed = $user->belongsToOrganization($distributor);

        if ($allowed) {
            $allowed = $campaign->distributor_id === $distributor->id;

            if ($allowed && $lead) {
                $allowed = $lead->distributor_campaign_id === $campaign->id;
            }
        }

        return $allowed;
    }

    /** Region */
    public function readAnyRegion(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_ALL_REGIONS);
    }

    public function readRegion(User $user, Distributor $distributor, Region $region): bool
    {
        if (!$user->belongsToOrganization($distributor) || $region->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_REGION);
    }

    public function createRegion(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::CREATE_REGION);
    }

    public function updateRegion(User $user, Distributor $distributor, Region $region): bool
    {
        if (!$user->belongsToOrganization($distributor) || $region->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::EDIT_REGION);
    }

    public function updateRegionManager(User $user, Distributor $distributor, Region $region): bool
    {
        if (!$user->belongsToOrganization($distributor) || $region->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::ASSIGN_REGION_MANAGER);
    }

    /** District */
    public function readAnyDistrict(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_ALL_DISTRICTS);
    }

    public function readDistrict(User $user, Distributor $distributor, District $district): bool
    {
        if (!$user->belongsToOrganization($distributor) || $district->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_DISTRICT);
    }

    public function createDistrict(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::CREATE_DISTRICT);
    }

    public function updateDistrict(User $user, Distributor $distributor, District $district): bool
    {
        if (!$user->belongsToOrganization($distributor) || $district->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::EDIT_DISTRICT);
    }

    public function updateDistrictManager(User $user, Distributor $distributor, District $district): bool
    {
        if (!$user->belongsToOrganization($distributor) || $district->organization->id !== $distributor->id) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::ASSIGN_DISTRICT_MANAGER);
    }

    /** Territory */
    public function readAnyTerritory(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_ALL_TERRITORIES);
    }

    public function readTerritory(User $user, Distributor $distributor, Territory $territory): bool
    {
        if (!$user->belongsToOrganization($distributor) || !$territory->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::READ_TERRITORY);
    }

    public function createTerritory(User $user, Distributor $distributor): bool
    {
        if (!$user->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::CREATE_TERRITORY);
    }

    public function updateTerritory(User $user, Distributor $distributor, Territory $territory): bool
    {
        if (!$user->belongsToOrganization($distributor) || !$territory->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::EDIT_TERRITORY);
    }

    public function updateTerritoryManager(User $user, Distributor $distributor, Territory $territory): bool
    {
        if (!$user->belongsToOrganization($distributor) || !$territory->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::ASSIGN_TERRITORY_MANAGER);
    }

    public function updateTerritoryZipCodes(User $user, Distributor $distributor, Territory $territory): bool
    {
        if (!$user->belongsToOrganization($distributor) || !$territory->belongsToOrganization($distributor)) {
            return false;
        }

        return $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::EDIT_TERRITORY_ZIP_CODES);
    }

    public function manageGalleryImage(User $user, Distributor $distributor): bool
    {
        return $user->belongsToOrganization($distributor) && (
            $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::MANAGE_GALLERY_IMAGE)
        );
    }

    public function viewSignalWireMessageLogs(User $user, Distributor $distributor): bool
    {
        return $user->belongsToOrganization($distributor) && (
            $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
                || $user->hasPermissionTo(UserPermissionEnum::VIEW_SIGNALWIRE_MESSAGE_LOGS)
        );
    }

    public function configureMessageTemplates(User $user, Distributor $distributor): bool
    {
        return $user->belongsToOrganization($distributor) && (
            $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
                || $user->hasPermissionTo(UserPermissionEnum::CONFIGURE_MESSAGE_TEMPLATES)
        );
    }

    public function attachOrderDocument(User $user, Distributor $distributor, PatientDocument $document): Response
    {
        $allowed = $document->source === OrderDocumentSourceEnum::DISTRIBUTOR_USER
            && !empty($document->uploaded_by)
            && $user->belongsToOrganization($distributor)
            && $document->uploadedBy->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order_document.unauthorized_attach'));
    }

    public function detachOrderDocument(User $user, Distributor $distributor, PatientDocument $document): Response
    {
        $allowed = $document->source === OrderDocumentSourceEnum::DISTRIBUTOR_USER
            && !empty($document->uploaded_by)
            && $user->belongsToOrganization($distributor)
            && $document->uploadedBy->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order_document.unauthorized_detach'));
    }

    public function deletePatientDocument(User $user, Distributor $distributor, ?Patient $patient = null, ?PatientDocument $document = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor) && $patient->belongsToOrganization($distributor)
            && $document->source === OrderDocumentSourceEnum::DISTRIBUTOR_USER
            && !empty($document->uploaded_by)
            && $user->belongsToOrganization($distributor)
            && $document->uploadedBy->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
            ]);

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order_document.unauthorized_delete'));
    }

    public function uploadPatientDocument(User $user, Distributor $distributor, ?Patient $patient = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        if ($allowed && $patient) {
            $allowed = $patient->belongsToOrganization($distributor);
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.patient_document.unauthorized_upload'));
    }

    public function uploadOrderDocument(User $user, Distributor $distributor, ?Order $order = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        if ($allowed && $order) {
            $allowed = $order->distributor_id === $distributor->id
                && in_array($order->status, $order->getAvailableInDmeStatuses());

            if ($allowed && $user->hasRole(DistributorUserTypeEnum::EXTERNAL_USER)) {
                $role = $user->roles->where('name', DistributorUserTypeEnum::EXTERNAL_USER->value)->firstOrFail();
                $allowed = $order->products()->where('manufacturer_id', $role->pivot->manufacturer_id)->exists();
            }
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.order_document.unauthorized_upload'));
    }

    public function updatePatientDocument(User $user, Distributor $distributor, ?Patient $patient = null, ?PatientDocument $document = null): Response
    {
        $allowed = $user->belongsToOrganization($distributor)
            && $user->hasAnyRole([
                DistributorUserTypeEnum::STAFF,
                DistributorUserTypeEnum::ADMINISTRATOR,
                DistributorUserTypeEnum::EXTERNAL_USER,
            ]);

        if ($allowed && $patient) {
            $allowed = $patient->belongsToOrganization($distributor);
        }

        if ($allowed && $document) {
            $allowed = $document->createdByOrganization->id === $distributor->id;
        }

        return $allowed
            ? $this->allow()
            : $this->deny(__('errors.patient_document.unauthorized_update'));
    }

    public function generateAnalyticsEmbedUrl(User $user, Distributor $distributor): Response
    {
        // Check if the user belongs to the organization
        if (!$user->belongsToOrganization($distributor)) {
            return $this->deny(__('errors.analytics.unauthorized_organization'));
        }

        // Check if user is distributor admin OR has explicit permission
        $hasAccess = $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::GENERATE_ANALYTICS_EMBED_URL);

        if (!$hasAccess) {
            return $this->deny(__('errors.analytics.unauthorized_generate_embed_url'));
        }

        return $this->allow();
    }

    public function createQuickSightUser(User $user, Distributor $distributor): Response
    {
        // Check if the user belongs to the organization
        if (!$user->belongsToOrganization($distributor)) {
            return $this->deny(__('errors.analytics.unauthorized_organization'));
        }

        // Check if user is distributor admin OR has explicit permission
        $hasAccess = $user->hasRole(DistributorUserTypeEnum::ADMINISTRATOR)
            || $user->hasPermissionTo(UserPermissionEnum::CREATE_QUICK_SIGHT_USER);

        if (!$hasAccess) {
            return $this->deny(__('errors.analytics.unauthorized_create_user'));
        }

        return $this->allow();
    }
}
