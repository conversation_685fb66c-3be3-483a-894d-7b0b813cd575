<?php

namespace App\Services;

use App\Enums\LogGroupEnum;
use App\Enums\PatientTimezoneEnum;
use App\Extensions\Logger;
use App\Models\Lead;
use App\Models\Patient;
use App\Services\ShuttleHealth\OrganizationContextService;
use App\Traits\IdentifiesMessageType;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\Job;
use Illuminate\Notifications\SendQueuedNotifications;
use Illuminate\Support\Facades\Cache;

class PatientCommunicationWindowService
{
    use IdentifiesMessageType;

    public function __construct(public OrganizationContextService $organizationContextService)
    {
    }

    public function canRun(Job $job): bool
    {
        if (config('app.bypass_queue_communication_window_range')) {
            return true;
        }

        $payload = $job->payload();
        $command = unserialize($payload['data']['command']);

        // Check if this is a notification that should never be queued
        if ($command instanceof SendQueuedNotifications && $command->notification) {
            $messageType = $this->getMessageType($command->notification);

            // Messages that should NEVER be subject to time windows
            $alwaysRunTypes = [
                'communication_control',
                'insurance_card_request',
                'aob_form_request',
                'ad_hoc',
                'on_demand',
                'digital_journey_day_0', // Day-0 messages are always real-time
            ];

            if (in_array($messageType, $alwaysRunTypes)) {
                return true;
            }
        }

        $organization = $this->getOrganization($command);
        $timezone = $this->getTimezone($command);

        if (empty($organization) || empty($timezone)) {
            // If we can't determine organization or timezone, allow the message to be sent
            // This prevents messages from being stuck in queue indefinitely
            return true;
        }

        $settings = Cache::remember(
            "organization:{$organization->id}:settings",
            now()->addMinutes(2),
            function () use ($organization) {
                return $organization->organizationSettings()
                    ->get()
                    ->mapWithKeys(fn ($item) => [$item->name->value => $item->value])
                    ->toArray();
            },
        );

        if (empty($settings)) {
            // If no settings found, allow the message to be sent
            return true;
        }

        if ($timezone instanceof PatientTimezoneEnum) {
            $timezone = $timezone->value;
        }

        $now = Carbon::now($timezone);
        $day = strtolower($now->format('l')); // monday, tuesday, etc.

        // ✅ 1. Day check
        if (
            ($settings["communication_days_{$day}"] ?? 'false') !== 'true'
            && ($settings["communication_days_{$day}"] ?? 'false') !== true
        ) {
            return false;
        }

        // ✅ 2. Time window
        $startRaw = $settings['communication_start_time'] ?? null;
        $endRaw = $settings['communication_end_time'] ?? null;

        if (! $startRaw || ! $endRaw) {
            // If no time window configured, allow the message to be sent
            return true;
        }

        try {
            $start = Carbon::createFromFormat('H:i', $startRaw, $timezone);
            $end = Carbon::createFromFormat('H:i', $endRaw, $timezone);
        } catch (\Exception $e) {
            // If time parsing fails, allow the message to be sent
            return true;
        }

        return $now->between($start, $end);
    }

    public function getLead($command): ?Lead
    {
        $lead = null;

        if ($command instanceof SendQueuedNotifications) {
            $lead = property_exists($command->notification, 'lead') ? $command->notification->lead : null;
        }

        if ($lead === null && property_exists($command, 'lead')) {
            $lead = $command->lead;
        }

        return $lead instanceof Lead ? $lead : null;
    }

    public function getPatient($command): ?Patient
    {
        $patient = null;

        if ($command instanceof SendQueuedNotifications) {
            $patient = $command->notification?->patient;
        }

        if ($patient === null && property_exists($command, 'patient')) {
            $patient = $command->patient;
        }

        return $patient instanceof Patient ? $patient : null;
    }

    public function getOrganization($command)
    {
        $organization = null;

        if ($this->getPatient($command) !== null) {
            $organization = $this->getPatient($command)->organization;
        }

        if ($this->getLead($command) !== null) {
            $organization = $this->getLead($command)->distributorCampaign?->distributor;
        }

        return $organization;
    }

    public function getTimezone($command): string
    {
        try {
            $timezone = config('app.sh_timezone');

            if ($this->getPatient($command) !== null) {
                $patientTimezone = $this->getPatient($command)->timezone;

                if ($patientTimezone) {
                    $timezone = $patientTimezone instanceof PatientTimezoneEnum ? $patientTimezone->value : $patientTimezone;
                }
            }

            if ($this->getLead($command) !== null) {
                $leadTimezone = $this->getLead($command)->timezone;

                if ($leadTimezone) {
                    $timezone = $leadTimezone instanceof PatientTimezoneEnum ? $leadTimezone->value : $leadTimezone;
                }
            }

            // Ensure we always return a string, fallback to default timezone
            if (empty($timezone)) {
                Logger::info('Using default timezone for communication window check', LogGroupEnum::DEFAULT, [
                    'command_class' => get_class($command),
                    'default_timezone' => config('app.sh_timezone'),
                    'patient_found' => $this->getPatient($command) !== null,
                    'lead_found' => $this->getLead($command) !== null,
                ]);
            }

            return $timezone ?? config('app.sh_timezone');
        } catch (\Exception $e) {
            // Log the exception for debugging but don't break the application
            Logger::warning('Error getting timezone, falling back to default', LogGroupEnum::DEFAULT, [
                'error' => $e->getMessage(),
                'command_class' => get_class($command),
                'fallback_timezone' => config('app.sh_timezone'),
            ]);

            // Always return a valid timezone string
            return config('app.sh_timezone');
        }
    }
}
