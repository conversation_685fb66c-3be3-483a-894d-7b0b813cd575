<?php

namespace App\Services;

use App\Models\Distributor;
use App\Models\Lead;
use App\Models\Patient;
use App\Traits\HasOrganizationCommunicationWindowOpen;
use Carbon\Carbon;

class MessageQueueingService
{
    use HasOrganizationCommunicationWindowOpen;

    /**
     * Determine if a message should be queued based on message type and communication window.
     *
     * @param string $messageType
     * @param Distributor|null $distributor
     * @param string|null $timezone
     * @return bool
     */
    public function shouldQueueMessage(string $messageType, ?Distributor $distributor, ?string $timezone): bool
    {
        // Messages that should NEVER be queued (always real-time)
        $neverQueuedTypes = [
            'ad_hoc',
            'on_demand',
            'communication_control',
            'insurance_card_request',
            'aob_form_request',
            'digital_journey_day_0', // Day-0 messages are always real-time
        ];

        if (in_array($messageType, $neverQueuedTypes)) {
            return false;
        }

        // Messages that are ALWAYS queued
        $alwaysQueuedTypes = [
            'digital_journey_other_days', // Non Day-0 digital journey
        ];

        if (in_array($messageType, $alwaysQueuedTypes)) {
            return true;
        }

        // Messages that are queued if outside communication window
        $conditionallyQueuedTypes = [
            'order_status_update',
        ];

        if (in_array($messageType, $conditionallyQueuedTypes)) {
            // Check if we're outside the communication window
            if (!$distributor || !$timezone) {
                // If we don't have distributor or timezone info, don't queue
                // This ensures messages aren't stuck indefinitely
                return false;
            }

            return !$this->isCommunicationWindowOpen($distributor, $timezone);
        }

        // Default: don't queue unknown message types
        return false;
    }

    /**
     * Calculate delay time for a message that should be queued.
     *
     * @param Distributor $distributor
     * @param Carbon $targetDate
     * @param string $timezone
     * @return int Delay in seconds
     */
    public function calculateDelayTime(Distributor $distributor, Carbon $targetDate, string $timezone): int
    {
        return $this->delayTimeInSeconds($distributor, $targetDate, $timezone);
    }

    /**
     * Check if a patient/lead has valid communication preferences for the channel.
     *
     * @param Patient|Lead $recipient
     * @param string $channel
     * @return bool
     */
    public function hasValidCommunicationPreference($recipient, string $channel = 'sms'): bool
    {
        if ($recipient instanceof Patient) {
            return $channel === 'sms' ? $recipient->sms_enabled : $recipient->email_enabled;
        }

        if ($recipient instanceof Lead) {
            return $channel === 'sms' ? $recipient->sms_enabled : $recipient->email_enabled;
        }

        return false;
    }
}
