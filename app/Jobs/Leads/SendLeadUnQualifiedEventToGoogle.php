<?php

namespace App\Jobs\Leads;

use App\Enums\OrganizationSettingNameEnum;
use App\Models\Lead;
use App\Services\Google\GoogleAdwordsPostEventsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;

class SendLeadUnQualifiedEventToGoogle implements ShouldQueue
{
    use Dispatchable;
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public Lead $lead)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(GoogleAdwordsPostEventsService $googleAdwordsPostEventsService): void
    {
        $distributorCampaign = $this->lead?->distributorCampaign;
        $distributor = $distributorCampaign?->distributor;
        $conversionActionId = decrypt($distributor?->organizationSettings->where('name', OrganizationSettingNameEnum::GOOGLE_ADWORDS_API_LEAD_UNQUALIFIED_CONVERSION_ACTION_ID)->first()?->value);
        $gclid = $lead?->gclid;
        $googleAdwordsPostEventsService->postEvents(
            organization: $distributor,
            conversionActionId: $conversionActionId,
            gclid: $gclid,
        );

    }
}
