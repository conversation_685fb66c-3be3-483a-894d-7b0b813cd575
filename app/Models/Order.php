<?php

namespace App\Models;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\InternalSourceEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PortalEnum;
use App\Filters\OrderFilter;
use App\Models\Pivot\OrderProductPivot;
use App\Models\Scopes\NotArchivedOrderScope;
use App\Observers\OrderObserver;
use App\Services\ShuttleHealth\OrganizationContextService;
use App\Traits\HasActivityLogs;
use App\Traits\HasEnvelopes;
use App\Traits\HasStatusTransitions;
use App\Traits\IsFilterable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property OrderTypeEnum $type
 * @property OrderStatusEnum $status
 * @property int $patient_id
 * @property int $provider_id
 * @property int $provider_user_id
 * @property int|null $provider_order_id
 * @property int $distributor_id
 * @property int|null $distributor_user_id
 * @property int|null $distributor_order_id
 * @property int|null $external_manufacturer_user_id
 * @property Carbon|null $due_on
 * @property Carbon|null $follow_up_at
 * @property int $created_by
 * @property string $external_order_id
 * @property int $brightree_note_id
 * @property int $global_patient_id
 * @property int $global_id
 * @property int $facility_id
 * @property bool $source_editable
 * @property string $source_description
 * @property InternalSourceEnum $internal_source
 * @property string|null $created_by_organization_type
 * @property int|null $created_by_organization_id
 * @property int|null $distributor_territory_id
 * @property Carbon|null $document_follow_up_date
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Collection $activityLogs
 * @property-read Distributor $distributor
 * @property-read DistributorUser|null $distributorUser
 * @property-read Collection $documentRequests
 * @property-read User|null $externalManufacturerUser
 * @property-read Facility $facility
 * @property-read Lead $lead
 * @property-read Collection $medicalPolicyResponses
 * @property-read OrderCancellation $cancellation
 * @property-read Collection $documents
 * @property-read OrderEscalation $assignedEscalation
 * @property-read OrderEscalation $escalation
 * @property-read OrderShipping $shipping
 * @property-read Patient $patient
 * @property-read Provider $provider
 * @property-read Territory $distributorTerritory
 * @property-read ProviderUser|null $providerUser
 * @property-read User|null $createdBy
 * @property-read Provider|Distributor|Manufacturer|null $createdByOrganization
 *
 * @method isActive[scopeIsActive]
 * @method isAvailableInDme[scopeIsAvailableInDme]
 */
#[ObservedBy([OrderObserver::class])]
class Order extends BaseModel
{
    use HasActivityLogs;
    use HasEnvelopes;
    use HasFactory;
    use HasStatusTransitions;
    use IsFilterable;

    public const FOLLOW_UP_INTERVAL = 2; // in days

    protected string $filterClass = OrderFilter::class;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'status' => OrderStatusEnum::class,
            'type' => OrderTypeEnum::class,
            'due_on' => 'date',
            'follow_up_at' => 'date',
            'source_editable' => 'boolean',
            'internal_source' => InternalSourceEnum::class,
        ];
    }

    protected $attributes = [
        'source_editable' => false,
    ];

    /* ATTRIBUTES */

    /**
     * On order list we need to display only product categories without products
     * and it is more efficient to return categories on order level than return products for every product in the order
     */
    protected function computedProductCategories(): Attribute
    {
        return Attribute::make(
            get: function () {
                $productCategories = ProductCategory::query()
                    ->join('product_product_category', 'product_product_category.product_category_id', '=', 'product_categories.id')
                    ->whereIn('product_product_category.product_id', $this->products()->select('products.id'))
                    ->get();

                return $productCategories->keyBy('id')->values();
            },
        );
    }

    /* ATTRIBUTES END */

    /* SCOPES */

    protected static function booted()
    {
        static::addGlobalScope(new NotArchivedOrderScope);
    }

    public function scopeIsActive(Builder $query): void
    {
        $query->whereNotIn($query->qualifyColumn('status'), [
            OrderStatusEnum::DISTRIBUTOR_CANCELED,
            OrderStatusEnum::PROVIDER_CANCELED,
            OrderStatusEnum::MANUFACTURER_CANCELED,
            OrderStatusEnum::SHIPPED,
        ]);
    }

    public function scopeIsAvailableInDme(Builder $query): void
    {
        $query->whereIn($query->qualifyColumn('status'), $this->getAvailableInDmeStatuses());
    }

    public function scopeIsArchived(Builder $query): void
    {
        $query->withoutGlobalScope(NotArchivedOrderScope::class)
            ->where('is_archived', true);
    }

    /* SCOPES END */

    public function isSigned(): bool
    {
        return $this->envelopes()->exists();
    }

    /* RELATIONS */

    public function cancellation(): HasOne
    {
        return $this->hasOne(OrderCancellation::class);
    }

    public function shipping(): HasOne
    {
        return $this->hasOne(OrderShipping::class);
    }

    public function snapshot(): HasOne
    {
        return $this->hasOne(OrderSnapshot::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function distributor(): BelongsTo
    {
        return $this->belongsTo(Distributor::class);
    }

    public function distributorUser(): BelongsTo // AKA Assigned User (Distributor Side)
    {
        return $this->belongsTo(DistributorUser::class)->withTrashed();
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(OrderTask::class);
    }

    public function lead(): HasOne
    {
        return $this->hasOne(Lead::class);
    }

    public function medicalPolicyResponses(): HasMany
    {
        return $this->hasMany(MedicalPolicyResponse::class);
    }

    /**
     * Notice: this method depends on OrganizationContextService which usually is set by OrganizationContext middleware
     * so calling this method before OrganizationContextService initialization will cause an error
     *
     * Defines a relation to Patient through GlobalPatient
     */
    public function patient(): BelongsTo
    {
        /** @var OrganizationContextService $contextService */
        $contextService = app(OrganizationContextService::class);

        return $this->belongsTo(Patient::class, 'global_patient_id', 'global_patient_id')
            ->where('organization_type', $contextService->getType()->value)
            ->where('organization_id', $contextService->getId());
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(OrderProductPivot::$pivotList)->using(OrderProductPivot::class);
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    public function providerUser(): BelongsTo
    {
        return $this->belongsTo(ProviderUser::class);
    }

    public function documents(): BelongsToMany
    {
        // return $this->hasMany(PatientDocument::class);
        return $this->belongsToMany(PatientDocument::class, 'order_patient_document', 'order_id', 'patient_document_id')
            ->select('patient_documents.*');
    }

    public function documentRequests(): HasMany
    {
        return $this->hasMany(DocumentRequest::class);
    }

    public function facility(): BelongsTo
    {
        return $this->belongsTo(Facility::class);
    }

    public function globalPatient(): BelongsTo
    {
        return $this->belongsTo(GlobalPatient::class);
    }

    public function escalation(): HasOne
    {
        return $this->hasOne(OrderEscalation::class, 'order_id');
    }

    public function assignedEscalation(): HasOne
    {
        return $this->hasOne(OrderEscalation::class, 'assigned_order_id');
    }

    public function externalManufacturerUser(): BelongsTo
    {
        return $this->belongsTo(DistributorUser::class, 'external_manufacturer_user_id');
    }

    public function distributorTerritory(): BelongsTo
    {
        return $this->belongsTo(Territory::class);
    }

    public function createdByOrganization(): MorphTo
    {
        return $this->morphTo('createdByOrganization', 'created_by_organization_type', 'created_by_organization_id', 'id');
    }

    public function diagnosisCodes()
    {
        return $this->belongsToMany(ICD10::class, 'order_diagnosis_code', 'order_id', 'icd_10_id')
            ->using(OrderDiagnosisCode::class)
            ->withTimestamps()
            ->withPivot('created_by', 'created_by_type');
    }

    public function orderDiagnosisCodes()
    {
        return $this->hasMany(OrderDiagnosisCode::class, 'order_id');
    }


    public function orderNotes()
    {
        return $this->morphMany(ActivityLog::class, 'activityable')
            ->where('type', ActivityLogTypeEnum::ORDER_NOTE);
    }

    public function orderSource()
    {
        return $this->hasOne(OrderSource::class, 'id', 'order_source_id');
    }


    public function archivedBy()
    {
        return $this->belongsTo(User::class, 'archived_by');
    }

    /* RELATIONS END */

    /* METHODS */
    public function canModifyProducts(): bool
    {
        return $this->type === OrderTypeEnum::NEW_PRESCRIPTION
            && !in_array($this->status, [
                OrderStatusEnum::SHIPPED,
                OrderStatusEnum::DISTRIBUTOR_CANCELED,
                OrderStatusEnum::PROVIDER_CANCELED,
                OrderStatusEnum::MANUFACTURER_CANCELED,
            ]);
    }

    public function getDiagnosisCodes(): array
    {
        $diagnosisCodes = [];

        /** @var MedicalPolicyResponse $medicalPolicyResponse */
        foreach ($this->medicalPolicyResponses as $medicalPolicyResponse) {
            $diagnosisCodes = array_merge($diagnosisCodes, $medicalPolicyResponse->getDiagnosisCodes());
        }

        return $diagnosisCodes;
    }

    public function getProvider(): ?Provider
    {
        // NOTE: only distributor portal side
        return isPortal(PortalEnum::DISTRIBUTOR)
            ? $this->distributor->providerData($this->provider)
            : $this->provider;
    }

    public function getGlobalId(): string
    {
        return $this->global_id;
    }

    public function generateGlobalId(): string
    {
        return 'OR' . $this->id;
    }

    public function getAvailableInDmeStatuses(): array
    {
        return [
            OrderStatusEnum::PENDING_INITIAL_ORDER_REVIEW,
            OrderStatusEnum::PENDING_BENEFITS_INVESTIGATION,
            OrderStatusEnum::PENDING_CUSTOMER_CONTACT,
            OrderStatusEnum::PENDING_DOCUMENT_COLLECTION,
            OrderStatusEnum::PENDING_DOCUMENT_QA,
            OrderStatusEnum::PENDING_PRE_AUTHORIZATION,
            OrderStatusEnum::PENDING_SHIPPING_CONFIRMATION,
            OrderStatusEnum::READY_TO_SHIP,
            OrderStatusEnum::SHIPPED,
            OrderStatusEnum::PROVIDER_CANCELED,
            OrderStatusEnum::DISTRIBUTOR_CANCELED,
        ];
    }

    public function getAvailableInDmeRenewalStatuses(): array
    {
        return [
            OrderStatusEnum::PENDING_PRESCRIPTION_EXPIRATION,
            OrderStatusEnum::PENDING_FACILITY_ONBOARDING,
            OrderStatusEnum::PENDING_PHYSICIAN_ONBOARDING,
            OrderStatusEnum::PENDING_CLINICAL_DOCUMENTATION,
            OrderStatusEnum::PENDING_PATIENT_EVALUATION,
            OrderStatusEnum::PENDING_PHYSICIAN_SIGNATURE,
            OrderStatusEnum::PENDING_DOCUMENT_REVIEW,
            OrderStatusEnum::NEEDS_MORE_INFORMATION,
            OrderStatusEnum::APPROVED,
        ];
    }

    public static function getInitialFollowUpAt(): Carbon
    {
        return todayTz()->addDays(Order::FOLLOW_UP_INTERVAL);
    }

    /* METHODS END */
}
