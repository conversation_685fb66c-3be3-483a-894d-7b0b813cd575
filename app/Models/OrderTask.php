<?php

namespace App\Models;

use App\Enums\OrderTaskStatusEnum;
use App\Enums\OrderTaskTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $order_id
 * @property int $user_id
 * @property int|null $request_id
 * @property OrderTaskTypeEnum $type
 * @property OrderTaskStatusEnum $status
 * @property bool $is_automated
 * @property array $metadata
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class OrderTask extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'user_id',
        'request_id',
        'type',
        'status',
        'is_automated',
        'metadata',
    ];

    protected $casts = [
        'type' => OrderTaskTypeEnum::class,
        'status' => OrderTaskStatusEnum::class,
        'is_automated' => 'boolean',
        'metadata' => 'array',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function documentRequest(): BelongsTo
    {
        return $this->belongsTo(DocumentRequest::class, 'request_id');
    }

    public function isAutomated(): bool
    {
        return $this->is_automated;
    }
}
