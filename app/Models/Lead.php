<?php

namespace App\Models;

use App\Contracts\CommunicationsAwareInterface;
use App\Enums\LeadCanceledReasonEnum;
use App\Enums\LeadQualityRankEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\PatientGenderEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PatientPayerStatusEnum;
use App\Enums\PatientTimezoneEnum;
use App\Enums\PayerTypeEnum;
use App\Filters\LeadFilter;
use App\Models\Pivot\LeadPayerPivot;
use App\Models\Pivot\LeadProductPivot;
use App\Observers\LeadObserver;
use App\Traits\CustomNotifiable;
use App\Traits\HasActivityLogs;
use App\Traits\HasAddresses;
use App\Traits\IsFilterable;
use App\Traits\UnsubscribeEmailCommunications;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Auditable as AuditingAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * @property int $id
 * @property int $distributor_campaign_id
 * @property string $first_name
 * @property string $last_name
 * @property PatientGenderEnum $gender
 * @property Carbon $date_of_birth
 * @property string $email
 * @property int $mobile
 * @property string $zip


 * @property string $communications_text
 * @property PayerTypeEnum $payer_type
 * @property string $source
 * @property string $source_url
 * @property string $marketing_id
 * @property Carbon $created_date
 * @property Carbon $follow_up_at // This is date in the database
 * @property LeadStatusEnum $status
 * @property ?int $assigned_user_id
 * @property ?int $provider_user_id
 * @property LeadQualityRankEnum $quality_rank
 * @property int $patient_id
 * @property Carbon $canceled_date
 * @property LeadCanceledReasonEnum $canceled_reason
 * @property array $notes
 * @property Carbon $converted_date
 * @property Carbon $qualified_date
 * @property ?int $order_id
 * @property bool $source_editable
 * @property string $policy_number
 * @property string $group_number
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read DistributorCampaign $distributorCampaign
 * @property-read User $assignedUser
 * @property-read Patient $patient
 * @property-read ProviderUser $providerUser
 * @property-read Order $order
 * @property-read Collection $activityLogs
 * @property-read Collection $addresses
 * @property-read Collection $payers
 * @property-read Collection $products
 * @property Carbon|null $first_contact_date
 * @property string|null $first_contact_date_notes
 */
#[ObservedBy([LeadObserver::class])]
class Lead extends BaseModel implements CommunicationsAwareInterface, Auditable
{
    use AuditingAuditable;
    use CustomNotifiable;
    use HasActivityLogs;
    use HasAddresses;
    use HasFactory;
    use IsFilterable;
    use SoftDeletes;
    use UnsubscribeEmailCommunications;


    protected string $filterClass = LeadFilter::class;

    protected $fillable = [
        'distributor_campaign_id',
        'first_name',
        'last_name',
        'gender',
        'date_of_birth',
        'email',
        'zip',

        'communications_text',
        'sms_enabled',
        'email_enabled',
        'call_enabled',
        'payer_type',
        'source',
        'source_url',
        'marketing_id',
        'created_date',
        'follow_up_at',
        'status',
        'assigned_user_id',
        'provider_user_id',
        'quality_rank',
        'canceled_date',
        'canceled_reason',
        'notes',
        'converted_date',
        'qualified_date',
        'order_id',
        'patient_id',
        'source_editable',
        'policy_number',
        'group_number',
        'facility_id',
        'created_by',
        'phone_work',
        'phone_home',
        'phone_cell',
        'primary_phone_type',
        'gclid',
        'fbclid',
        'msclkid',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'timezone',
        'preferred_language_id',
        'first_contact_date',
        'first_contact_date_notes',
    ];

    protected function casts(): array
    {
        return [
            'gender' => PatientGenderEnum::class,
            'date_of_birth' => 'date',
            'phone_cell' => 'integer',
            'phone_home' => 'integer',
            'phone_work' => 'integer',

            'created_date' => 'datetime:Y-m-d H:i:s',
            'follow_up_at' => 'date',
            'status' => LeadStatusEnum::class,
            'payer_type' => PayerTypeEnum::class,
            'quality_rank' => LeadQualityRankEnum::class,
            'canceled_date' => 'date',
            'canceled_reason' => LeadCanceledReasonEnum::class,
            'converted_date' => 'date',
            'qualified_date' => 'date',
            'notes' => 'array',
            'timezone' => PatientTimezoneEnum::class,
            'first_contact_date' => 'datetime',
            'preferred_language_id' => 'integer',

        ];
    }

    /*
     * Add all attributes with default values here
     *
     * This attributes needed when model created without default attribute value, we retrieve model without this attributes
     */
    protected $attributes = [

        'sms_enabled' => true,
        'email_enabled' => true,
        'call_enabled' => true,
        'status' => LeadStatusEnum::OPEN->value,
        'quality_rank' => LeadQualityRankEnum::MEDIUM->value,
        'source_editable' => false,
    ];

    public function transformAudit(array $data): array
    {
        foreach (['old_values', 'new_values'] as $key) {

            if (isset($data[$key]['preferred_language_id'])) {
                $lang = Language::find($data[$key]['preferred_language_id']);
                $data[$key]['preferred_language'] = $lang?->name ?? 'Unknown'; // Forces Y-m-d
            }
        }


        return $data;
    }


    public function generateTags(): array
    {
        // Generate a tag only once per request
        if (!app()->bound('current_audit_tag')) {
            app()->instance('current_audit_tag', 'audit_' . now()->timestamp . '_' . uniqid());
        }

        // Return the stored tag for all audits in the request
        return [app('current_audit_tag')];
    }

    public function getNotificationsField(): string
    {
        return 'sms_enabled';
    }

    public function setPhoneCellAttribute($value)
    {
        $this->attributes['mobile'] = $value;
    }

    public function getPhoneCellAttribute()
    {
        return $this->attributes['mobile'] ?? null;
    }

    public function getActivePayerByPriority(PatientPayerPriorityEnum $priority): ?Payer
    {
        if ($this->relationLoaded('payers')) {
            return $this->payers->firstWhere(function ($payer) use ($priority) {
                return $payer->pivot->priority === $priority
                    && $payer->pivot->status === PatientPayerStatusEnum::ACTIVE;
            });
        }

        return $this->payers()
            ->wherePivot('priority', $priority)
            ->wherePivot('status', PatientPayerStatusEnum::ACTIVE)
            ->first();
    }

    // Relationships

    public function distributorCampaign(): BelongsTo
    {
        return $this->belongsTo(DistributorCampaign::class);
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function providerUser(): BelongsTo
    {
        return $this->belongsTo(ProviderUser::class);
    }

    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    public function payers(): BelongsToMany
    {
        return $this->belongsToMany(Payer::class, 'lead_payer')
            ->withPivot(LeadPayerPivot::$pivotList)
            ->using(LeadPayerPivot::class);
    }

    public function primaryPayer(): Attribute
    {
        return new Attribute(
            get: function () {
                $payer = $this->payers()->wherePivot('priority', PatientPayerPriorityEnum::PRIMARY->value)->first();

                if ($payer === null) {
                    $payer = Payer::query()->where('internal_type', PayerTypeEnum::SELF)->first();
                }

                return $payer;
            },
        );
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(LeadProductPivot::$pivotList)->using(LeadProductPivot::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function facility(): BelongsTo
    {
        return $this->belongsTo(Facility::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function customFields(): BelongsToMany
    {
        return $this->belongsToMany(CustomField::class, 'lead_custom_fields')
            ->withPivot('value')
            ->withTimestamps();
    }

    public function leadCustomFields()
    {
        return $this->hasMany(LeadCustomField::class)
            ->whereHas('customField');
    }

    public function preferredLanguage(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'preferred_language_id');
    }

    public function digitalJourneyLead(): HasMany
    {
        return $this->hasMany(DigitalJourneyLead::class, 'lead_id');
    }
    public function pharmacyPayer()
    {
        return $this->morphOne(PharmacyPayer::class, 'payable');
    }

    public function insuranceCardRequests(): HasMany
    {
        return $this->hasMany(LeadInsuranceCardRequest::class);
    }

    public function campaignCancellationHistory()
    {
        return $this->hasMany(LeadCampaignCancellationHistory::class);
    }
}
