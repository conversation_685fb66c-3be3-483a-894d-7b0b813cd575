<?php

namespace App\Models;

use App\Enums\IdentificationTokenTypeEnum;
use App\Enums\ProviderUserPositionEnum;
use App\Enums\ShuttleUserTypeEnum;
use App\Filters\UserFilter;
use App\Notifications\Mail\AccountOwnerChangedMail;
use App\Notifications\Mail\DistributorProviderUserInvitationEmail;
use App\Notifications\Mail\InvitationEmail;
use App\Notifications\Mail\ResetPasswordSuccessEmail;
use App\Observers\UserObserver;
use App\Traits\HasFullName;
use App\Traits\HasIdentificationToken;
use App\Traits\HasImage;
use App\Traits\HasNpi;
use App\Traits\HasSignature;
use App\Traits\IsFilterable;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Passport\HasApiTokens;
use Parental\HasChildren;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property int $id
 * @property string $first_name
 * @property string $middle_name
 * @property string $last_name
 * @property string $email
 * @property string $entity_user_type
 * @property Carbon $last_login_at
 * @property bool $notifications
 * @property int $mobile
 * @property int $phone
 * @property int|null $phone_extension
 * @property int $fax
 * @property ProviderUserPositionEnum $position_type
 * @property string $position_description
 * @property string $password
 * @property bool $is_system
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 * @property-read NpiRecord $npiRecord
 * @property-read Signature $signature
 */
#[ObservedBy(UserObserver::class)]
class User extends Authenticatable
{
    use HasApiTokens;
    use HasChildren;
    use HasFactory;
    use HasFullName;
    use HasIdentificationToken;
    use HasImage;
    use HasNpi;
    use HasRoles {
        roles as public originalRoles;
    }
    use HasSignature;
    use IsFilterable;
    use Notifiable {
        notify as public originalNotify;
        notifyNow as public originalNotifyNow;
    }
    use SoftDeletes;

    protected $guard_name = 'web';

    public $perPage = BaseModel::PER_PAGE;

    protected string $childColumn = 'entity_user_type'; // property belongs to Parental package

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'email',
        'mobile',
        'phone',
        'phone_extension',
        'fax',
        'entity_user_type',
        'notifications',
        'position_type',
        'position_description',
        'cognito_id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    protected function casts(): array
    {
        return [
            'notifications' => 'bool',
            'position_type' => ProviderUserPositionEnum::class,
            'password' => 'hashed',
            'is_system' => 'bool',
            'last_login_at' => 'datetime',
        ];
    }

    /*
     * Add all attributes with default values here
     *
     * This attributes needed when model created without default attribute value, we retrieve model without this attributes
     */
    protected $attributes = [
        'notifications' => true,
        'is_system' => false,
    ];

    protected $with = [
        'roles',
        // it is not used for now
        // 'permissions',
    ];

    protected string $filterClass = UserFilter::class;

    public function getClientDomain($withScheme = false): string
    {
        if ($this->isShuttleHealthEmployee()) {
            $domain = config('app.sh_admin_client_domain');

            return $withScheme ? 'https://' . $domain : $domain;
        }

        return '';
    }

    /* For auth as user from passport in external api */
    public function findForPassport($value)
    {
        if (is_numeric($value)) {
            return $this->where('id', $value)->first();
        } else {
            return $this->where('email', $value)->first();
        }
    }

    public function createInvitation(bool $isOwner = false): string
    {
        $identificationToken = $this->generateIdentificationToken(
            IdentificationTokenTypeEnum::INVITATION,
            self::INVITATION_TOKEN_EXPIRATION_DAYS,
        );

        return $this->getInvitationUrl($isOwner, $identificationToken->token);
    }

    public function createInvitationToken(bool $isOwner = false): IdentificationToken
    {
        return $this->generateIdentificationToken(
            IdentificationTokenTypeEnum::INVITATION,
            self::INVITATION_TOKEN_EXPIRATION_DAYS,
        );
    }

    public function getInvitationUrl(bool $isOwner, string $token): string
    {
        $linkPath = $isOwner ? 'organization-invitation' : 'invitation';

        return sprintf(
            '%s/%s?token=%s',
            $this->getClientDomain(true),
            $linkPath,
            $token,
        );
    }

    public function isShuttleHealthEmployee(): bool
    {
        return $this->hasAnyRole(array_map(fn ($case) => $case->value, ShuttleUserTypeEnum::cases()));
    }

    public function isSHAdmin(): bool
    {
        return $this->hasRole(ShuttleUserTypeEnum::ADMINISTRATOR->value);
    }

    public function belongsToOrganization(Distributor|Manufacturer|Provider $organization): bool
    {
        return $organization->users()->where('id', $this->id)->exists();
    }

    /**
     * Send the given notification.
     *
     * @param  mixed  $instance
     */
    public function notify($instance): void
    {
        if ($this->shouldReceiveNotifications($instance)) {
            $this->originalNotify($instance);
        }
    }

    /**
     * Send the given notification immediately.
     *
     * @param  mixed  $instance
     */
    public function notifyNow($instance, ?array $channels = null): void
    {
        if ($this->shouldReceiveNotifications($instance)) {
            $this->originalNotifyNow($instance, $channels);
        }
    }

    /**
     * Determine whether notification should be sent.
     */
    protected function shouldReceiveNotifications(mixed $instance): bool
    {
        $forcedNotifications = [
            ResetPassword::class,
            ResetPasswordSuccessEmail::class,
            AccountOwnerChangedMail::class,
            InvitationEmail::class,
            DistributorProviderUserInvitationEmail::class,
        ];

        return in_array(get_class($instance), $forcedNotifications) || $this->notifications;
    }

    public function isProviderUser(): bool
    {
        return $this->{$this->childColumn} === ProviderUser::class;
    }

    public function isDistributorUser(): bool
    {
        return $this->{$this->childColumn} === DistributorUser::class;
    }

    public function isManufacturerUser(): bool
    {
        return $this->{$this->childColumn} === ManufacturerUser::class;
    }

    public function isPending(): bool
    {
        // NOTE: if the user has set their password, then they have accepted their invitation and are activated
        return !empty($this->email) && empty($this->password);
    }

    public function isExternal(): bool
    {
        return empty($this->email);
    }

    public function roles(): BelongsToMany
    {
        return $this->originalRoles()->withPivot(['manufacturer_id']);
    }

    public function isZendeskSatisfiable(): bool
    {
        return config('zendesk-laravel.token') && $this->email && $this->phone;
    }

    public function scopeIsSystem($query, bool $value): void
    {
        $query->where('is_system', $value);
    }

    /**
     * Get Cognito groups for this user based on their relations.
     * Returns an array of group names.
     */
    public function getCognitoGroups(): array
    {
        $groups = [];

        if ($this->isDistributorUser()) {
            $groups[] = 'int-dme-scope';
        }

        if ($this->isProviderUser()) {
            $groups[] = 'int-hcp-scope';
        }

        if ($this->isManufacturerUser()) {
            $groups[] = 'int-mfr-scope';
        }

        if ($this->entity_user_type === null) {
            $groups[] = 'int-sha-scope'; // Default group for system users
        }

        // Add more logic here for other user types/roles if needed
        return $groups;
    }

    /**
     * Check if the user is soft deleted (inactive).
     */
    public function isInactive(): bool
    {
        return !is_null($this->deleted_at);
    }

    /**
     * Get all QuickSight users associated with this user.
     */
    public function quicksightUser(): HasMany
    {
        return $this->hasMany(QuicksightUser::class, 'user_id', 'id');
    }

    /**
     * Check if the user has any QuickSight accounts.
     */
    public function hasQuicksightUser(): bool
    {
        return $this->quicksightUser()->exists();
    }

    public function getQuickSightUserName(): string
    {
        return "{$this->first_name}_{$this->last_name}_{$this->id}";
    }
}
