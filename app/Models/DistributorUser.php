<?php

namespace App\Models;

use App\Enums\DistributorUserTypeEnum;
use App\Observers\UserObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Parental\HasParent;
use Spatie\Permission\Models\Role;

#[ObservedBy(UserObserver::class)]
class DistributorUser extends User
{
    use HasFactory;
    use HasParent;

    // public function getMorphClass()
    // {
    //     return static::class;  // or 'App\Models\ManufacturerUser'
    // }

    public function getClientDomain($withScheme = false): string
    {
        $domain = config('app.distributor_client_domain');

        return $withScheme ? 'https://' . $domain : $domain;
    }

    public function distributor(): Attribute
    {
        return new Attribute(
            get: function () {
                return $this->distributors()->first();
            },
        );
    }

    public function distributors(): BelongsToMany
    {
        return $this->belongsToMany(Distributor::class, 'distributor_user', 'user_id', 'distributor_id');
    }

    public function updateUserRole(string $roleName, ?int $manufacturerId): self
    {
        if ($roleName === DistributorUserTypeEnum::EXTERNAL_USER->value) {
            $role = Role::where('name', DistributorUserTypeEnum::EXTERNAL_USER->value)->firstOrFail();

            $this->roles()->syncWithoutDetaching([
                $role->id => ['manufacturer_id' => $manufacturerId],
            ]);
        }

        return $this;
    }

    public function distributorCampaigns(): HasMany
    {
        return $this->hasMany(DistributorCampaign::class, 'created_by');
    }

    public function managedRegions()
    {
        return $this->morphToMany(Region::class, 'manager', 'region_managers')
            ->withTimestamps();
    }
}
