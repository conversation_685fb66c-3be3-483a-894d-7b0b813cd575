<?php

namespace App\Models;

use App\Enums\AddressTypeEnum;
use App\Models\Pivot\DistributorProviderPivot;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

/**
 * @property int $id
 * @property AddressTypeEnum $type
 * @property string $address_line_1
 * @property string $address_line_2
 * @property string $city
 * @property string $state
 * @property string $zip
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Address extends BaseModel implements AuditableContract
{
    use Auditable;
    use HasFactory;

    protected $casts = [
        'zip' => 'string',
        'type' => AddressTypeEnum::class,
    ];

    protected $fillable = [
        'type',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'zip',
        'addressable_type',
    ];

    protected $auditInclude = [
        'street',
        'city',
        'state',
        'zip',
        'country',
        'address_line_1',
        'address_line_2',
        'addressable_type',
        'addressable_id',
        'type',
        'distributor_id',
        'provider_id',
        // Add any additional fields
    ];

    public function transformAudit(array $data): array
    {
        // Standardize the audit format for addresses.
        $data['new_values']['distributor_id'] = $this->getDistributorIdFromContext();
        $data['new_values']['provider_id'] = $this->getProviderIdFromContext();

        // Generate a tag only once per request
        if (!app()->bound('current_audit_tag')) {
            app()->instance('current_audit_tag', 'audit_' . now()->timestamp . '_' . uniqid());
        }

        // Return the stored tag for all audits in the request
        $tags = [app('current_audit_tag')];

        return [
            'auditable_type' => $this->getMorphClass(),
            'auditable_id' => $this->getKey(),
            'event' => $data['event'] ?? 'update',
            'user_id' => $data['user_id'] ?? null,
            'old_values' => $data['old_values'] ?? [],
            'new_values' => $data['new_values'] ?? [],
            'tags' => $tags[0],
        ];
    }

    public function generateTags(): array
    {
        // Generate a tag only once per request
        if (!app()->bound('current_audit_tag')) {
            app()->instance('current_audit_tag', 'audit_' . now()->timestamp . '_' . uniqid());
        }

        // Return the stored tag for all audits in the request
        return [app('current_audit_tag')];
    }

    // In App\Models\Address.php
    public function getDistributorIdFromContext(): ?int
    {
        // Ensure the addressable relation is loaded and is the pivot model
        if ($this->relationLoaded('addressable') || $this->addressable) {
            // Check if the parent is an instance of your pivot
            if ($this->addressable instanceof DistributorProviderPivot) {
                return $this->addressable->distributor_id ?? null;
            }
        }

        return null;
    }

    public function getProviderIdFromContext(): ?int
    {
        if ($this->relationLoaded('addressable') || $this->addressable) {
            if ($this->addressable instanceof DistributorProviderPivot) {
                return $this->addressable->provider_id ?? null;
            }
        }

        return null;
    }

    public function addressable()
    {
        return $this->morphTo();
    }

}
