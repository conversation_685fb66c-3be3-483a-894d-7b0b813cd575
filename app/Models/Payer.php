<?php

namespace App\Models;

use App\Enums\PayerTypeEnum;
use App\Filters\PayerFilter;
use App\Models\Pivot\PatientPayerPivot;
use App\Traits\IsFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use OwenIt\Auditing\Auditable as AuditingAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * @property int $id
 * @property string $name
 * @property PayerTypeEnum $internal_type
 * @property bool $custom
 * @property-read Payer $originPayer
 * @property-read CustomPayer $customPayer
 */
class Payer extends BaseModel implements Auditable
{
    use AuditingAuditable;
    use HasFactory;
    use IsFilterable;

    protected $casts = [
        'internal_type' => PayerTypeEnum::class,
        'custom' => 'boolean',
    ];

    protected $attributes = [
        'custom' => false,
    ];

    protected $guarded = ['id'];

    protected string $filterClass = PayerFilter::class;

    public $timestamps = false;

    public function plans(): HasMany
    {
        return $this->hasMany(PayerPlan::class);
    }

    public function services(): HasMany
    {
        return $this->hasMany(PayerService::class);
    }

    public function patients(): BelongsToMany
    {
        return $this->belongsToMany(Patient::class, 'patient_payer')
            ->withPivot(PatientPayerPivot::$pivotList)
            ->using(PatientPayerPivot::class);
    }

    public function customPayer(): HasOne
    {
        return $this->hasOne(CustomPayer::class, 'payer_id');
    }

    public function originPayer(): BelongsTo
    {
        return $this->belongsTo(Payer::class, 'origin_payer_id');
    }

    public function medicalPolicyForms(): BelongsToMany
    {
        return $this->belongsToMany(MedicalPolicyForm::class, 'medical_policy_form_payer', 'payer_id', 'medical_policy_form_id');
    }

    public function isMedicare(): bool
    {
        return $this->internal_type === PayerTypeEnum::MEDICARE && !$this->custom;
    }

    public function isSelfPayer(): bool
    {
        return $this->internal_type === PayerTypeEnum::SELF && !$this->custom;
    }

    public function isEligibilityCheckSupported(): bool
    {
        if ($this->internal_type === PayerTypeEnum::SELF || $this->custom) {
            return false;
        }

        if ($this->origin_payer_id) {
            return $this->originPayer->isEligibilityCheckSupported();
        }

        if ($this->relationLoaded('services')) {
            return $this->services->count() > 0;
        } else {
            return $this->services()->count() > 0;
        }
    }

    public function generateTags(): array
    {
        // Generate a tag only once per request
        if (!app()->bound('current_audit_tag')) {
            app()->instance('current_audit_tag', 'audit_' . now()->timestamp . '_' . uniqid());
        }

        // Return the stored tag for all audits in the request
        return [app('current_audit_tag')];
    }
}
