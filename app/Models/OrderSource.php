<?php

namespace App\Models;

use App\Filters\OrderSourceFilter;
use App\Traits\IsFilterable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable as AuditingAuditable;
use OwenIt\Auditing\Contracts\Auditable;

class OrderSource extends BaseModel implements Auditable
{
    use AuditingAuditable;
    use IsFilterable;
    use SoftDeletes;

    protected $filterClass = OrderSourceFilter::class;

    protected $fillable = [
        'order_source_name',
        'order_source_slug',
        'is_active',
        'is_system',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
    ];

    /** Relations */
    public function orderSource(): MorphTo
    {
        return $this->morphTo();
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'order_source_id', 'id');
    }
}
