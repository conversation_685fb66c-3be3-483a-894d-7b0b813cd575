<?php

namespace App\Models;

use App\Enums\DigitalJourneyLeadStatusEnum;
use App\Enums\DigitalJourneyTemplateTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DigitalJourneyLead extends Model
{
    protected $fillable = [
        'lead_id',
        'digital_journey_id',
        'digital_journey_template_type',
        'status',
        'send_scheduled_at',
        'sent_at',
    ];

    protected $casts = [
        'status' => DigitalJourneyLeadStatusEnum::class,
        'digital_journey_template_type' => DigitalJourneyTemplateTypeEnum::class,
    ];

    public function digitalJourney(): BelongsTo
    {
        return $this->belongsTo(DigitalJourney::class);
    }

    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }
}
