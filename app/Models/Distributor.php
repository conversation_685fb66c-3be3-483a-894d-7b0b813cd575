<?php

namespace App\Models;

use App\Casts\EncryptString;
use App\Contracts\FileStorageServiceInterface;
use App\Enums\DistributorStatusEnum;
use App\Enums\PortalEnum;
use App\Extensions\Logger;
use App\Filters\DistributorFilter;
use App\Models\Pivot\DistributorPayerPivot;
use App\Models\Pivot\DistributorProviderPivot;
use App\Models\Pivot\DistributorProviderUserPivot;
use App\Traits\HasAddresses;
use App\Traits\HasFileImports;
use App\Traits\HasGalleryImages;
use App\Traits\HasImage;
use App\Traits\HasNpi;
use App\Traits\HasOrganizationSettings;
use App\Traits\HasReports;
use App\Traits\IsFilterable;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property int $phone
 * @property int $fax
 * @property null|int $assigned_fax
 * @property null|string $bt_user
 * @property null|string $bt_pass
 * @property DistributorStatusEnum $status
 * @property bool $allow_shipment_update_fax
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Distributor extends BaseModel
{
    use HasAddresses;
    use HasFactory;
    use HasFileImports;
    use HasGalleryImages;
    use HasImage;
    use HasNpi;
    use HasOrganizationSettings;
    use HasReports;
    use IsFilterable;

    protected string $filterClass = DistributorFilter::class;

    protected $guarded = ['id'];

    protected $hidden = ['bt_pass'];

    protected $attributes = [
        'status' => DistributorStatusEnum::ACTIVE,
        'allow_shipment_update_fax' => false,
    ];

    protected function casts(): array
    {
        return [
            'bt_pass' => EncryptString::class,
            'status' => DistributorStatusEnum::class,
            'allow_shipment_update_fax' => 'boolean',
        ];
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function owner(): Attribute
    {
        return new Attribute(
            get: function () {
                return $this->users()->wherePivot('owner', true)->first();
            },
        );
    }

    public function logo(): Attribute
    {
        return Attribute::get(function () {
            try {
                if ($this->imageFile) {
                    /** @var FileStorageServiceInterface $fileStorage */
                    $fileStorage = app(FileStorageServiceInterface::class);

                    return 'data:image/png;base64,' . base64_encode($fileStorage->getStoredFile($this->imageFile));
                }
            } catch (Exception $e) {
                Logger::info($e->getMessage());
            }

            return null;
        });
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            DistributorUser::class,
            'distributor_user',
            'distributor_id',
            'user_id',
        )
            ->withPivot(['owner']);
    }

    public function providerUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            ProviderUser::class,
            'distributor_provider_user',
            'distributor_id',
            'user_id',
        )
            ->using(DistributorProviderUserPivot::class);
    }

    public function providers(): BelongsToMany
    {
        return $this->belongsToMany(
            Provider::class,
            'distributor_provider',
            'distributor_id',
            'provider_id',
        )
            ->withPivot('id', 'phone')
            ->using(DistributorProviderPivot::class);
    }

    public function patients(): MorphMany
    {
        return $this->morphMany(Patient::class, 'organization');
    }

    public function manufacturerPatients(): HasMany
    {
        return $this->hasMany(DistributorManufacturerPatient::class);
    }

    public function documentRequests(): HasMany
    {
        return $this->hasMany(DocumentRequest::class);
    }

    public function faxes(): HasMany
    {
        return $this->hasMany(Fax::class);
    }

    public function providerData(?Provider $provider): ?Provider
    {
        if ($provider && isPortal(PortalEnum::DISTRIBUTOR)) {
            /** @var Provider $pivotProvider */
            $pivotProvider = $this->providers()->where('provider_id', $provider->id)->first();

            if ($pivotProvider) {
                if ($pivotProvider->pivot->addresses()->exists()) {
                    $pivotProvider->setRelation('addresses', $pivotProvider->pivot->addresses);
                }

                $pivotProvider->phone = $pivotProvider->pivot->phone ?: $provider->phone;

                if ($pivotProvider->pivot->contactNumbers()->exists()) {
                    $pivotProvider->setRelation('contactNumbers', $pivotProvider->pivot->contactNumbers);
                }

                return $pivotProvider;
            }
        }

        return $provider;
    }

    public function facilities(): HasMany
    {
        return $this->hasMany(Facility::class);
    }

    public function canUseBrightree(): bool
    {
        return $this->bt_user && $this->bt_pass;
    }

    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(
            Campaign::class,
            'campaign_distributor',
            'distributor_id',
            'campaign_id',
        );
    }

    public function payers(): BelongsToMany
    {
        return $this->belongsToMany(Payer::class)
            ->withPivot([
                'payer_types',
                'payer_plan_types',
                'serviceable_states',
                'sor_id',
            ])->using(DistributorPayerPivot::class);
    }

    public function distributorCampaigns(): HasMany
    {
        return $this->hasMany(DistributorCampaign::class);
    }

    public function leads(): HasManyThrough
    {
        return $this->hasManyThrough(Lead::class, DistributorCampaign::class);
    }

    public function productManufacturers(): Builder
    {
        return Manufacturer::whereHas('products.distributors', function (Builder $query) {
            $query->where('distributor_id', $this->id);
        });
    }

    public function regions(): MorphMany
    {
        return $this->morphMany(Region::class, 'organization');
    }

    public function districts(): MorphMany
    {
        return $this->morphMany(District::class, 'organization');
    }

    public function territories(): MorphMany
    {
        return $this->morphMany(Territory::class, 'organization');
    }

    public function providerInvitations(): HasMany
    {
        return $this->hasMany(ProviderInvitation::class);
    }

    public function orderActivityLogs(): HasManyThrough
    {
        return $this->hasManyThrough(ActivityLog::class, Order::class, 'distributor_id', 'activityable_id', )
            ->where('activityable_type', Order::class);
    }

    public function documents(): BelongsToMany
    {
        return $this->belongsToMany(
            PatientDocument::class,
            'distributor_document',
            'distributor_id',
            'document_id',
        );
    }

    public function orderSources(): MorphMany
    {
        return $this->morphMany(OrderSource::class, 'organization');
    }

    public function signalWireSmsLogs(): MorphMany
    {
        return $this->morphMany(SignalWireSmsLog::class, 'organization');
    }

    /**
     * Scope a query to only include active distributors.
     */
    public function scopeIsActive(Builder $query): void
    {
        $query->where('status', DistributorStatusEnum::ACTIVE);
    }

    /**
     * Scope a query to only include inactive distributors.
     */
    public function scopeIsInactive(Builder $query): void
    {
        $query->where('status', DistributorStatusEnum::INACTIVE);
    }

    /**
     * Get the distributor fax number which is used to send faxes.
     */
    public function getFaxFromNumber(): string
    {
        return $this->assigned_fax ?? $this->fax;
    }

    public function manufacturers(): BelongsToMany
    {
        return $this->belongsToMany(Manufacturer::class)->withTimestamps();
    }

    public function distributorEmailTemplates()
    {
        return $this->hasMany(DistributorEmailTemplate::class);
    }

    /**
     * Get all message configurations for the distributor.
     */
    public function messageConfigurations(): MorphMany
    {
        return $this->morphMany(MessageConfiguration::class, 'organization');
    }
}
