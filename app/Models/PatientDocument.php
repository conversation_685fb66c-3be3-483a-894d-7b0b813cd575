<?php

namespace App\Models;

use App\Enums\FileTypeEnum;
use App\Enums\LogGroupEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Extensions\Logger;
use App\Filters\DocumentFilter;
use App\Traits\HasActivityLogs;
use App\Traits\IsFilterable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Schema;
use OwenIt\Auditing\Auditable as AuditableTrait;
use OwenIt\Auditing\Contracts\Auditable;

// TODO: rename to Document
/**
 * @property int $id
 * @property int $order_id
 * @property int $relation_id
 * @property int $document_request_id
 * @property int $uploaded_by
 * @property string $title
 * @property array $details
 * @property bool $ltp
 * @property OrderDocumentTypeEnum $type
 * @property OrderDocumentSourceEnum $source
 * @property Carbon|null $signature_date
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $expiration_date
 * @property-read Collection $files
 * @property int $global_patient_id
 */
class PatientDocument extends BaseModel implements Auditable
{
    use AuditableTrait;
    use HasActivityLogs;
    use HasFactory;
    use IsFilterable;
    use SoftDeletes;


    public const CMN_DOCUMENT_TITLE = 'CMN Prescription';

    public const COSC_DOCUMENT_TITLE_FORMAT = 'Signature Certificate for Order [##%u]';

    public const COSC_DOCUMENT_TITLE_FOR_DR_FORMAT = 'Signature Certificate for Document Request [##%u]';

    protected $fillable = [
        'title',
        'type',
        'source',
        'details',
        'signature_date',
        'ltp',
        'order_id',
        'document_request_id',
        'relation_id',
        'uploaded_by',
        'expiration_date',
        'created_by_organization_id',
        'created_by_organization_type',
        'global_patient_id',
    ];

    protected $casts = [
        'type' => OrderDocumentTypeEnum::class,
        'source' => OrderDocumentSourceEnum::class,
        'details' => 'array',
        'signature_date' => 'date',
        'expiration_date' => 'date:Y-m-d',
        'ltp' => 'boolean',
    ];

    protected $attributes = [
        'ltp' => false,
        'source' => OrderDocumentSourceEnum::SYSTEM->value,
    ];

    protected string $filterClass = DocumentFilter::class;

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function documentRequest(): BelongsTo
    {
        return $this->belongsTo(DocumentRequest::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class, 'relation_id')->whereIn(
            'type',
            [
                FileTypeEnum::ORDER_DOCUMENT->value,
                FileTypeEnum::CMN_DOCUMENT->value,
                FileTypeEnum::COSC_DOCUMENT->value,
                FileTypeEnum::AOB->value,
                FileTypeEnum::PATIENT_DOCUMENT->value,
            ],
        );
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function createdByOrganization(): MorphTo
    {
        return $this->morphTo('createdByOrganization', 'created_by_organization_type', 'created_by_organization_id', 'id');
    }

    public function globalPatient(): BelongsTo
    {
        return $this->belongsTo(GlobalPatient::class, 'global_patient_id', 'id');
    }

    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'global_patient_id', 'global_patient_id');
    }

    public function getDistributor(): ?Distributor
    {
        $order = $this->order;

        if ($order) {
            return $order->distributor;
        }

        $documentRequest = $this->documentRequest;

        return $documentRequest?->distributor;
    }

    public function getProvider(): ?Provider
    {
        $order = $this->order;

        if ($order) {
            $provider = $order->provider;

            if (!$provider) {
                $createdByOrganization = $order->createdByOrganization;

                if ($createdByOrganization instanceof Provider) {
                    return $createdByOrganization;
                }
            }
        }

        $documentRequest = $this->documentRequest;

        if ($documentRequest) {
            $provider = $documentRequest->provider;

            if (!$provider) {
                $createdByOrganization = $order->createdByOrganization;

                if ($createdByOrganization instanceof Provider) {
                    return $createdByOrganization;
                }
            }
        }

        return $provider;
    }

    public function getGlobalPatient(): ?GlobalPatient
    {
        // First try direct relationship
        $globalPatient = $this->globalPatient;

        if ($globalPatient) {
            return $globalPatient;
        }

        // Fallback to order relationship
        $globalPatient = $this->order?->globalPatient;

        if ($globalPatient) {
            return $globalPatient;
        }

        // Fallback to document request relationship
        return $this->documentRequest?->globalPatient;
    }

    // move to a document model
    public function associateDistributor(Distributor $distributor): void
    {
        $distributor->documents()->syncWithoutDetaching($this);

        $globalPatient = $this->getGlobalPatient();

        if ($globalPatient === null) {
            Logger::warning('Associating document with patient: global patient missing!', LogGroupEnum::DEFAULT, [
                'distributorId' => $distributor->id,
                'orderDocumentId' => $this->id,
            ]);

            return;
        }

        $distributorPatient = $globalPatient->getPatient($distributor);

        if ($distributorPatient === null) {
            Logger::warning('Associating document with patient: distributor patient missing!', LogGroupEnum::DEFAULT, [
                'globalPatientId' => $globalPatient->id,
                'distributorId' => $distributor->id,
                'orderDocumentId' => $this->id,
            ]);
        }
        // no longer need as a document is directly linked with patient through global patient in patient documents table
        // $distributorPatient?->documents()->attach($this);
    }

    public function associateProvider(Provider $provider): void
    {
        $provider->documents()->syncWithoutDetaching($this);

        $globalPatient = $this->getGlobalPatient();

        if ($globalPatient === null) {
            Logger::warning('Associating document with patient: global patient missing!', LogGroupEnum::DEFAULT, [
                'providerId' => $provider->id,
                'orderDocumentId' => $this->id,
            ]);

            return;
        }

        $providerPatient = $globalPatient->getPatient($provider);

        if ($providerPatient === null) {
            Logger::warning('Associating document with patient: provider patient missing!', LogGroupEnum::DEFAULT, [
                'globalPatientId' => $globalPatient->id,
                'providerId' => $provider->id,
                'orderDocumentId' => $this->id,
            ]);
        }

        // $providerPatient?->documents()->attach($this);
    }

    public function generateTags(): array
    {
        // Generate a tag only once per request
        if (!app()->bound('current_audit_tag')) {
            app()->instance('current_audit_tag', 'audit_' . now()->timestamp . '_' . uniqid());
        }

        // Return the stored tag for all audits in the request
        return [app('current_audit_tag')];
    }

    public function transformAudit(array $data): array
    {
        if (($data['event'] ?? null) === 'deleted') {
            $data['new_values']['reason'] = request()->input('reason');
        }

        return $data;
    }

    public function getTable(): string
    {
        if (Schema::hasTable('patient_documents')) {
            return 'patient_documents';
        }

        return 'order_documents';
    }
}
