<?php

namespace App\Models;

use App\Enums\DocumentRequestCancellationReasonsEnum;
use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Filters\DocumentRequestFilter;
use App\Models\Pivot\DocumentRequestCmsHcpcPivot;
use App\Models\Pivot\DocumentRequestProductPivot;
use App\Observers\DocumentRequestObserver;
use App\Services\ShuttleHealth\OrganizationContextService;
use App\Traits\HasActivityLogs;
use App\Traits\HasEnvelopes;
use App\Traits\IsFilterable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $distributor_id
 * @property int $distributor_user_id
 * @property int $provider_id
 * @property int $provider_user_id
 * @property int $facility_id
 * @property int $global_patient_id
 * @property int $created_by
 * @property DocumentRequestRequestTypeEnum $request_type
 * @property DocumentRequestTypeEnum $type
 * @property DocumentRequestStatusEnum $status
 * @property bool $paused
 * @property bool $is_digital
 * @property bool $delivered_by_email
 * @property bool $delivered_by_phone
 * @property Carbon|null $date_needed
 * @property array $details
 * @property int $months_needed
 * @property string $label
 * @property Carbon|null $appointment_confirmation_date
 * @property Carbon|null $follow_up_at
 * @property DocumentRequestCancellationReasonsEnum $reason
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read User|null $createdBy
 * @property-read Order $order
 * @property-read GlobalPatient $globalPatient
 * @property-read Patient $patient
 * @property-read ProviderUser $providerUser
 * @property-read Distributor $distributor
 * @property-read DistributorUser $distributorUser
 * @property-read Collection $documents
 * @property-read Collection $history
 * @property-read Collection $icd10s
 * @method Builder isActive()
 */
#[ObservedBy([DocumentRequestObserver::class])]
class DocumentRequest extends BaseModel
{
    use HasActivityLogs;
    use HasEnvelopes;
    use HasFactory;
    use IsFilterable;

    public const GLOBAL_ID_PREFIX = 'RE';
    public const FOLLOW_UP_INTERVAL = 2;

    protected string $filterClass = DocumentRequestFilter::class;

    protected $guarded = ['id'];

    protected $casts = [
        'request_type' => DocumentRequestRequestTypeEnum::class,
        'type' => DocumentRequestTypeEnum::class,
        'status' => DocumentRequestStatusEnum::class,
        'paused' => 'boolean',
        'is_digital' => 'boolean',
        'delivered_by_email' => 'boolean',
        'delivered_by_phone' => 'boolean',
        'details' => 'array',
        'reason' => DocumentRequestCancellationReasonsEnum::class,
        'date_needed' => 'date',
        'appointment_confirmation_date' => 'date',
        'follow_up_at' => 'datetime',
    ];

    protected $attributes = [
        'paused' => false,
    ];

    /** SCOPES */

    public function scopeIsActive(Builder $query): Builder
    {
        return $query->whereIn('status', [
            DocumentRequestStatusEnum::PENDING_DIGITAL,
            DocumentRequestStatusEnum::PENDING_ANALOG,
            DocumentRequestStatusEnum::SCHEDULED,
            DocumentRequestStatusEnum::FAILED,
        ]);
    }

    /** SCOPES END */

    /** RELATIONS */

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function history(): HasMany
    {
        return $this->hasMany(DocumentRequestHistory::class);
    }

    public function documents(): HasMany
    {
        return $this->hasMany(PatientDocument::class);
    }

    public function patientSignatureRequest(): HasOne
    {
        return $this->hasOne(PatientAobRequest::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function distributor(): BelongsTo
    {
        return $this->belongsTo(Distributor::class);
    }

    public function distributorUser(): BelongsTo
    {
        return $this->belongsTo(DistributorUser::class)->withTrashed();
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    public function providerUser(): BelongsTo
    {
        return $this->belongsTo(ProviderUser::class);
    }

    public function facility(): BelongsTo
    {
        return $this->belongsTo(Facility::class);
    }

    public function orderTasks(): HasMany
    {
        return $this->hasMany(OrderTask::class, 'request_id');
    }

    public function globalPatient(): BelongsTo
    {
        return $this->belongsTo(GlobalPatient::class);
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(DocumentRequestProductPivot::$pivotList)->using(DocumentRequestProductPivot::class);
    }

    public function cmsHcpcs(): BelongsToMany
    {
        return $this->belongsToMany(CMSHCPC::class, DocumentRequestCmsHcpcPivot::class, 'document_request_id', 'cms_hcpc_id')
            ->withPivot([
                ...DocumentRequestCmsHcpcPivot::$pivotList,
                'product_id',
            ]);
    }

    /**
     * Notice: this method depends on OrganizationContextService which usually is set by OrganizationContext middleware
     * so calling this method before OrganizationContextService initialization will cause an error
     *
     * Defines a relation to Patient through GlobalPatient
     */
    public function patient(): BelongsTo
    {
        /** @var OrganizationContextService $contextService */
        $contextService = app(OrganizationContextService::class);

        return $this->belongsTo(Patient::class, 'global_patient_id', 'global_patient_id')
            ->where('organization_type', $contextService->getType()->value)
            ->where('organization_id', $contextService->getId());
    }

    public function icd10s(): BelongsToMany
    {
        return $this->belongsToMany(ICD10::class, 'document_request_icd_10', 'document_request_id', 'icd_10_id');
    }

    /** RELATIONS END */

    public function getGlobalId(): string
    {
        return self::GLOBAL_ID_PREFIX . $this->id;
    }

    public function getSimilarDocumentRequestsFromNewPrescriptionOrders(): Collection
    {
        return self::query()
            ->where('type', $this->type)
            ->isActive()
            ->where('global_patient_id', $this->global_patient_id)
            ->where('distributor_id', $this->distributor_id)
            ->where('provider_id', $this->provider_id)
            ->get();
    }

    public static function getIdFromGlobalId(string $globalId): ?int
    {
        $id = (int) trim(ltrim($globalId, self::GLOBAL_ID_PREFIX));

        return !empty($id) ? $id : null;
    }

    public static function getIdsFromGlobalIds(array $globalIds): array
    {
        return array_filter(array_map(fn ($value) => self::getIdFromGlobalId($value), $globalIds));
    }

    /**
     * Get products from order if it is linked, otherwise get products from document request
     */
    public function getProducts(): Collection
    {
        $products = $this->order ? $this->order->products : $this->products;

        $filterProductIds = $this->details['cmn']['product_ids'] ?? [];

        if (!empty($filterProductIds)) {
            $products = $products->whereIn('id', $filterProductIds);
        }

        return $products;
    }

    public function canModifyProducts(): bool
    {
        $isStatusEditable = !in_array($this->status, [
            DocumentRequestStatusEnum::APPROVED,
            DocumentRequestStatusEnum::CANCELED,
        ]);

        if (!$isStatusEditable) {
            return false;
        }

        if ($this->order) {
            return $this->order->canModifyProducts();
        }

        return true;
    }

    public function isPrescriptionRenewal(): bool
    {
        return $this->request_type === DocumentRequestRequestTypeEnum::PRESCRIPTION_RENEWAL;
    }
}
