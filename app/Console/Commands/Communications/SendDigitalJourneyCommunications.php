<?php

namespace App\Console\Commands\Communications;

use App\Enums\DigitalJourneyLeadStatusEnum;
use App\Enums\DigitalJourneyTemplateTypeEnum;
use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\DistributorCampaignTypeEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\LogGroupEnum;
use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Extensions\Logger;
use App\Models\DigitalJourney;
use App\Models\DigitalJourneyLead;
use App\Models\EmailTemplate;
use App\Models\Lead;
use App\Models\MessageTemplate;
use App\Notifications\Mail\DigitalJourneyEmail;
use App\Notifications\Sms\Sms;
use App\Traits\HasOrganizationCommunicationWindowOpen;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class SendDigitalJourneyCommunications extends Command
{
    use HasOrganizationCommunicationWindowOpen;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:send-digital-journey-communications {method?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends communications to leads configured via digital journeys';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $digitalJourneys = $this->getDigitalJourneyQuery();
        $journeyCount = 0;
        $totalLeadsProcessed = 0;

        Logger::error('Digital Journey cron started', LogGroupEnum::DEFAULT, [
            'command' => 'communications:send-digital-journey',
            'method' => $this->argument('method') ?? 'all',
        ]);

        foreach ($digitalJourneys->cursor() as $digitalJourney) {
            $journeyCount++;
            $leadsProcessed = $this->processDigitalJourney($digitalJourney);
            $totalLeadsProcessed += $leadsProcessed;
        }

        Logger::error('Digital Journey cron completed', LogGroupEnum::DEFAULT, [
            'total_journeys_processed' => $journeyCount,
            'total_leads_messaged' => $totalLeadsProcessed,
        ]);
    }

    private function getDigitalJourneyQuery(): Builder
    {
        $method = 'message_template';

        if (app()->runningInConsole()) {
            $method = $this->argument('method');
        }

        return DigitalJourney::query()
            ->whereHas('distributorCampaign', function (Builder $query) {
                $query
                    ->where('type', DistributorCampaignTypeEnum::LEAD)
                    ->where('status', DistributorCampaignStatusEnum::ACTIVE);
            })
            ->where('days_from_created_date', '>', 0) // ✅ EXCLUDE Day-0 messages (handled by LeadObserver)
            ->where(function (Builder $query) use ($method) {
                $hasMessage = is_null($method) || $method === DigitalJourneyTemplateTypeEnum::MESSAGE->value;
                $hasEmail = is_null($method) || $method === DigitalJourneyTemplateTypeEnum::EMAIL->value;

                if ($hasMessage) {
                    $query->whereHasMorph('template', MessageTemplate::class, function (Builder $query) {
                        $query->where('message_templates.is_active', true)
                            ->where('message_templates.status', MessageTemplateStatusEnum::APPROVED)
                            ->where('message_templates.type', MessageTemplateTypeEnum::DIGITAL_JOURNEY)
                            ->whereNotNull('message_templates.phone_number');
                    });
                }

                if ($hasEmail) {
                    $query->orWhereHasMorph('template', EmailTemplate::class, function (Builder $query) {
                        $query->where('email_templates.is_active', true);
                    });
                }
            })
            ->with(['template', 'template.distributorTemplate']);
    }

    private function processDigitalJourney(DigitalJourney $digitalJourney): int
    {
        $template = $digitalJourney->template;

        if (!$this->checkEmptyTemplateBody($digitalJourney->template_type, $template)) {
            Logger::error('Digital Journey skipped - Empty template', LogGroupEnum::DEFAULT, [
                'digital_journey_id' => $digitalJourney->id,
                'template_type' => $digitalJourney->template_type->value,
                'days_from_created' => $digitalJourney->days_from_created_date,
            ]);

            return 0;
        }

        // Calculate the target creation date for leads that should be processed
        // Use UTC to avoid timezone issues with lead creation dates
        $targetCreationDate = Carbon::now('UTC')->subDays($digitalJourney->days_from_created_date)->toDateString();

        $leads = $this->getLeadsForDigitalJourney($digitalJourney, $targetCreationDate);
        $leadCount = $leads->count();
        $processedCount = 0;

        Logger::error('Processing Digital Journey', LogGroupEnum::DEFAULT, [
            'digital_journey_id' => $digitalJourney->id,
            'campaign_id' => $digitalJourney->distributor_campaign_id,
            'campaign_name' => $digitalJourney->distributorCampaign?->name,
            'distributor_id' => $digitalJourney->distributorCampaign?->distributor_id,
            'distributor_name' => $digitalJourney->distributorCampaign?->distributor?->name,
            'days_from_created' => $digitalJourney->days_from_created_date,
            'template_type' => $digitalJourney->template_type->value,
            'template_name' => $template->name ?? 'Unknown',
            'qualified_leads_count' => $leadCount,
            'target_creation_date' => $targetCreationDate,
            'current_time_utc' => Carbon::now('UTC')->toDateTimeString(),
            'current_time_app_tz' => todayTz()->toDateTimeString(),
        ]);

        $leads->each(function (Lead $lead) use ($digitalJourney, $template, &$processedCount) {
            $distributor = $digitalJourney->distributorCampaign?->distributor;

            // Get lead's timezone for consistent time window calculations
            $leadTimezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');

            // ✅ Convert lead created_date from UTC to lead's timezone, then add days
            $targetDate = Carbon::parse($lead->created_date)
                ->setTimezone($leadTimezone) // Convert from UTC to lead's timezone
                ->addDays($digitalJourney->days_from_created_date)
                ->setTimeFrom(Carbon::now($leadTimezone)); // Set current time in lead's timezone
            $delayTime = $this->delayTimeInSeconds($distributor, $targetDate, $leadTimezone);

            if ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::MESSAGE) {
                $digitalJourneyLeadId = $this->markDigitalJourneyProcessed($lead, $digitalJourney, $delayTime);
                $this->processMessage($lead, $template, $delayTime, $digitalJourneyLeadId);
            } elseif ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::EMAIL) {
                $digitalJourneyLeadId = $this->markDigitalJourneyProcessed($lead, $digitalJourney, $delayTime);
                $this->processEmail($lead, $template, $delayTime, $digitalJourneyLeadId);
            }
            $processedCount++;

            Logger::error('Digital Journey message queued for lead', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_id' => $digitalJourney->id,
                'delay_seconds' => $delayTime,
                'will_send_at' => Carbon::now()->addSeconds($delayTime)->toDateTimeString(),
                'lead_timezone' => $leadTimezone,
            ]);
        });

        return $processedCount;
    }

    private function checkEmptyTemplateBody($templateType, $template): bool
    {
        // Updated: Only check for distributorTemplate->html_content for EMAIL
        return $templateType === DigitalJourneyTemplateTypeEnum::MESSAGE && !empty($template->body)
            || (
                $templateType === DigitalJourneyTemplateTypeEnum::EMAIL
                && !empty($template->distributorTemplate?->html_content)
            );
    }

    private function getLeadsForDigitalJourney(DigitalJourney $digitalJourney, string $targetCreationDate): Collection
    {
        $distributorCampaign = $digitalJourney->distributorCampaign;
        $templateType = $digitalJourney->template_type;

        $query = $distributorCampaign->leads()
            ->where(function ($query) use ($templateType) {
                if ($templateType === DigitalJourneyTemplateTypeEnum::MESSAGE) {
                    $query->where('sms_enabled', true);
                } elseif ($templateType === DigitalJourneyTemplateTypeEnum::EMAIL) {
                    $query->where('email_enabled', true);
                }
            })
            ->where(function ($q) use ($digitalJourney) {
                // Case 1 + Case 2: no journey lead for this specific digital journey
                $q->whereDoesntHave('digitalJourneyLead', function ($sub) use ($digitalJourney) {
                    $sub->where('digital_journey_id', $digitalJourney->id);
                })
                    // Case 3: journey lead exists for this specific digital journey, but status is pending
                    ->orWhereHas('digitalJourneyLead', function ($sub) use ($digitalJourney) {
                        $sub->where('digital_journey_id', $digitalJourney->id)
                            ->where('status', DigitalJourneyLeadStatusEnum::PENDING);
                    });
            })
            ->with('digitalJourneyLead')
            ->where('status', LeadStatusEnum::OPEN)
            ->whereDate('created_date', $targetCreationDate); // ✅ Leads created on specific calendar date (UTC)

        return $query->get();
    }

    private function processMessage($lead, MessageTemplate $template, $delay = 0, int $digitalJourneyLeadId = null): void
    {
        $notification = new Sms($template->body, $template->phone_number, null, $lead, false, $digitalJourneyLeadId);

        if ($delay) {
            $lead->notify($notification->delay($delay));
        } else {
            $lead->notify($notification);
        }
    }

    private function processEmail($lead, EmailTemplate $template, $delay = 0, int $digitalJourneyLeadId = null): void
    {
        $notification = new DigitalJourneyEmail($template, $digitalJourneyLeadId);

        if ($delay) {
            $lead->notify($notification->delay($delay));
        } else {
            $lead->notify($notification);
        }
    }

    private function markDigitalJourneyProcessed(Lead $lead, DigitalJourney $digitalJourney, int $delayTime = 0): int
    {
        $digitalJourneyLead = new DigitalJourneyLead;
        $digitalJourneyLead->lead_id = $lead->id;
        $digitalJourneyLead->digital_journey_id = $digitalJourney->id;
        $digitalJourneyLead->digital_journey_template_type = $digitalJourney->template_type;
        $digitalJourneyLead->status = DigitalJourneyLeadStatusEnum::PROCESSING;
        $digitalJourneyLead->send_scheduled_at = Carbon::now(config('app.sh_timezone'))->addSeconds($delayTime);
        $digitalJourneyLead->save();

        return $digitalJourneyLead->id;
    }


    // Uncomment for debugging. Digital Journey Cron Job debugging
    // Donot add comment on PR. This is intentional
    //
    public function debug()
    {
        $leads = [];
        $digitalJourneys = $this->getDigitalJourneyQuery();

        foreach ($digitalJourneys->cursor() as $digitalJourney) {
            $targetCreationDate = Carbon::now('UTC')->subDays($digitalJourney->days_from_created_date)->toDateString();
            $leads['digital-journey-' . $digitalJourney->id] = $this->getLeadsForDigitalJourney($digitalJourney, $targetCreationDate);
        }

        return $leads;
    }

    /**
     * Debug method to check what leads would be processed for each day
     */
    public function debugDayAnalysis()
    {
        $analysis = [];
        $digitalJourneys = $this->getDigitalJourneyQuery();

        foreach ($digitalJourneys->cursor() as $digitalJourney) {
            $daysFromCreated = $digitalJourney->days_from_created_date;
            $targetDate = Carbon::now('UTC')->subDays($daysFromCreated)->toDateString();

            $leads = $digitalJourney->distributorCampaign->leads()
                ->where('status', LeadStatusEnum::OPEN)
                ->whereDate('created_date', $targetDate)
                ->get();

            $analysis["Day +{$daysFromCreated}"] = [
                'target_date' => $targetDate,
                'total_leads' => $leads->count(),
                'leads' => $leads->map(function ($lead) {
                    return [
                        'id' => $lead->id,
                        'created_date' => $lead->created_date,
                        'created_date_utc' => $lead->created_date->toDateTimeString(),
                        'sms_enabled' => $lead->sms_enabled,
                        'email_enabled' => $lead->email_enabled,
                    ];
                })->toArray(),
            ];
        }

        return $analysis;
    }

}
