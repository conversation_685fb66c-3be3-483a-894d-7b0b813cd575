<?php

namespace App\Console\Commands\Communications;

use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class DebugDigitalJourneyCommand extends Command
{
    protected $signature = 'debug:digital-journey';
    protected $description = 'Debug digital journey day calculation issues';

    public function handle(): void
    {
        $this->info('=== Digital Journey Debug Analysis ===');

        // Get the digital journey command instance
        $digitalJourneyCommand = app(SendDigitalJourneyCommunications::class);

        // Run the debug analysis
        $analysis = $digitalJourneyCommand->debugDayAnalysis();

        $this->info('Current Time (UTC): ' . Carbon::now('UTC')->toDateTimeString());
        $this->info('Current Time (App TZ): ' . todayTz()->toDateTimeString());
        $this->info('App Timezone: ' . config('app.sh_timezone'));

        $this->newLine();

        foreach ($analysis as $dayLabel => $dayData) {
            $this->info("=== {$dayLabel} ===");
            $this->info("Target Date: {$dayData['target_date']}");
            $this->info("Total Leads Found: {$dayData['total_leads']}");

            if ($dayData['total_leads'] > 0) {
                $this->info('Leads:');

                foreach ($dayData['leads'] as $lead) {
                    $this->line("  - Lead ID: {$lead['id']}");
                    $this->line("    Created Date (UTC): {$lead['created_date_utc']}");
                    $this->line('    SMS Enabled: ' . ($lead['sms_enabled'] ? 'Yes' : 'No'));
                    $this->line('    Email Enabled: ' . ($lead['email_enabled'] ? 'Yes' : 'No'));
                }
            } else {
                $this->warn('No leads found for this day!');
            }

            $this->newLine();
        }

        // Log the analysis for Sentry
        Logger::info('Digital Journey Debug Analysis', LogGroupEnum::DEFAULT, [
            'analysis' => $analysis,
            'current_time_utc' => Carbon::now('UTC')->toDateTimeString(),
            'current_time_app_tz' => todayTz()->toDateTimeString(),
            'app_timezone' => config('app.sh_timezone'),
        ]);

        $this->info('Debug analysis completed. Check logs for detailed information.');
    }
}
