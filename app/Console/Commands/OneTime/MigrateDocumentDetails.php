<?php

namespace App\Console\Commands\OneTime;

use App\Enums\OrderDocumentTypeEnum;
use App\Models\PatientDocument;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateDocumentDetails extends Command
{
    protected $signature = 'documents:migrate-details-structure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrates documents details to new structure';

    public function handle(): int
    {
        $progressBar = $this->output->createProgressBar(DB::table('order_documents')->whereNotNull('details')->count());
        $progressBar->start();

        DB::transaction(function () use ($progressBar) {
            PatientDocument::query()
                ->whereNotNull('details')
                ->chunkById(100, function (Collection $documents) use ($progressBar) {
                    $documents->each(function (PatientDocument $orderDocument) use ($progressBar) {
                        if (empty($orderDocument->details)) {
                            $orderDocument->update(['details' => null]);
                        } elseif ($orderDocument->type === OrderDocumentTypeEnum::LAB) {
                            $this->handleLab($orderDocument);
                        }

                        $progressBar->advance();
                    });
                });
        });

        $progressBar->finish();

        $this->output->success('Successful');

        return self::SUCCESS;
    }

    private function handleLab(PatientDocument $orderDocument): void
    {
        $details = $orderDocument->details;

        if (!is_array($details)) {
            $messageFormat = 'Unexpected lab details element! Request|history Id=%d, details=`%s`';

            throw new Exception(sprintf(
                $messageFormat,
                $orderDocument->id,
                json_encode($orderDocument->details),
            ));
        }

        // return if it already has new structure
        if (!empty($details['lab_names'])) {
            return;
        }

        $details = ['lab_names' => $details];

        $orderDocument->update(['details' => $details]);
    }
}
