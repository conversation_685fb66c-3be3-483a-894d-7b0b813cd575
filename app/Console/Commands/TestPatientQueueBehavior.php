<?php

namespace App\Console\Commands;

use App\Models\Lead;
use App\Models\Order;
use App\Models\Patient;
use App\Notifications\Sms\OrderShipped;
use App\Notifications\Sms\Sms;
use App\Services\MessageQueueingService;
use Illuminate\Console\Command;

class TestPatientQueueBehavior extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:patient-queue {--type=} {--patient-id=} {--lead-id=} {--order-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test patient queue behavior for different message types';

    public function __construct(
        protected MessageQueueingService $queueingService,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type') ?? 'all';

        $this->info("Testing patient queue behavior for message type: {$type}");
        $this->newLine();

        switch ($type) {
            case 'digital-journey-day-0':
                $this->testDigitalJourneyDay0();
                break;
            case 'digital-journey-other':
                $this->testDigitalJourneyOther();
                break;
            case 'order-status':
                $this->testOrderStatus();
                break;
            case 'ad-hoc':
                $this->testAdHoc();
                break;
            case 'on-demand':
                $this->testOnDemand();
                break;
            case 'insurance-card':
                $this->testInsuranceCard();
                break;
            case 'communication-control':
                $this->testCommunicationControl();
                break;
            case 'all':
                $this->testAllTypes();
                break;
            default:
                $this->error("Unknown message type: {$type}");

                return 1;
        }

        return 0;
    }

    protected function testDigitalJourneyDay0(): void
    {
        $this->info('Testing Digital Journey Day-0 messages...');

        if ($leadId = $this->option('lead-id')) {
            $lead = Lead::find($leadId);

            if (!$lead) {
                $this->error("Lead not found: {$leadId}");

                return;
            }

            $distributor = $lead->distributorCampaign?->distributor;
            $timezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');

            $shouldQueue = $this->queueingService->shouldQueueMessage('digital_journey_day_0', $distributor, $timezone);

            $this->info("Lead #{$leadId}:");
            $this->info('  - Distributor: ' . ($distributor ? $distributor->name : 'None'));
            $this->info("  - Timezone: {$timezone}");
            $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes (ERROR!)' : 'No (Correct - Day-0 is always real-time)'));

            // Test actual notification
            $this->info('  - Sending test message...');
            // Day-0 messages are sent WITH lead context but forced real-time
            $notification = new Sms('Test Day-0 message', null, null, $lead, true);
            $lead->notify($notification);
            $this->info('  - Message sent immediately (real-time with forceRealTime=true)');
        } else {
            $this->warn('Please provide --lead-id to test Digital Journey Day-0');
        }
    }

    protected function testDigitalJourneyOther(): void
    {
        $this->info('Testing Digital Journey Other Days messages...');

        if ($leadId = $this->option('lead-id')) {
            $lead = Lead::find($leadId);

            if (!$lead) {
                $this->error("Lead not found: {$leadId}");

                return;
            }

            $distributor = $lead->distributorCampaign?->distributor;
            $timezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');

            $shouldQueue = $this->queueingService->shouldQueueMessage('digital_journey_other_days', $distributor, $timezone);

            $this->info("Lead #{$leadId}:");
            $this->info('  - Should always queue: ' . ($shouldQueue ? 'Yes' : 'No'));

            // Test actual notification
            $this->info('  - Sending test message...');
            $notification = new Sms('Test Other Days message', null, null, $lead);
            $lead->notify($notification);
            $this->info('  - Message queued successfully');
        } else {
            $this->warn('Please provide --lead-id to test Digital Journey Other Days');
        }
    }

    protected function testOrderStatus(): void
    {
        $this->info('Testing Order Status Update messages...');

        if ($orderId = $this->option('order-id')) {
            $order = Order::find($orderId);

            if (!$order) {
                $this->error("Order not found: {$orderId}");

                return;
            }

            $patient = $order->globalPatient?->getPatient($order->distributor);
            $distributor = $order->distributor;
            $timezone = $patient?->timezone?->value ?? $patient?->timezone ?? config('app.sh_timezone');

            $shouldQueue = $this->queueingService->shouldQueueMessage('order_status_update', $distributor, $timezone);

            $this->info("Order #{$orderId}:");
            $this->info('  - Patient: ' . ($patient ? $patient->getFullName() : 'None'));
            $this->info('  - Distributor: ' . ($distributor ? $distributor->name : 'None'));
            $this->info("  - Timezone: {$timezone}");
            $this->info('  - Communication window open: ' . ($distributor && !$shouldQueue ? 'Yes' : 'No'));
            $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes' : 'No'));

            // Test actual notification
            if ($patient) {
                $this->info('  - Sending test order shipped notification...');
                $notification = new OrderShipped($order, $distributor?->assigned_fax);
                $patient->notify($notification);
                $this->info('  - Message sent/queued successfully');
            }
        } else {
            $this->warn('Please provide --order-id to test Order Status Updates');
        }
    }

    protected function testAdHoc(): void
    {
        $this->info('Testing Ad Hoc messages...');

        if ($leadId = $this->option('lead-id')) {
            $lead = Lead::find($leadId);

            if (!$lead) {
                $this->error("Lead not found: {$leadId}");

                return;
            }

            $distributor = $lead->distributorCampaign?->distributor;
            $timezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');

            $shouldQueue = $this->queueingService->shouldQueueMessage('ad_hoc', $distributor, $timezone);

            $this->info("Lead #{$leadId}:");
            $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes (ERROR!)' : 'No (Correct)'));

            // Test actual notification
            $this->info('  - Sending test ad hoc message...');
            $notification = new Sms('Test ad hoc message', null, 1, null, true); // forceRealTime = true
            $lead->notify($notification);
            $this->info('  - Message sent immediately');
        } else {
            $this->warn('Please provide --lead-id to test Ad Hoc messages');
        }
    }

    protected function testOnDemand(): void
    {
        $this->info('Testing On-Demand messages...');

        if ($patientId = $this->option('patient-id')) {
            $patient = Patient::find($patientId);

            if (!$patient) {
                $this->error("Patient not found: {$patientId}");

                return;
            }

            $distributor = $patient->organization;
            $timezone = $patient->timezone?->value ?? $patient->timezone ?? config('app.sh_timezone');

            $shouldQueue = $this->queueingService->shouldQueueMessage('on_demand', $distributor, $timezone);

            $this->info("Patient #{$patientId}:");
            $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes (ERROR!)' : 'No (Correct)'));

            // Test actual notification
            $this->info('  - Sending test on-demand message...');
            $notification = new Sms('Test on-demand message', null, 1, null, true); // forceRealTime = true
            $patient->notify($notification);
            $this->info('  - Message sent immediately');
        } else {
            $this->warn('Please provide --patient-id to test On-Demand messages');
        }
    }

    protected function testInsuranceCard(): void
    {
        $this->info('Testing Insurance Card Request messages...');

        $shouldQueue = $this->queueingService->shouldQueueMessage('insurance_card_request', null, null);

        $this->info('Insurance Card Requests:');
        $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes (ERROR!)' : 'No (Correct)'));
        $this->info('  - These messages are always sent real-time');
    }

    protected function testCommunicationControl(): void
    {
        $this->info('Testing Communication Control messages...');

        $shouldQueue = $this->queueingService->shouldQueueMessage('communication_control', null, null);

        $this->info('Communication Control (STOP/START):');
        $this->info('  - Should queue: ' . ($shouldQueue ? 'Yes (ERROR!)' : 'No (Correct)'));
        $this->info('  - These messages are always sent real-time');
    }

    protected function testAllTypes(): void
    {
        $this->testDigitalJourneyDay0();
        $this->newLine();
        $this->testDigitalJourneyOther();
        $this->newLine();
        $this->testOrderStatus();
        $this->newLine();
        $this->testAdHoc();
        $this->newLine();
        $this->testOnDemand();
        $this->newLine();
        $this->testInsuranceCard();
        $this->newLine();
        $this->testCommunicationControl();
    }
}
