<?php

namespace App\Console\Commands\Npi;

use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Models\OpenSearch\LocalNpiRecord;
use Generator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use OpenSearch\Client as OpenSearchClient;
use ZipArchive;

class ImportNpiRecords extends Command
{
    // NPI CSV headers
    public const NPI = 'NPI';
    public const ENTITY_TYPE_CODE = 'Entity Type Code';
    public const REPLACEMENT_NPI = 'Replacement NPI';
    public const EMPLOYER_IDENTIFICATION_NUMBER = 'Employer Identification Number (EIN)';
    public const PROVIDER_ORGANIZATION_NAME = 'Provider Organization Name (Legal Business Name)';
    public const PROVIDER_LAST_NAME = 'Provider Last Name (Legal Name)';
    public const PROVIDER_FIRST_NAME = 'Provider First Name';
    public const PROVIDER_MIDDLE_NAME = 'Provider Middle Name';
    public const PROVIDER_NAME_PREFIX_TEXT = 'Provider Name Prefix Text';
    public const PROVIDER_NAME_SUFFIX_TEXT = 'Provider Name Suffix Text';
    public const PROVIDER_CREDENTIAL_TEXT = 'Provider Credential Text';
    public const PROVIDER_OTHER_ORGANIZATION_NAME = 'Provider Other Organization Name';
    public const PROVIDER_OTHER_ORGANIZATION_NAME_TYPE_CODE = 'Provider Other Organization Name Type Code';
    public const PROVIDER_OTHER_LAST_NAME = 'Provider Other Last Name';
    public const PROVIDER_OTHER_FIRST_NAME = 'Provider Other First Name';
    public const PROVIDER_OTHER_MIDDLE_NAME = 'Provider Other Middle Name';
    public const PROVIDER_OTHER_NAME_PREFIX_TEXT = 'Provider Other Name Prefix Text';
    public const PROVIDER_OTHER_NAME_SUFFIX_TEXT = 'Provider Other Name Suffix Text';
    public const PROVIDER_OTHER_CREDENTIAL_TEXT = 'Provider Other Credential Text';
    public const PROVIDER_OTHER_LAST_NAME_TYPE_CODE = 'Provider Other Last Name Type Code';
    public const PROVIDER_FIRST_LINE_BUSINESS_MAILING_ADDRESS = 'Provider First Line Business Mailing Address';
    public const PROVIDER_SECOND_LINE_BUSINESS_MAILING_ADDRESS = 'Provider Second Line Business Mailing Address';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_CITY_NAME = 'Provider Business Mailing Address City Name';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_STATE_NAME = 'Provider Business Mailing Address State Name';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_POSTAL_CODE = 'Provider Business Mailing Address Postal Code';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_COUNTRY_CODE = 'Provider Business Mailing Address Country Code (If outside U.S.)';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_TELEPHONE_NUMBER = 'Provider Business Mailing Address Telephone Number';
    public const PROVIDER_BUSINESS_MAILING_ADDRESS_FAX_NUMBER = 'Provider Business Mailing Address Fax Number';
    public const PROVIDER_FIRST_LINE_BUSINESS_PRACTICE_LOCATION_ADDRESS = 'Provider First Line Business Practice Location Address';
    public const PROVIDER_SECOND_LINE_BUSINESS_PRACTICE_LOCATION_ADDRESS = 'Provider Second Line Business Practice Location Address';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_CITY_NAME = 'Provider Business Practice Location Address City Name';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_STATE_NAME = 'Provider Business Practice Location Address State Name';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_POSTAL_CODE = 'Provider Business Practice Location Address Postal Code';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_COUNTRY_CODE = 'Provider Business Practice Location Address Country Code (If outside U.S.)';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_TELEPHONE_NUMBER = 'Provider Business Practice Location Address Telephone Number';
    public const PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_FAX_NUMBER = 'Provider Business Practice Location Address Fax Number';
    public const PROVIDER_ENUMERATION_DATE = 'Provider Enumeration Date';
    public const LAST_UPDATE_DATE = 'Last Update Date';
    public const NPI_DEACTIVATION_REASON_CODE = 'NPI Deactivation Reason Code';
    public const NPI_DEACTIVATION_DATE = 'NPI Deactivation Date';
    public const NPI_REACTIVATION_DATE = 'NPI Reactivation Date';
    public const PROVIDER_GENDER_CODE = 'Provider Gender Code';
    public const AUTHORIZED_OFFICIAL_LAST_NAME = 'Authorized Official Last Name';
    public const AUTHORIZED_OFFICIAL_FIRST_NAME = 'Authorized Official First Name';
    public const AUTHORIZED_OFFICIAL_MIDDLE_NAME = 'Authorized Official Middle Name';
    public const AUTHORIZED_OFFICIAL_TITLE_OR_POSITION = 'Authorized Official Title or Position';
    public const AUTHORIZED_OFFICIAL_TELEPHONE_NUMBER = 'Authorized Official Telephone Number';
    public const HEALTHCARE_PROVIDER_TAXONOMY_CODE_1 = 'Healthcare Provider Taxonomy Code_1';
    public const PROVIDER_LICENSE_NUMBER_1 = 'Provider License Number_1';
    public const PROVIDER_LICENSE_NUMBER_STATE_CODE_1 = 'Provider License Number State Code_1';
    public const HEALTHCARE_PROVIDER_PRIMARY_TAXONOMY_SWITCH_1 = 'Healthcare Provider Primary Taxonomy Switch_1';
    public const OTHER_PROVIDER_IDENTIFIER_1 = 'Other Provider Identifier_1';
    public const OTHER_PROVIDER_IDENTIFIER_TYPE_CODE_1 = 'Other Provider Identifier Type Code_1';
    public const OTHER_PROVIDER_IDENTIFIER_STATE_1 = 'Other Provider Identifier State_1';
    public const OTHER_PROVIDER_IDENTIFIER_ISSUER_1 = 'Other Provider Identifier Issuer_1';
    public const IS_SOLE_PROPRIETOR = 'Is Sole Proprietor';
    public const IS_ORGANIZATION_SUBPART = 'Is Organization Subpart';
    public const PARENT_ORGANIZATION_LBN = 'Parent Organization LBN';
    public const PARENT_ORGANIZATION_TIN = 'Parent Organization TIN';
    public const AUTHORIZED_OFFICIAL_NAME_PREFIX_TEXT = 'Authorized Official Name Prefix Text';
    public const AUTHORIZED_OFFICIAL_NAME_SUFFIX_TEXT = 'Authorized Official Name Suffix Text';
    public const AUTHORIZED_OFFICIAL_CREDENTIAL_TEXT = 'Authorized Official Credential Text';
    public const HEALTHCARE_PROVIDER_TAXONOMY_GROUP_1 = 'Healthcare Provider Taxonomy Group_1';
    public const CERTIFICATION_DATE = 'Certification Date';

    // Command constants
    public const DEFAULT_BULK_SIZE = 10000;
    public const TMP_DATA_FOLDER = 'npi-data';
    private object $headersObject;
    private bool $debug = false;

    protected $signature = 'npi:import-csv {--month=} {--year=} {--debug} {bulkSize?}';
    protected $description = 'Read NPI CSV files with headers and values, then index in elastic search.
    {--month} is a month (June) of NPI data file to download, {--year} is a year (2024) of NPI data file to download,
    optional parameter {bulkSize} is a number of records to index in one bulk. Default is 10000.
    Additionally, you can pass {--debug} to enable debug mode.';

    public function handle(): int
    {
        $this->debug = $this->option('debug');

        /** @var OpenSearchClient $osClient */
        $osClient = DB::connection('opensearch')->getClient();
        $npiRecordModel = new LocalNpiRecord;
        $npiRecordModel->deleteIndexIfExists();
        $npiRecordsIndexName = $npiRecordModel->getIndex();
        $this->createNpiRecordsIndex($osClient, $npiRecordsIndexName);

        if (!$this->clearTemporaryFiles()) {
            return self::FAILURE;
        }

        $archiveFileSuffix = $this->getArchiveFileSuffix($this->option('month'), $this->option('year'));

        if (!$this->downloadAndUnarchive($archiveFileSuffix)) {
            return self::FAILURE;
        }

        $bulkSize = $this->argument('bulkSize') ?? self::DEFAULT_BULK_SIZE;

        $fileResource = Storage::disk('local-tmp-content')->readStream(
            $this->getNpiDataFullFileName($archiveFileSuffix),
        );

        $npiRecords = $this->readBigCsv($fileResource);
        $headers = $npiRecords->current();
        $npiRecords->next();

        $bulkData = [];
        $headersAsKeys = array_flip($headers);
        $this->headersObject = (object) $headersAsKeys;

        for ($i = 0; $npiRecords->valid(); $npiRecords->next()) {
            $row = $npiRecords->current();

            // Skip inactive records
            if (!$this->isRecordActive($row)) {
                continue;
            }

            $npiRecordData = $this->makeNpiRecordData($row);
            $bulkData[] = $npiRecordData;

            if (++$i % 100000 === 0) {
                $this->info('Count: ' . $i);
            }

            if ($i % $bulkSize === 0) {
                $this->bulkCreate($osClient, $npiRecordsIndexName, $bulkData);
                $bulkData = [];
            }
        }

        $this->logInfo('NPI records data indexed successfully.');

        $this->clearTemporaryFiles();

        return self::SUCCESS;
    }

    private function bulkCreate(OpenSearchClient $client, $index, $npiRecordsData): array
    {
        $params = ['body' => []];

        foreach ($npiRecordsData as $item) {
            $params['body'][] = [
                'create' => [
                    '_index' => $index,
                    '_id' => $item['number'],
                ],
            ];

            $params['body'][] = $item;
        }

        $result = $client->bulk($params);

        if ($result['errors']) {
            $this->logError('Bulk create error');

            if ($this->debug) {
                $this->logError(json_encode($result));
            }
        }

        return $result;
    }

    private function createNpiRecordsIndex(OpenSearchClient $client, $index): array
    {
        $indexConfigFile = database_path('indexes/npi_records.json');
        $indexConfig = json_decode(file_get_contents($indexConfigFile), true, 512, JSON_THROW_ON_ERROR);

        return $client->indices()->create([
            'index' => $index,
            'body' => $indexConfig,
        ]);
    }

    public function readBigCsv($fileResource, $delimiter = ',', $enclosure = '"', $escape = '\\'): Generator
    {
        if (!$fileResource) {
            throw new \Exception("Error opening file: $fileResource");
        }

        // Loop through lines
        while (($data = fgetcsv($fileResource, 0, $delimiter, $enclosure, $escape)) !== false) {
            // Process each line (data is an array)
            yield $data;
        }

        // Close file
        fclose($fileResource);
    }

    private function makeNpiRecordData(array $row): array
    {
        $record = [
            // we don't have precise epoch values in CSV and don't know timezone, so skipped
            // 'created_epoch' => $this->getRowValue($row, self::PROVIDER_ENUMERATION_DATE), // same as enumeration_date
            // 'last_updated_epoch' => $this->getRowValue($row, self::LAST_UPDATE_DATE), // same as last_updated
            'enumeration_type' => $this->getRowValue($row, self::ENTITY_TYPE_CODE), // 1,2 -> NPI-1, NPI-2 in API
            'number' => $this->getRowValue($row, self::NPI),
            'basic' => [
                // common fields
                'enumeration_date' => $this->formatDate($this->getRowValue($row, self::PROVIDER_ENUMERATION_DATE)),
                'last_updated' => $this->formatDate($this->getRowValue($row, self::LAST_UPDATE_DATE)),
                'status' => $this->isRecordActive($row) ? 'A' : 'D',

                // described in file documentation but missing in API, so skipped
                // 'replacement_npi' => $this->getRowValue($row, self::REPLACEMENT_NPI),
                // 'ein' => $this->getRowValue($row, self::EMPLOYER_IDENTIFICATION_NUMBER),
                // 'deactivation_reason_code' => $this->getRowValue($row, self::NPI_DEACTIVATION_REASON_CODE), // Death,Disbandment,Fraud,Other
                // 'deactivation_date' => $this->formatDate($this->getRowValue($row, self::NPI_DEACTIVATION_DATE)),
                // 'reactivation_date' => $this->formatDate($this->getRowValue($row, self::NPI_REACTIVATION_DATE)),
                // 'parent_organization_ein' => $this->getRowValue($row, self::PARENT_ORGANIZATION_TIN),
                // 'authorized_official_credential' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_CREDENTIAL_TEXT),
            ],
            'addresses' => [
                [
                    'address_purpose' => 'LOCATION',
                    'address_1' => $this->getRowValue($row, self::PROVIDER_FIRST_LINE_BUSINESS_PRACTICE_LOCATION_ADDRESS),
                    'address_2' => $this->getRowValue($row, self::PROVIDER_SECOND_LINE_BUSINESS_PRACTICE_LOCATION_ADDRESS),
                    'city' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_CITY_NAME),
                    'state' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_STATE_NAME),
                    'postal_code' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_POSTAL_CODE),
                    'country_code' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_COUNTRY_CODE),
                    'telephone_number' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_TELEPHONE_NUMBER),
                    'fax_number' => $this->getRowValue($row, self::PROVIDER_BUSINESS_PRACTICE_LOCATION_ADDRESS_FAX_NUMBER),
                ],
                [
                    'address_purpose' => 'MAILING',
                    'address_1' => $this->getRowValue($row, self::PROVIDER_FIRST_LINE_BUSINESS_MAILING_ADDRESS),
                    'address_2' => $this->getRowValue($row, self::PROVIDER_SECOND_LINE_BUSINESS_MAILING_ADDRESS),
                    'state' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_STATE_NAME),
                    'city' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_CITY_NAME),
                    'postal_code' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_POSTAL_CODE),
                    'country_code' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_COUNTRY_CODE),
                    'telephone_number' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_TELEPHONE_NUMBER),
                    'fax_number' => $this->getRowValue($row, self::PROVIDER_BUSINESS_MAILING_ADDRESS_FAX_NUMBER),
                ],
            ],
        ];

        if ($record['enumeration_type'] === '1') {
            // individual fields
            $record['basic'] = array_merge($record['basic'], [
                // individual fields
                'first_name' => $this->getRowValue($row, self::PROVIDER_FIRST_NAME),
                'last_name' => $this->getRowValue($row, self::PROVIDER_LAST_NAME),
                'middle_name' => $this->getRowValue($row, self::PROVIDER_MIDDLE_NAME),
                'credential' => $this->getRowValue($row, self::PROVIDER_CREDENTIAL_TEXT),
                'sole_proprietor' => $this->getRowValue($row, self::IS_SOLE_PROPRIETOR), // X,Y,N -> YES,NO in API
                'gender' => $this->getRowValue($row, self::PROVIDER_GENDER_CODE),
                'name_prefix' => $this->getRowValue($row, self::PROVIDER_NAME_PREFIX_TEXT),
                'name_suffix' => $this->getRowValue($row, self::PROVIDER_NAME_SUFFIX_TEXT),
            ]);
        } elseif ($record['enumeration_type'] === '2') {
            // organization fields
            $record['basic'] = array_merge($record['basic'], [
                'organization_name' => $this->getRowValue($row, self::PROVIDER_ORGANIZATION_NAME),
                'organizational_subpart' => $this->getRowValue($row, self::IS_ORGANIZATION_SUBPART), // X,Y,N -> YES,NO in API
                'parent_organization_legal_business_name' => $this->getRowValue($row, self::PARENT_ORGANIZATION_LBN),
                'certification_date' => $this->formatDate($this->getRowValue($row, self::CERTIFICATION_DATE)),
                'authorized_official_first_name' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_FIRST_NAME),
                'authorized_official_last_name' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_LAST_NAME),
                'authorized_official_middle_name' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_MIDDLE_NAME),
                'authorized_official_telephone_number' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_TELEPHONE_NUMBER),
                'authorized_official_title_or_position' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_TITLE_OR_POSITION),
                'authorized_official_name_prefix' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_NAME_PREFIX_TEXT),
                'authorized_official_name_suffix' => $this->getRowValue($row, self::AUTHORIZED_OFFICIAL_NAME_SUFFIX_TEXT),
            ]);
        }

        return $record;
    }

    private function isRecordActive(array $row): bool
    {
        return empty($this->getRowValue($row, self::NPI_DEACTIVATION_DATE)) || !empty($this->getRowValue($row, self::NPI_REACTIVATION_DATE));
    }

    private function formatDate(?string $date): ?string
    {
        if (empty($date)) {
            return null;
        }

        return date('Y-m-d', strtotime($date));
    }

    private function getRowValue(array $row, string $header): string|int|null
    {
        if (empty($row[$this->headersObject->$header])) {
            return null;
        }

        return $row[$this->headersObject->$header];
    }

    private function downloadAndUnarchive(string $fileSuffix): bool
    {
        $urlFormat = 'https://download.cms.gov/nppes/NPPES_Data_Dissemination_%s.zip';
        $url = sprintf($urlFormat, $fileSuffix);

        $this->logInfo('Start downloading file from ' . $url);

        $zipFullFilename = $this->getNpiArchiveFullFileName($fileSuffix);

        // Stream the download
        Http::sink($zipFullFilename)->timeout(120)->get($url);

        $this->logInfo('File downloaded successfully.');

        $this->logInfo('Unarchiving started. File: ' . $zipFullFilename);

        $zip = new ZipArchive;

        // Zip File Name
        $res = $zip->open($zipFullFilename);

        if ($res === false) {
            $this->logError('Zip open error');

            return false;
        }

        // Unzip Path
        $res = $zip->extractTo(
            $this->getTempDirectory(),
        );

        $zip->close();

        if ($res === false) {
            $this->logError('Unzipping error');

            return false;
        }

        $this->logInfo('Unzipped successfully');

        return true;
    }

    private function getTempDirectoryName(): string
    {
        return 'npi-data';
    }

    private function getTempDirectory(): string
    {
        $dir = $this->getTempDirectoryName();

        if (!Storage::disk('local-tmp-content')->exists($dir)) {
            Storage::disk('local-tmp-content')->makeDirectory($dir);
        }

        return Storage::disk('local-tmp-content')->path($dir);
    }

    private function getArchiveFileSuffix(string $month, int $year): string
    {
        return ucfirst(strtolower($month)) . '_' . $year;
    }

    private function getNpiDataFullFileName(): string
    {
        $files = Storage::disk('local-tmp-content')->files($this->getTempDirectoryName());

        $pfileFileName = collect($files)->firstWhere(function ($file) {
            return strpos($file, 'npidata_pfile') !== false && strpos($file, 'fileheader') === false;
        });

        return $pfileFileName;
    }

    private function getNpiArchiveFullFileName(string $fileSuffix): string
    {
        return $this->getTempDirectory() . '/npi_archive_' . $fileSuffix . '.zip';
    }

    private function clearTemporaryFiles(): bool
    {
        $result = Storage::disk('local-tmp-content')->deleteDirectory('npi-data');

        if ($result) {
            $this->logInfo('Temporary files cleared. Directory: ' . $this->getTempDirectory());
        } else {
            $this->logError('Temporary files clearing error. Directory: ' . $this->getTempDirectory());
        }

        return $result;
    }

    private function logInfo($message): void
    {
        $this->info($message);
        Logger::info($message, LogGroupEnum::NPI);
    }

    private function logError($message): void
    {
        $this->error($message);
        Logger::error($message, LogGroupEnum::NPI);
    }
}
