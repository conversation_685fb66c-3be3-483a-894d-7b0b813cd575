<?php

namespace App\Queue\Connectors;

use App\Queue\PatientCommunicationAwareRedisQueue;
use App\Services\PatientCommunicationWindowService;
use Illuminate\Queue\Connectors\RedisConnector;

class PatientCommunicationRedisConnector extends RedisConnector
{
    public function connect(array $config)
    {

        $queue = $config['queue'];
        $connection = $config['connection'] ?? $this->connection ?? 'default';

        return new PatientCommunicationAwareRedisQueue(
            $this->redis,
            $queue,
            $connection,
            app(PatientCommunicationWindowService::class),
            $config,
        );
    }
}
