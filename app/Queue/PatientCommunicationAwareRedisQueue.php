<?php

namespace App\Queue;

use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Services\PatientCommunicationWindowService;
use Illuminate\Contracts\Queue\Job;
use Illuminate\Queue\Jobs\RedisJob;
use Illuminate\Queue\RedisQueue;

class PatientCommunicationAwareRedisQueue extends RedisQueue
{
    protected PatientCommunicationWindowService $windowService;

    public function __construct(
        $redis,
        string $default,
        string $connectionName,
        PatientCommunicationWindowService $windowService,
        array $options = [],
    ) {
        // parent expects: redis, default, connectionName, options
        parent::__construct($redis, $default, $connectionName);

        $this->windowService = $windowService;
    }

    /**
     * Override pop to defer jobs outside allowed window.
     */
    public function pop($queue = null, $index = 0): ?Job
    {
        $this->migrate($prefixed = $this->getQueue($queue));

        $block = !$this->secondaryQueueHadJob && $index == 0;

        [$job, $reserved] = $this->retrieveNextJob($prefixed, $block);

        if ($index == 0) {
            $this->secondaryQueueHadJob = false;
        }

        if (!$reserved || !$job) {
            return null;
        }

        $job = new RedisJob(
            $this->container,
            $this,
            $job,
            $reserved,
            $this->connectionName,
            $queue ?: $this->default,
        );

        // ✅ Check communication window directly
        if (!$this->windowService->canRun($job)) {
            $payload = $job->payload();

            Logger::info('Message deferred due to communication window', LogGroupEnum::DEFAULT, [
                'job_id' => $payload['uuid'] ?? 'unknown',
                'job_name' => $payload['displayName'] ?? 'unknown',
                'queue' => $queue ?: $this->default,
            ]);

            $uuid = $payload['uuid'] ?? null;

            if ($uuid) {
                $reservedKey = $this->getQueue($queue) . ':reserved';
                $delayedKey = $this->getQueue($queue) . ':delayed';

                $items = $this->redis->connection($this->connection)->zrange($reservedKey, 0, -1);

                foreach ($items as $raw) {
                    $decoded = json_decode($raw, true);

                    if (isset($decoded['uuid']) && $decoded['uuid'] === $uuid) {
                        // Remove from reserved queue
                        $this->redis->connection($this->connection)->zrem($reservedKey, $raw);

                        // Calculate next available time (2 minutes from now)
                        $availableAt = time() + config('queue.connections.redis-patient-communication.poll_interval', 120);

                        // Decrement attempts to prevent infinite retries
                        if (isset($decoded['attempts']) && $decoded['attempts'] > 0) {
                            $decoded['attempts']--;
                        }

                        // Push to delayed queue with updated attempts
                        $this->redis->connection($this->connection)->zadd(
                            $delayedKey,
                            $availableAt,
                            json_encode($decoded),
                        );

                        break;
                    }
                }
            }

            return null;
        }

        return $job;
    }
}
