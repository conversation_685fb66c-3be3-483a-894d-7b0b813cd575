<?php

namespace App\Repositories;

use App\Enums\QuicksightEnum;
use App\Models\QuicksightUser;

class QuicksightUserRepository
{
    public function findReaderByDistributor(int $distributorId, int $userId): ?QuicksightUser
    {
        return QuicksightUser::where('distributor_id', $distributorId)
//            ->where('user_id', $userId)
            ->where('role', QuicksightEnum::READER)
            ->where('is_active', true)
            ->first();
    }

    public function findAuthorByDistributor(int $distributorId, int $userId): ?QuicksightUser
    {
        return QuicksightUser::where('distributor_id', $distributorId)
            ->where('role', QuicksightEnum::AUTHOR)
            ->where('user_id', $userId)
            ->where('is_active', true)
            ->first();
    }
}
