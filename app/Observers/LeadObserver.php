<?php

namespace App\Observers;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\DigitalJourneyLeadStatusEnum;
use App\Enums\DigitalJourneyTemplateTypeEnum;
use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\LogGroupEnum;
use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Extensions\Logger;
use App\Jobs\Leads\SendLeadConvertedToOrderEventToFacebook;
use App\Jobs\Leads\SendLeadConvertedToOrderEventToGoogle;
use App\Jobs\Leads\SendLeadQualifiedEventToFacebook;
use App\Jobs\Leads\SendLeadQualifiedEventToGoogle;
use App\Jobs\Leads\SendLeadUnqualifiedEventToFacebook;
use App\Jobs\Leads\SendLeadUnQualifiedEventToGoogle;
use App\Models\DigitalJourney;
use App\Models\DigitalJourneyLead;
use App\Models\EmailTemplate;
use App\Models\Lead;
use App\Models\LeadCampaignCancellationHistory;
use App\Models\MessageTemplate;
use App\Models\Patient;
use App\Notifications\Mail\DigitalJourneyEmail;
use App\Notifications\Sms\CommunicationStart;
use App\Notifications\Sms\CommunicationStop;
use App\Notifications\Sms\Sms;
use App\Services\ShuttleHealth\ActivityLogger;
use App\Traits\HasOrganizationCommunicationWindowOpen;
use Illuminate\Support\Carbon;
use ValueError;

/**
 * This observer is responsible for logging the lead updates and sending the communication start/stop SMS notifications.
 */
class LeadObserver
{
    use HasOrganizationCommunicationWindowOpen;
    public function updated(Lead $lead): void
    {
        if (!app()->runningInConsole() && $lead->utm_source == 'facebook') {
            $ipAddress = request()->ip();
            $userAgent = request()->userAgent();

            $leadStatus = $lead->status;

            try {
                switch ($lead->status) {
                    case LeadStatusEnum::CANCELED:
                        SendLeadUnqualifiedEventToFacebook::dispatch($lead, $ipAddress, $userAgent);
                        SendLeadUnQualifiedEventToGoogle::dispatch($lead);
                        break;

                    case LeadStatusEnum::CONVERTED:
                        SendLeadConvertedToOrderEventToFacebook::dispatch($lead, $ipAddress, $userAgent);
                        SendLeadConvertedToOrderEventToGoogle::dispatch($lead);
                        break;

                    default:
                        break;
                }

                if (
                    $lead->wasChanged('qualified_date')
                    && is_null($lead->getOriginal('qualified_date'))
                    && !is_null($lead->qualified_date)
                ) {
                    SendLeadQualifiedEventToFacebook::dispatch($lead, $ipAddress, $userAgent);
                    SendLeadQualifiedEventToGoogle::dispatch($lead);
                }
            } catch (ValueError $e) {
                Logger::error('Invalid lead status for Facebook event dispatch', LogGroupEnum::FACEBOOK_PIXEL, [
                    'leadId' => $lead->id,
                    'status' => $lead->status,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if ($lead->wasChanged('qualified_date')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_QUALIFIED_DATE, [
                'from' => $lead->getOriginal('qualified_date')?->toDateString(),
                'to' => $lead->qualified_date?->toDateString(),
            ]);
        }

        if ($lead->wasChanged('sms_enabled')
            || $lead->wasChanged('email_enabled')
            || $lead->wasChanged('call_enabled')) {

            // Make sure campaign is active before proceeding
            if ($lead->distributorCampaign->status === DistributorCampaignStatusEnum::ACTIVE) {
                $fromNumber = $lead->distributorCampaign->distributor?->assigned_fax;

                // Check if any of the communication options are enabled and there is an opt-in text
                $distributorId = $lead->distributorCampaign->distributor_id;

                $patient = Patient::where('mobile', $lead->mobile)->first();
                $activeOrders = $patient?->orders()->where('distributor_id', $distributorId)->isActive()->exists();

                if ($lead->sms_enabled || $lead->email_enabled || $lead->call_enabled && ($activeOrders)) {
                    $lead->notify(new CommunicationStart($fromNumber), true);

                    // Only send opt-in to patient if not already opted in
                    if ($patient && !$patient->sms_enabled) {
                        $patient->update(['sms_enabled' => true]);
                        $patient->notify(new CommunicationStart($fromNumber), true);
                    }
                }
                // If all communications are disabled and there is an opt-out text, send CommunicationStop
                elseif (!$lead->sms_enabled && !$lead->email_enabled && !$lead->call_enabled) {
                    $lead->notify(new CommunicationStop($fromNumber), true);
                } else {
                    Logger::warning('There is no opt in or opt out text for such campaign', LogGroupEnum::SIGNAL_WIRE, [
                        'campaignName' => $lead->distributorCampaign->name,
                        'distributorId' => $lead->distributorCampaign->distributor_id,
                    ]);
                }
            }
        }

        if ($lead->wasChanged('status')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_STATUS, [
                'from' => $lead->getOriginal('status'),
                'to' => $lead->status,
            ]);

            // Record cancellation history when lead is cancelled
            if ($lead->status === LeadStatusEnum::CANCELED && $lead->getOriginal('status') !== LeadStatusEnum::CANCELED) {
                LeadCampaignCancellationHistory::create([
                    'lead_id' => $lead->id,
                    'distributor_campaign_id' => $lead->distributor_campaign_id,
                    'canceled_reason' => $lead->canceled_reason,
                    'canceled_date' => $lead->canceled_date ?? now()->toDateString(),
                    'canceled_by' => getUser()?->id,
                ]);
            }
        }

        if ($lead->wasChanged('first_name')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_LEAD_FIRST_NAME, [
                'from' => $lead->getOriginal('first_name'),
                'to' => $lead->first_name,
            ]);
        }

        if ($lead->wasChanged('last_name')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_LEAD_LAST_NAME, [
                'from' => $lead->getOriginal('last_name'),
                'to' => $lead->last_name,
            ]);
        }

    }

    public function created(Lead $lead): void
    {
        // Ensure the campaign is active before proceeding
        if ($lead->distributorCampaign->status === DistributorCampaignStatusEnum::ACTIVE) {
            $fromNumber = $lead->distributorCampaign->distributor?->assigned_fax;

            if ($lead->sms_enabled || $lead->email_enabled || $lead->call_enabled) {
                $lead->notify(new CommunicationStart($fromNumber));
            } else {
                Logger::warning('There is no opt in or opt out text for such campaign', LogGroupEnum::SIGNAL_WIRE, [
                    'campaignName' => $lead->distributorCampaign->name,
                    'distributorId' => $lead->distributorCampaign->distributor_id,
                ]);
            }

            // ✅ Process Day-0 digital journey messages immediately
            $this->processDay0DigitalJourneyMessages($lead);
        }
    }

    /**
     * Process Day-0 digital journey messages immediately upon lead creation.
     * This ensures Day-0 messages are sent in real-time instead of waiting for cron.
     */
    private function processDay0DigitalJourneyMessages(Lead $lead): void
    {
        // Find all Day-0 digital journeys for this campaign (SMS only)
        $day0Journeys = DigitalJourney::query()
            ->where('distributor_campaign_id', $lead->distributor_campaign_id)
            ->where('days_from_created_date', 0) // Day-0 messages only
            ->where('template_type', DigitalJourneyTemplateTypeEnum::MESSAGE) // ✅ Only SMS templates
            ->whereHasMorph('template', MessageTemplate::class, function ($query) {
                $query->where('message_templates.is_active', true)
                    ->where('message_templates.status', MessageTemplateStatusEnum::APPROVED)
                    ->where('message_templates.type', MessageTemplateTypeEnum::DIGITAL_JOURNEY)
                    ->whereNotNull('message_templates.phone_number');
            })
            ->with('template')
            ->get();

        // Log Day-0 digital journey processing summary
        Logger::info('Processing Day-0 digital journey messages', LogGroupEnum::DEFAULT, [
            'lead_id' => $lead->id,
            'campaign_id' => $lead->distributor_campaign_id,
            'campaign_name' => $lead->distributorCampaign?->name,
            'distributor_id' => $lead->distributorCampaign?->distributor_id,
            'total_day0_journeys' => $day0Journeys->count(),
            'lead_sms_enabled' => $lead->sms_enabled,
            'lead_created_date' => $lead->created_date,
        ]);

        foreach ($day0Journeys as $digitalJourney) {
            $this->processDay0DigitalJourney($lead, $digitalJourney);
        }
    }

    /**
     * Process a single Day-0 digital journey for the lead.
     */
    private function processDay0DigitalJourney(Lead $lead, DigitalJourney $digitalJourney): void
    {
        $template = $digitalJourney->template;


        // Check if template has content
        if (!$this->checkEmptyTemplateBody($digitalJourney->template_type, $template)) {
            return;
        }

        // Check if lead qualifies for this message type
        if ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::MESSAGE && !$lead->sms_enabled) {
            Logger::info('Day-0 journey skipped - SMS disabled for lead', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_id' => $digitalJourney->id,
                'template_name' => $template->name ?? 'Unknown',
            ]);

            return;
        }

        if ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::EMAIL && !$lead->email_enabled) {
            Logger::info('Day-0 journey skipped - Email disabled for lead', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_id' => $digitalJourney->id,
                'template_name' => $template->name ?? 'Unknown',
            ]);

            return;
        }

        // Check if already processed (prevent duplicates)
        $existingJourneyLead = DigitalJourneyLead::where('lead_id', $lead->id)
            ->where('digital_journey_id', $digitalJourney->id)
            ->where('digital_journey_template_type', $digitalJourney->template_type)
            ->first();

        if ($existingJourneyLead) {
            Logger::info('Day-0 journey skipped - Already processed', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_id' => $digitalJourney->id,
                'existing_journey_lead_id' => $existingJourneyLead->id,
            ]);

            return; // Already processed
        }

        $distributor = $digitalJourney->distributorCampaign?->distributor;
        $leadTimezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');

        // For Day-0 messages, target date is lead creation time in lead's timezone
        $targetDate = Carbon::parse($lead->created_date)
            ->setTimezone($leadTimezone); // Convert from UTC to lead's timezone

        // Day-0 messages are always sent real-time, no delay
        $delayTime = 0;

        // Get associated patient for patient communication queue
        $patient = null;

        if ($lead->mobile) {
            $patient = Patient::where('mobile', $lead->mobile)->first();
        }

        // Mark as processed first to get the ID
        $digitalJourneyLeadId = $this->markDigitalJourneyProcessed($lead, $digitalJourney, 0);

        // Send the message (Day-0 messages are always real-time)
        if ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::MESSAGE) {
            $this->processDay0Message($lead, $template, $digitalJourneyLeadId);
        } elseif ($digitalJourney->template_type === DigitalJourneyTemplateTypeEnum::EMAIL) {
            $this->processDay0Email($lead, $template, $digitalJourneyLeadId);
        }

        Logger::info('Day-0 digital journey message sent successfully', LogGroupEnum::DEFAULT, [
            'lead_id' => $lead->id,
            'digital_journey_id' => $digitalJourney->id,
            'template_type' => $digitalJourney->template_type->value,
            'template_name' => $template->name ?? 'Unknown',
            'distributor_id' => $distributor?->id,
            'distributor_name' => $distributor?->name,
            'real_time' => true,
            'force_real_time' => true,
            'patient_found' => $patient !== null,
        ]);
    }

    /**
     * Process Day-0 SMS message.
     */
    private function processDay0Message(Lead $lead, MessageTemplate $template, int $digitalJourneyLeadId): void
    {
        // Day-0 messages are always sent real-time, never queued
        // Pass the lead context but force real-time delivery
        $notification = new Sms($template->body, $template->phone_number, null, $lead, true, $digitalJourneyLeadId);
        $lead->notify($notification);
    }

    /**
     * Process Day-0 email message.
     */
    private function processDay0Email(Lead $lead, EmailTemplate $template, int $digitalJourneyLeadId): void
    {
        // Day-0 messages are always sent real-time, never queued
        $notification = new DigitalJourneyEmail($template, $digitalJourneyLeadId);
        $lead->notify($notification);
    }

    /**
     * Check if template has content.
     */
    private function checkEmptyTemplateBody($templateType, $template): bool
    {
        return $templateType === DigitalJourneyTemplateTypeEnum::MESSAGE && !empty($template->body)
            || (
                $templateType === DigitalJourneyTemplateTypeEnum::EMAIL
                && !empty($template->distributorTemplate?->html_content)
            );
    }

    /**
     * Mark digital journey as processed.
     */
    private function markDigitalJourneyProcessed(Lead $lead, DigitalJourney $digitalJourney, int $delayTime = 0): int
    {
        $digitalJourneyLead = new DigitalJourneyLead;
        $digitalJourneyLead->lead_id = $lead->id;
        $digitalJourneyLead->digital_journey_id = $digitalJourney->id;
        $digitalJourneyLead->digital_journey_template_type = $digitalJourney->template_type;
        $digitalJourneyLead->status = DigitalJourneyLeadStatusEnum::PROCESSING;
        $digitalJourneyLead->send_scheduled_at = Carbon::now(config('app.sh_timezone'))->addSeconds($delayTime);
        $digitalJourneyLead->save();

        return $digitalJourneyLead->id;
    }
}
