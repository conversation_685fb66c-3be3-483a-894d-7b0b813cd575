<?php

namespace App\Observers;

use App\Actions\ShuttleHealth\UpdateOrderTaskAction;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\LogGroupEnum;
use App\Enums\OrderTaskStatusEnum;
use App\Events\DocumentRequestCompleted;
use App\Events\DocumentRequestCreated;
use App\Extensions\Logger;
use App\Models\DocumentRequest;
use App\Services\ShuttleHealth\ActivityLogger;

/**
 * This observer is responsible for storing the document request updates to history,
 * dispatching events when document requests are created, and dispatching completion
 * events when document requests reach completed statuses.
 */
class DocumentRequestObserver
{
    /**
     * Handle the DocumentRequest "created" event.
     */
    public function created(DocumentRequest $documentRequest): void
    {
        DocumentRequestCreated::dispatch($documentRequest);
    }

    public function updated(DocumentRequest $documentRequest): void
    {
        $newValues = $documentRequest->getChanges();
        $oldValues = [];

        foreach ($newValues as $key => $newValue) {
            $oldValues[$key] = $documentRequest->getRawOriginal($key);
        }

        ActivityLogger::writeModelChanges(
            parent: $documentRequest,
            newValues: $newValues,
            oldValues: $oldValues,
        );

        // Check if status was changed to a completed state
        if (isset($newValues['status'])) {
            $newStatus = $documentRequest->status;
            $oldStatus = DocumentRequestStatusEnum::tryFrom($oldValues['status']);

            // Dispatch completion event if status changed to received or approved
            if (
                in_array($newStatus, [
                    DocumentRequestStatusEnum::RECEIVED,
                    DocumentRequestStatusEnum::APPROVED,
                ])
                && $oldStatus !== $newStatus
            ) {
                DocumentRequestCompleted::dispatch($documentRequest);
            }
        }

        if (!$documentRequest->wasChanged('status')) {
            return;
        }

        // Skip if the document request has no order_id - automated tasks require an order
        if (!$documentRequest->order_id) {
            Logger::info(
                "Skipping automated task update - document request {$documentRequest->id} has no associated order",
                LogGroupEnum::DEFAULT,
                ['document_request_id' => $documentRequest->id],
            );

            return;
        }

        $this->updateOrderTaskWhenDocumentRequestStatusChanges($documentRequest->orderTasks, $oldValues);
    }

    private function updateOrderTaskWhenDocumentRequestStatusChanges($orderTasks, $documentRequestOldValues): void
    {
        $action = new UpdateOrderTaskAction;

        foreach ($orderTasks as $orderTask) {
            $metadata = $orderTask->metadata ?? [];
            $taskData = [];

            if (in_array($documentRequestOldValues['status'], [
                DocumentRequestStatusEnum::RECEIVED->value,
                DocumentRequestStatusEnum::APPROVED->value,
            ])) {
                $metadata['initial_request_date'] = now()->format('Y-m-d');
                $metadata['complete_date'] = null;
                $taskData = array(
                    'status' => OrderTaskStatusEnum::IN_PROGRESS->value,
                    'metadata' => $metadata,
                );
            } elseif (in_array($documentRequestOldValues['status'], [
                DocumentRequestStatusEnum::PENDING_ANALOG->value,
                DocumentRequestStatusEnum::PENDING_DIGITAL->value,
            ])) {
                $metadata['complete_date'] = now()->format('Y-m-d');
                $taskData = array(
                    'status' => OrderTaskStatusEnum::DONE->value,
                    'metadata' => $metadata,
                );
            } else {
                continue;
            }

            $action->execute($orderTask, $taskData);
        }

    }
}
