<?php

namespace App\Listeners;

use App\Actions\ShuttleHealth\CreateAutomatedTaskAction;
use App\Actions\ShuttleHealth\UpdateTaskStatusAction;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\LogGroupEnum;
use App\Enums\OrderTaskTypeEnum;
use App\Events\DocumentRequestCreated;
use App\Extensions\Logger;
use Exception;

/**
 * This listener is responsible for creating automated tasks when document requests are created.
 */
class DocumentRequestCreatedListener
{
    public function __construct(
        private CreateAutomatedTaskAction $createTaskAction,
        private UpdateTaskStatusAction $updateTaskAction,
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(DocumentRequestCreated $event): void
    {
        try {
            $documentRequest = $event->documentRequest;

            // Skip if the document request has no order_id - automated tasks require an order
            if (!$documentRequest->order_id) {
                Logger::info(
                    "Skipping automated task creation - document request {$documentRequest->id} has no associated order",
                    LogGroupEnum::DEFAULT,
                    ['document_request_id' => $documentRequest->id],
                );

                return;
            }

            // Skip if an automated task already exists for this request
            if ($this->createTaskAction->taskExistsForRequest($documentRequest)) {
                Logger::info(
                    "Automated task already exists for document request {$documentRequest->id}",
                    LogGroupEnum::DEFAULT,
                    ['document_request_id' => $documentRequest->id],
                );

                return;
            }

            // Determine task type based on document request type
            $taskType = $this->mapDocumentRequestTypeToTaskType($documentRequest->type);

            // Determine the user ID for the task
            $userId = $documentRequest->distributor_user_id
                ?? $documentRequest->distributor?->owner?->id
                ?? $documentRequest->created_by;

            if (!$userId) {
                Logger::error(
                    "Cannot create automated task - no user ID available for document request {$documentRequest->id}",
                    LogGroupEnum::DEFAULT,
                    ['document_request_id' => $documentRequest->id],
                );

                return;
            }

            // Create automated task
            $taskData = [
                'type' => $taskType,
                'user_id' => $userId,
                'metadata' => [
                    'task_name' => "{$documentRequest->type->value} Task",
                ],
            ];

            if ($documentRequest->type === DocumentRequestTypeEnum::LAB) {
                $details = is_string($documentRequest->details)
                    ? json_decode($documentRequest->details, true)
                    : $documentRequest->details;

                $labNames = array_filter(array_column($details['labs'] ?? [], 'name'));
                $taskData['metadata']['lab_names'] = $labNames;
            }

            $orderTask = $this->createTaskAction->execute($documentRequest, $taskData);

            Logger::info(
                "Automated task created for document request {$documentRequest->id}",
                LogGroupEnum::DEFAULT,
                [
                    'document_request_id' => $documentRequest->id,
                    'order_task_id' => $orderTask->id,
                    'task_type' => $taskType->value,
                ],
            );

        } catch (Exception $e) {
            Logger::error(
                "Failed to create automated task for document request {$event->documentRequest->id}: {$e->getMessage()}",
                LogGroupEnum::DEFAULT,
                [
                    'document_request_id' => $event->documentRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ],
            );
        }
    }

    /**
     * Map document request type to order task type
     */
    private function mapDocumentRequestTypeToTaskType($documentRequestType): OrderTaskTypeEnum
    {
        return match ($documentRequestType) {
            DocumentRequestTypeEnum::CMN => OrderTaskTypeEnum::CMN,
            DocumentRequestTypeEnum::CHART_NOTES => OrderTaskTypeEnum::CHART_NOTES,
            DocumentRequestTypeEnum::LAB => OrderTaskTypeEnum::LAB,
            DocumentRequestTypeEnum::AOB => OrderTaskTypeEnum::AOB,
            default => OrderTaskTypeEnum::CUSTOM,
        };
    }
}
