<?php

namespace App\Listeners;

use App\Contracts\Notifications\OrderLoggableMailInterface;
use App\Enums\ActivityLogTypeEnum;
use App\Enums\DigitalJourneyLeadStatusEnum;
use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Models\DigitalJourneyLead;
use App\Models\Lead;
use App\Models\Order;
use App\Models\Patient;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Mail\DigitalJourneyEmail;
use App\Notifications\Sms\Sms;
use App\Services\ShuttleHealth\ActivityLogger;
use App\Traits\IdentifiesMessageType;
use Illuminate\Notifications\Events\NotificationSent;
use Illuminate\Support\Carbon;

/**
 * This listener is responsible for storing the sent notifications.
 */
class NotificationSentListener
{
    use IdentifiesMessageType;
    /**
     * Handle the given event.
     */
    public function handle(NotificationSent $event): void
    {
        $notification = $event->notification;
        $senderUserId = null;

        if (method_exists($notification, 'getUserId')) {
            $senderUserId = $notification->getUserId();
        }

        if ($event->channel === SMSChannel::class && $event->notifiable instanceof Patient) {
            $patient = $event->notifiable;

            $order = method_exists($notification, 'getOrder') ? $notification->getOrder() : null;

            if ($order) {
                ActivityLogger::write(
                    $order,
                    ActivityLogTypeEnum::MESSAGE,
                    [
                        'message' => $notification->toSMS($patient),
                        'patient' => [
                            'first_name' => $patient->first_name,
                            'last_name' => $patient->last_name,
                            'mobile' => $patient->mobile,
                        ],
                    ],
                    null,
                    $senderUserId,
                );
            }
        }

        if ($event->channel === SMSChannel::class && $event->notifiable instanceof Lead) {
            $lead = $event->notifiable;

            if (!$senderUserId) {
                $senderUserId = getUser()?->id ?? $lead->created_by;
            }

            ActivityLogger::write(
                $lead,
                ActivityLogTypeEnum::MESSAGE,
                [
                    'message' => $notification->toSMS($lead),
                    'lead' => [
                        'first_name' => $lead->first_name,
                        'last_name' => $lead->last_name,
                        'mobile' => $lead->mobile,
                    ],
                ],
                null,
                $senderUserId,
            );

            // ✅ Update Digital Journey Lead status if this is a digital journey SMS
            if ($this->isDigitalJourneyNotification($notification)) {
                $this->updateDigitalJourneyLeadStatus($lead, $notification, 'message');
            }
        }

        if ($event->notification instanceof DigitalJourneyEmail && $event->notifiable instanceof Lead) {
            $lead = $event->notifiable;
            $template = $event->notification->getTemplate();
            // Always use distributor template name
            $templateName = $template->distributorTemplate ? $template->distributorTemplate->name : null;

            ActivityLogger::write(
                $lead,
                ActivityLogTypeEnum::EMAIL,
                [
                    'template_id' => $template->id,
                    'template_name' => $templateName,
                    'lead' => [
                        'first_name' => $lead->first_name,
                        'last_name' => $lead->last_name,
                        'email' => $lead->email,
                    ],
                ],
                null,
                $senderUserId,
            );

            // ✅ Update Digital Journey Lead status for digital journey emails
            if ($this->isDigitalJourneyNotification($event->notification)) {
                $this->updateDigitalJourneyLeadStatus($lead, $event->notification, 'email');
            }
        }

        if ($event->notification instanceof OrderLoggableMailInterface) {
            /** @var OrderLoggableMailInterface $notification */
            $notification = $event->notification;
            /** @var Order $order */
            $order = $notification->getOrder();
            $user = $event->notifiable;

            ActivityLogger::write(
                $order,
                ActivityLogTypeEnum::USER_EMAIL,
                [
                    'email_type' => $notification->getType(),
                    'user' => [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'email' => $user->email,
                    ],
                ],
                null,
                $senderUserId,
            );
        }
    }

    /**
     * Update Digital Journey Lead status when a digital journey message is sent.
     */
    private function updateDigitalJourneyLeadStatus(Lead $lead, $notification, string $templateType): void
    {
        // Get the digital journey lead ID from the notification
        $digitalJourneyLeadId = null;

        if (method_exists($notification, 'getDigitalJourneyLeadId')) {
            $digitalJourneyLeadId = $notification->getDigitalJourneyLeadId();
        }

        if (!$digitalJourneyLeadId) {
            Logger::warning('No digital journey lead ID found in notification', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'template_type' => $templateType,
                'notification_class' => get_class($notification),
            ]);

            return;
        }

        // Find the specific digital journey lead record by ID
        $digitalJourneyLead = DigitalJourneyLead::where('id', $digitalJourneyLeadId)
            ->where('lead_id', $lead->id)
            ->where('status', DigitalJourneyLeadStatusEnum::PROCESSING)
            ->first();

        if ($digitalJourneyLead) {
            // Update status to SENT and set sent_at timestamp
            $digitalJourneyLead->update([
                'status' => DigitalJourneyLeadStatusEnum::SENT,
                'sent_at' => Carbon::now(),
            ]);

            Logger::error('Digital Journey Lead status updated to SENT', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_lead_id' => $digitalJourneyLead->id,
                'digital_journey_id' => $digitalJourneyLead->digital_journey_id,
                'template_type' => $digitalJourneyLead->digital_journey_template_type,
                'sent_at' => Carbon::now()->toDateTimeString(),
            ]);
        } else {
            Logger::error('Digital Journey Lead record not found or not in PROCESSING status', LogGroupEnum::DEFAULT, [
                'lead_id' => $lead->id,
                'digital_journey_lead_id' => $digitalJourneyLeadId,
                'template_type' => $templateType,
                'notification_class' => get_class($notification),
            ]);
        }
    }

    /**
     * Check if the notification is a digital journey notification.
     */
    private function isDigitalJourneyNotification($notification): bool
    {
        $messageType = $this->getMessageType($notification);

        return in_array($messageType, ['digital_journey_day_0', 'digital_journey_other_days']);
    }
}
