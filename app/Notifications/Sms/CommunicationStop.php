<?php

namespace App\Notifications\Sms;

use App\Enums\LeadStatusEnum;
use App\Enums\MessageConfigurationConditionEnums;
use App\Enums\MessageConfigurationScenarioEnums;
use App\Helpers\DistributorMessageHelper;
use App\Models\Lead;
use App\Models\Order;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class CommunicationStop extends Notification implements ShouldQueue
{
    use FromNumber;
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected ?string $fromNumber = null)
    {
    }

    public function getAuditContext(): array
    {
        return [
            'scenario' => MessageConfigurationScenarioEnums::DEFAULT->value,
            'condition' => MessageConfigurationConditionEnums::OPT_OUT->value,
        ];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param object $notifiable
     *
     * @return string
     */
    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSMS(object $notifiable): string
    {
        if ($notifiable instanceof Lead) {
            return $this->getLeadOptOutMessage($notifiable);
        }

        if ($notifiable instanceof Order) {
            return $this->getOrderOptOutMessage($notifiable);
        }

        // Default: treat as patient/global
        return $this->getGlobalOptOutMessage($notifiable);
    }

    /**
     * Handle opt-out message for a Lead.
     */
    private function getLeadOptOutMessage(Lead $lead): string
    {
        $patient = $lead->patient;
        $distributorId = $lead->distributorCampaign->distributor_id ?? null;
        $activeOrders = $patient?->orders()->where('distributor_id', $distributorId)->isActive()->exists();
        $isLeadActive = $lead->status === LeadStatusEnum::OPEN->value;
        $hasCampaignOptOut = !empty($lead->distributorCampaign->opt_out_text);

        // Both Active Order + Active Lead: send global opt-out
        if ($isLeadActive && $activeOrders) {
            return $this->getGlobalOptOutMessage($lead, $distributorId);
        }

        // Closed Order + Active Lead: campaign-specific if available, else global
        if ($isLeadActive && !$activeOrders) {
            if ($hasCampaignOptOut) {
                return $lead->distributorCampaign->opt_out_text;
            }

            return $this->getGlobalOptOutMessage($lead, $distributorId);
        }

        // Inactive Lead: fallback to global
        if (!$isLeadActive) {
            return $this->getGlobalOptOutMessage($lead, $distributorId);
        }

        // Default fallback
        return $this->getGlobalOptOutMessage($lead, $distributorId);
    }

    /**
     * Handle opt-out message for an Order.
     */
    private function getOrderOptOutMessage(Order $order): string
    {
        // Order-specific opt-out
        $fallback = __('communications.patient.stop', [
            'organizationName' => $order->organization?->name ?? 'Shuttle Health',
        ]);
        $msg = DistributorMessageHelper::getDistributorMessage(
            $order->organization?->id,
            MessageConfigurationScenarioEnums::ORDER->value,
            MessageConfigurationConditionEnums::OPT_OUT->value,
            $fallback,
        );

        return DistributorMessageHelper::replacePlaceholdersInOptInOptOut($order, $msg, $order->organization);
    }

    /**
     * Handle global opt-out message (for patient or fallback).
     */
    private function getGlobalOptOutMessage($notifiable): string
    {
        $fallback = __('communications.patient.stop', [
            'organizationName' => $notifiable->organization?->name ?? 'Shuttle Health',
        ]);
        $msg = DistributorMessageHelper::getDistributorMessage(
            $notifiable->organization?->id,
            MessageConfigurationScenarioEnums::DEFAULT->value,
            MessageConfigurationConditionEnums::OPT_OUT->value,
            $fallback,
        );

        return DistributorMessageHelper::replacePlaceholdersInOptInOptOut($notifiable, $msg, $notifiable?->organization);
    }
}
