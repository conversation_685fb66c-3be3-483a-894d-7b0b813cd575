<?php

namespace App\Notifications\Sms;

use App\Models\Lead;
use App\Models\Patient;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use App\Traits\HasScheduledPatientCommunication;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class Sms extends Notification implements ShouldQueue
{
    use FromNumber;
    use HasScheduledPatientCommunication;
    use Queueable;

    public ?Patient $patient = null;
    public ?Lead $lead = null;
    protected bool $forceRealTime = false;
    protected ?int $digitalJourneyLeadId = null;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected string $message, protected ?string $fromNumber = null, protected ?int $userId = null, Patient|Lead $patientOrLead = null, bool $forceRealTime = false, ?int $digitalJourneyLeadId = null)
    {
        if ($patientOrLead instanceof Patient) {
            $this->patient = $patientOrLead;
        }

        if ($patientOrLead instanceof Lead) {
            $this->lead = $patientOrLead;
        }

        $this->forceRealTime = $forceRealTime;
        $this->digitalJourneyLeadId = $digitalJourneyLeadId;

        // Use patient communication queue for time window scheduling
        // Only if we have a patient/lead AND we're not forcing real-time
        if (($this->patient || $this->lead) && !$this->forceRealTime) {
            $this->onScheduledPatientCommunicationConnection();
        }
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSMS(object $notifiable): string
    {
        return $this->message;
    }

    /**
     * Get the id of user that initiated notification.
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Check if this notification is forced to be real-time.
     */
    public function isForceRealTime(): bool
    {
        return $this->forceRealTime;
    }

    /**
     * Get the digital journey lead ID associated with this notification.
     */
    public function getDigitalJourneyLeadId(): ?int
    {
        return $this->digitalJourneyLeadId;
    }
}
