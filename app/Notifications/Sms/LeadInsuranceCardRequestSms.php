<?php

namespace App\Notifications\Sms;

use App\Models\LeadInsuranceCardRequest;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use App\Services\LeadInsuranceCardRequestTokenService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class LeadInsuranceCardRequestSms extends Notification
{
    use FromNumber;
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected LeadInsuranceCardRequest $request,
        protected ?string $fromNumber = null,
        protected ?int $userId = null,
    ) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return string
     */
    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSMS(object $notifiable): string
    {
        $tokenService = app(LeadInsuranceCardRequestTokenService::class);
        $accessUrl = $tokenService->generateLeadAccessUrl($this->request->request_token);

        $leadName = $notifiable->first_name;
        $expirationDate = $this->request->expiration_date->format('M j, Y g:i A');

        return "Hi {$leadName}, please upload your insurance card images (front and back) using this secure link: {$accessUrl}. This link expires on {$expirationDate}. Reply HELP for assistance.";
    }

    /**
     * Get the id of user that initiated notification.
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Get the request for logging purposes.
     */
    public function getRequest(): LeadInsuranceCardRequest
    {
        return $this->request;
    }
}
