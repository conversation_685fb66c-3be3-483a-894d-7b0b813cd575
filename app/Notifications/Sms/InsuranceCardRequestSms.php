<?php

namespace App\Notifications\Sms;

use App\Models\Patient;
use App\Models\PatientInsuranceCardRequest;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use App\Services\PatientInsuranceCardRequestTokenService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class InsuranceCardRequestSms extends Notification
{
    use FromNumber;
    use Queueable;

    public Patient $patient;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected PatientInsuranceCardRequest $request,
        protected ?string $fromNumber = null,
        protected ?int $userId = null,
    ) {
        // Set Patient to adjust according to his timezone.
        $this->patient = $this->request->patient;
        // Insurance Card Requests should be sent real-time
        // They are user-initiated actions that need immediate response
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return string
     */
    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSMS(object $notifiable): string
    {
        $tokenService = app(PatientInsuranceCardRequestTokenService::class);
        $accessUrl = $tokenService->generatePatientAccessUrl($this->request->request_token);

        $patientName = $notifiable->first_name;
        $expirationDate = $this->request->expiration_date->format('M j, Y g:i A');

        return "Hi {$patientName}, please upload your insurance card images (front and back) using this secure link: {$accessUrl}. This link expires on {$expirationDate}. Reply HELP for assistance.";
    }

    /**
     * Get the id of user that initiated notification.
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Get the request for logging purposes.
     */
    public function getRequest(): PatientInsuranceCardRequest
    {
        return $this->request;
    }
}
