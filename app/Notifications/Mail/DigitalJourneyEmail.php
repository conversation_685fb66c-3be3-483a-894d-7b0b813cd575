<?php

namespace App\Notifications\Mail;

use App\Models\EmailTemplate;
use App\Services\ShuttleHealth\HtmlPurifierService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class DigitalJourneyEmail extends Notification implements ShouldQueue
{
    use Queueable;

    protected ?int $digitalJourneyLeadId = null;

    /**
     * Create a new notification instance.
     */
    public function __construct(private readonly EmailTemplate $template, ?int $digitalJourneyLeadId = null)
    {
        $this->digitalJourneyLeadId = $digitalJourneyLeadId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $distributor = $this->template->distributorCampaign->distributor;

        // Get the related distributor template
        $distributorTemplate = $this->template->distributorTemplate;
        $htmlContent = $distributorTemplate?->html_content ?? '';

        // Prepare dynamic params
        $params = [
            '{{patient_first_name}}' => $notifiable->first_name ?? '',
            '{{patient_last_name}}' => $notifiable->last_name ?? '',
            '{{campaign_name}}' => $this->template->distributorCampaign->name ?? '',
            '{{distributor_name}}' => $distributor->name ?? '',
            '{{distributor_website}}' => $distributor->organizationSettings()->where('name', 'website_url')->get()->value('value') ?? '',
            '{{distributor_phone_number}}' => $distributor->phone ?? '',
            '{{unsubscribe_link}}' => '<a href="' . generateUnsubscribeUrl($notifiable) . '">Unsubscribe</a>',
        ];

        // Replace params in html_content
        $htmlContent = strtr($htmlContent, $params);

        $mailMessage = (new MailMessage)
            ->from(config('mail.from.address'), $distributor->name)
            ->subject($distributorTemplate?->name ?? $this->template->name)
            ->view(
                'emails.distributor-digital-journey-template',
                [
                    'content' => $this->toHtml($htmlContent),
                ],
            );

        return $mailMessage;
    }

    public function getTemplate(): EmailTemplate
    {
        return $this->template;
    }

    /**
     * Get the digital journey lead ID associated with this notification.
     */
    public function getDigitalJourneyLeadId(): ?int
    {
        return $this->digitalJourneyLeadId;
    }

    private function toHtml(string $html): HtmlString
    {
        $html = app(HtmlPurifierService::class)->purify($html);

        return new HtmlString($html);
    }
}
