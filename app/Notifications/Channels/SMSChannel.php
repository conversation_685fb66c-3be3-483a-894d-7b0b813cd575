<?php

namespace App\Notifications\Channels;

use App\Actions\ShuttleHealth\ResolveNameByPhoneNumberAction;
use App\Contracts\CommunicationServiceInterface;
use App\Enums\LogGroupEnum;
use App\Enums\SignalWireSmsStatusEnums;
use App\Enums\SmsDirectionEnum;
use App\Extensions\Logger;
use App\Jobs\SignalWire\Logs\SaveSignalWireSmsLogJob;
use App\Models\Audit;
use App\Models\Distributor;
use App\Models\Lead;
use App\Models\Manufacturer;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\User;
use App\Services\ShuttleHealth\OrganizationContextService;
use Illuminate\Notifications\Notification;

readonly class SMSChannel
{
    public function __construct(
        private CommunicationServiceInterface $communicationService,
        protected OrganizationContextService $organizationContextService,
    ) {
    }

    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        $message = $notification->toSMS($notifiable);

        if (empty($message)) {
            Logger::info('SMSChannel: Attempted to send empty SMS message', LogGroupEnum::SIGNAL_WIRE, [
                'notifiable_id' => $notifiable->id ?? null,
                'notification' => get_class($notification),
            ]);

            return;
        }

        $fromNumber = null;

        if (method_exists($notification, 'getFromNumber')) {
            $fromNumber = $notification->getFromNumber($notifiable);
        }


        // Audit log for all SMS sent
        $auditData = [
            'message' => $message,
            'from_number' => $fromNumber,
            'to_number' => $notifiable->mobile ?? null,
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->id,
        ];

        if (method_exists($notification, 'getAuditContext')) {
            $auditData = array_merge($auditData, $notification->getAuditContext());
        }


        Audit::create([
            'user_type' => User::class,
            'user_id' => getUser()?->id,
            'event' => 'sms_sent',
            'auditable_type' => get_class($notifiable),
            'auditable_id' => $notifiable->id,
            'old_values' => [],
            'new_values' => $auditData,
            'url' => request()->fullUrl(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        $signalWireApiResponse = $this->communicationService->sendText($notifiable->mobile, $message, $fromNumber);

        $sid = $signalWireApiResponse?->sid ?? $signalWireApiResponse['MessageSid'] ?? 'Sample-SID';
        $status = $signalWireApiResponse->status ?? SignalWireSmsStatusEnums::QUEUED->value;
        $nameResolver = app(ResolveNameByPhoneNumberAction::class);

        $logData = [
            'sid' => $sid,
            'status' => $status,
            'direction' => SmsDirectionEnum::OUTBOUND->value,
            'from' => $fromNumber ?? '1234567890',
            'to' => $notifiable->mobile ?? $notifiable->phone ?? $notifiable->phone_cell ?? '1234567890',
            'sender_name' => $nameResolver->execute($fromNumber),
            'receiver_name' => $nameResolver->execute($notifiable->mobile ?? null),
            'body' => $message ?? 'Sample message body',
            'entity_type' => get_class($notifiable),
            'entity_id' => $notifiable->id,
        ];

        $currentOrganization = $this->getOrganizationFromNotifiable($notifiable);

        if ($currentOrganization) {
            SaveSignalWireSmsLogJob::dispatch($currentOrganization, $sid ?? 'Sample-SID', $logData);
        } else {
            Logger::warning('Could not determine organization for SMS log', LogGroupEnum::SIGNAL_WIRE, [
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? null,
                'sid' => $sid,
            ]);
        }
    }

    /**
     * Extract organization from the notifiable object
     */
    private function getOrganizationFromNotifiable(object $notifiable): Distributor|Manufacturer|Provider|null
    {
        // If notifiable is already an organization
        if ($notifiable instanceof Distributor || $notifiable instanceof Manufacturer || $notifiable instanceof Provider) {
            return $notifiable;
        }

        // Handle Lead - get from distributorCampaign relationship
        if ($notifiable instanceof Lead) {
            Logger::warning('Lead Data From SMS Channel getting organization from Notifiable', LogGroupEnum::SIGNAL_WIRE, [
                'lead' => $notifiable,
                'distributorCampaign' => $notifiable?->distributorCampaign,
                'distributor' => $notifiable?->distributorCampaign?->distributor,
            ]);

            return $notifiable->distributorCampaign?->distributor ?? null;
        }

        // Handle Patient - get from organization relationship
        if ($notifiable instanceof Patient) {
            return $notifiable->organization ?? null;
        }

        // Handle other models that might have direct organization relationship
        if (method_exists($notifiable, 'organization') && $notifiable->organization) {
            $org = $notifiable->organization;

            if ($org instanceof Distributor || $org instanceof Manufacturer || $org instanceof Provider) {
                return $org;
            }
        }

        // Handle models that might have distributor relationship
        if (method_exists($notifiable, 'distributor') && $notifiable->distributor instanceof Distributor) {
            return $notifiable->distributor;
        }

        // Handle models that might have manufacturer relationship
        if (method_exists($notifiable, 'manufacturer') && $notifiable->manufacturer instanceof Manufacturer) {
            return $notifiable->manufacturer;
        }

        // Handle models that might have provider relationship
        if (method_exists($notifiable, 'provider') && $notifiable->provider instanceof Provider) {
            return $notifiable->provider;
        }

        return null;
    }

}
