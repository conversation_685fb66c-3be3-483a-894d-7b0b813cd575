<?php

namespace App\Actions\ShuttleHealth;

use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Exceptions\DocumentRequestCreationException;
use App\Models\Distributor;
use App\Models\DocumentRequest;
use App\Models\Facility;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\ProviderUser;
use App\Models\User;
use App\Services\ShuttleHealth\OrganizationContextService;

/**
 * Class CreateDocumentRequest
 *
 * The action is responsible for creating a document request.
 */
class CreateDocumentRequest extends BaseDatabaseAction
{
    public function __construct(
        private readonly OrganizationContextService $contextService,
        private readonly FindOrCreateFacilityForProviderUser $findOrCreateFacilityForProviderUser,
    ) {
        //
    }

    public function execute(
        Distributor $distributor,
        User $user,
        DocumentRequestRequestTypeEnum $requestType,
        array $documentRequestData,
        ?ProviderUser $providerUser = null,
        ?array $products = null,
    ) {
        $callback = function () use (
            $distributor,
            $providerUser,
            $user,
            $requestType,
            $documentRequestData,
            $products,
        ) {
            if (!$providerUser && $documentRequestData['type'] !== DocumentRequestTypeEnum::AOB->value) {
                throw new DocumentRequestCreationException('Provider user is required to create not AOB document request.');
            }

            $patient = $this->getPatientByIdAndVerifyOrganization($documentRequestData['patient_id']);
            $provider = null;
            $facility = null;

            if ($documentRequestData['type'] !== DocumentRequestTypeEnum::AOB->value) {
                $provider = $this->getAndLinkProvider($documentRequestData['provider_id'] ?? null, $distributor);
                $this->linkPatientToProvider($patient, $provider);

                $this->verifyAndLinkProviderUser($providerUser, $distributor, $provider);

                if (!$provider && !empty($documentRequestData['facility_id'])) {
                    $facility = $this->verifyAndGetFacility($documentRequestData['facility_id'], $providerUser, $distributor);
                } elseif (!$provider && empty($documentRequestData['facility_id'])) {
                    $facility = $this->findOrCreateFacilityForProviderUser->execute($providerUser, $distributor->id);
                }
            }

            $documentRequestData['global_patient_id'] = $patient->global_patient_id;
            $documentRequestData['provider_user_id'] = $providerUser?->id;
            $documentRequestData['provider_id'] = $provider?->id;
            $documentRequestData['facility_id'] = $facility?->id;
            $documentRequestData['created_by'] = $user->id;
            $documentRequestData['request_type'] = $requestType;
            $documentRequestData['status'] = $this->getStatus($requestType, $documentRequestData);

            // assign creator to the request by default
            if (empty($documentRequestData['distributor_user_id']) && $user->isDistributorUser()) {
                $documentRequestData['distributor_user_id'] = $user->id;
            }

            /** @var DocumentRequest $documentRequest */
            $documentRequest = $distributor->documentRequests()->create($documentRequestData);

            if ($documentRequestData['type'] === DocumentRequestTypeEnum::CMN->value && !empty($products)) {
                foreach ($products as $product) {
                    $documentRequest->products()->attach($product['id'], [
                        'measure_count' => (int) ($product['measure_count'] ?? 0),
                        'measure_unit' => $product['measure_unit'] ?? null,
                        'duration_count' => (int) ($product['duration_count'] ?? 0),
                        'duration_unit' => $product['duration_unit'] ?? null,
                        'narrative_unit' => $product['narrative_unit'] ?? null,
                        'narrative_measure_unit' => $product['narrative_measure_unit'] ?? null,
                        'narrative_measure_count' => (int) ($product['narrative_measure_count'] ?? 0),
                        'serial_number' => $product['serial_number'] ?? null,
                    ]);
                }
            }

            return $documentRequest;
        };

        return $this->dbTransactionCallback($callback);
    }

    private function linkPatientToProvider(Patient $patient, ?Provider $provider): void
    {
        if ($provider) {
            $patient->linkToOrganization($provider);
        }
    }

    private function getPatientByIdAndVerifyOrganization(int $patientId): Patient
    {
        $patient = $this->contextService->getOrganization()->patients()->find($patientId);

        if (!$patient) {
            throw new DocumentRequestCreationException('Patient does not belong to organization.');
        }

        return $patient;
    }

    private function verifyAndLinkProviderUser(
        ?ProviderUser $providerUser,
        Distributor $distributor,
        ?Provider $provider,
    ): void {
        if (!$providerUser) {
            return;
        }

        if ($provider && $provider->users()->where('users.id', $providerUser->id)->doesntExist()) {
            throw new DocumentRequestCreationException('ProviderUser does not belong to provider.');
        }

        $distributor->providerUsers()->syncWithoutDetaching($providerUser);
    }

    private function verifyAndGetFacility(
        ?int $facilityId,
        ProviderUser $providerUser,
        Distributor $distributor,
    ): Facility {
        $facility = $distributor->facilities()
            ->where('facilities.id', $facilityId)
            ->where('facilities.user_id', $providerUser->id)
            ->first();

        if (!$facility) {
            throw new DocumentRequestCreationException('ProviderUser facility is not found for distributor.');
        }

        return $facility;
    }

    private function getAndLinkProvider(
        ?int $providerId,
        Distributor $distributor,
    ): ?Provider {
        if (!$providerId) {
            return null;
        }

        $provider = Provider::find($providerId);

        if (!$provider) {
            throw new DocumentRequestCreationException('Provider is not found.');
        }

        $distributor->providers()->syncWithoutDetaching($provider);

        return $provider;
    }

    private function getStatus(
        DocumentRequestRequestTypeEnum $requestType,
        array $documentRequestData,
    ): DocumentRequestStatusEnum {
        if ($requestType === DocumentRequestRequestTypeEnum::PRESCRIPTION_RENEWAL) {
            return DocumentRequestStatusEnum::SCHEDULED;
        }

        return $documentRequestData['is_digital']
            ? DocumentRequestStatusEnum::PENDING_DIGITAL
            : DocumentRequestStatusEnum::PENDING_ANALOG;
    }
}
