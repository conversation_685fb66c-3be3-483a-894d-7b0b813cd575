<?php

namespace App\Actions\ShuttleHealth;

use App\Models\Facility;
use App\Models\ProviderUser;
use App\Utils\PhoneNumberConverter;
use App\Utils\ZipCodeConverter;
use Exception;

/**
 * Class FindOrCreateFacilityForProviderUser
 *
 * The action is responsible for finding or creating a facility for a provider user.
 * Location address from NPI record is used to find or create a facility.
 */
class FindOrCreateFacilityForProviderUser extends BaseDatabaseAction
{
    /**
     * @throws Exception
     */
    public function execute(ProviderUser $providerUser, int $distributorId): Facility
    {
        $npiRecord = $providerUser->npiRecord;
        throw_if(!$npiRecord, new Exception('Can not find or create facility: provider user does not have NPI record'));

        $address = $npiRecord->getAddress();
        throw_if(!$address, new Exception('Can not find or create facility: provider user does not have location address in NPI record'));

        $facilityQuery = $providerUser->facilities()
            ->where('distributor_id', $distributorId)
            ->where('zip', ZipCodeConverter::fromNumeric($address['postal_code']));

        if (!empty($address['fax_number'])) {
            $facilityQuery->where('fax', PhoneNumberConverter::cleanPhoneNumber($address['fax_number']));
        } elseif (!empty($address['telephone_number'])) {
            $facilityQuery->where('phone', PhoneNumberConverter::cleanPhoneNumber($address['telephone_number']));
        }

        /** @var Facility $facility */
        $facility = $facilityQuery->first();

        if ($facility) {
            return $facility;
        }

        return Facility::create([
            'distributor_id' => $distributorId,
            'user_id' => $providerUser->id,
            'name' => '',
            'phone' => PhoneNumberConverter::cleanPhoneNumber($address['telephone_number']),
            'fax' => PhoneNumberConverter::cleanPhoneNumber($address['fax_number'] ?? null),
            'address_line_1' => $address['address_1'],
            'city' => $address['city'],
            'state' => $address['state'],
            'zip' => ZipCodeConverter::fromNumeric($address['postal_code']),
        ]);
    }
}
