<?php

namespace App\Actions\ShuttleHealth;

use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Models\DocumentRequest;
use App\Models\User;
use Exception;
use Illuminate\Support\Carbon;

/**
 * Class SendDigitalRequest
 *
 * This class is responsible for sending digital request.
 */
class SendDigitalRequest extends BaseDatabaseAction
{
    public function execute(
        DocumentRequest $documentRequest,
        User $user,
        ?string $label = null,
        ?array $details = null,
    ): void {
        if ($documentRequest->type !== DocumentRequestTypeEnum::AOB && !$documentRequest->provider_id) {
            throw new Exception('Provider must be set to send digital document request.');
        }

        $this->dbTransactionCallback(function () use ($documentRequest, $user, $label, $details) {
            $requestData = [
                'status' => DocumentRequestStatusEnum::PENDING_DIGITAL,
                'last_requested_at' => now(),
            ];
            $historyData = [
                'type' => DocumentRequestHistoryTypeEnum::REQUESTED_DIGITAL,
                'user_id' => $user->id,
                'activity_at' => Carbon::now(),
            ];

            if (!empty($details)) {
                $requestData['details'] = $details;
                $historyData['details'] = $details;
            }

            if (!empty($label)) {
                $requestData['label'] = $label;
            }

            $documentRequest->update($requestData);

            $documentRequest->history()->create($historyData);
        });
    }
}
