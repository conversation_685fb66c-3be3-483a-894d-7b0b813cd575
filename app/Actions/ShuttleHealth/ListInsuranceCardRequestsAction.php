<?php

namespace App\Actions\ShuttleHealth;

use App\Models\PatientInsuranceCardRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class ListInsuranceCardRequestsAction extends BaseDatabaseAction
{
    public function execute(int $patientId, array $queryParams): LengthAwarePaginator
    {
        $query = PatientInsuranceCardRequest::query()
            ->where('patient_id', $patientId)
            ->with([
                'patient.organization.imageFile',
                'frontImageFile',
                'backImageFile',
                'createdBy',
                'updatedBy',
            ])
            ->withTrashed(); // Include soft deleted records as per requirements

        // Apply filters using the PatientInsuranceCardRequestFilter
        $query = $this->applyFilters($query, $queryParams);

        // Default ordering by created_at desc
        $query->orderBy('created_at', 'desc');

        // Return paginated results
        return $query->paginate(
            perPage: $queryParams['per_page'] ?? 25,
            page: $queryParams['page'] ?? 1,
        );
    }

    public function executeForAll(array $queryParams): LengthAwarePaginator
    {
        $query = PatientInsuranceCardRequest::query()
            ->with(['patient', 'frontImageFile', 'backImageFile', 'createdBy', 'updatedBy'])
            ->withTrashed(); // Include soft deleted records as per requirements

        // Apply filters using the PatientInsuranceCardRequestFilter
        $query = $this->applyFilters($query, $queryParams);

        // Default ordering by created_at desc
        $query->orderBy('created_at', 'desc');

        // Return paginated results
        return $query->paginate(
            perPage: $queryParams['per_page'] ?? 25,
            page: $queryParams['page'] ?? 1,
        );
    }

    private function applyFilters(Builder $query, array $queryParams): Builder
    {
        // Status filter
        if (!empty($queryParams['status'])) {
            $status = $queryParams['status'];

            if ($status !== 'all') {
                $query->where('status', $status);
            }
        }

        // Date range filters
        if (!empty($queryParams['created_from'])) {
            $query->whereDate('created_at', '>=', $queryParams['created_from']);
        }

        if (!empty($queryParams['created_to'])) {
            $query->whereDate('created_at', '<=', $queryParams['created_to']);
        }

        // Patient ID filter (for admin views)
        if (!empty($queryParams['patient_id'])) {
            $query->where('patient_id', $queryParams['patient_id']);
        }

        return $query;
    }
}
