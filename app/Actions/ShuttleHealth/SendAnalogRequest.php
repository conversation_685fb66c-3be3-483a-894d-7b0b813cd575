<?php

namespace App\Actions\ShuttleHealth;

use App\Contracts\CommunicationServiceInterface;
use App\Contracts\FileStorageServiceInterface;
use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Models\DocumentRequest;
use App\Models\Fax;
use App\Models\User;
use Illuminate\Support\Carbon;

/**
 * Class SendAnalogRequest
 *
 * This class is responsible for sending analog request.
 */
class SendAnalogRequest extends BaseDatabaseAction
{
    public function __construct(
        protected readonly CommunicationServiceInterface $communicationService,
        protected readonly FileStorageServiceInterface $fileStorageService,
        protected readonly MakeCmnPDF $makeCmnPDF,
        protected readonly MakeDocumentRequestPDF $makeDocumentRequestPDF,
    ) {
        //
    }

    public function execute(
        DocumentRequest $documentRequest,
        User $user,
        int $faxNumber,
        ?string $label = null,
        ?array $details = null,
    ): void {
        $this->dbTransactionCallback(function () use ($documentRequest, $user, $faxNumber, $label, $details) {
            $requestData = [
                'status' => DocumentRequestStatusEnum::PENDING_ANALOG,
                'last_requested_at' => now(),
            ];
            $historyData = [
                'type' => DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG,
                'user_id' => $user->id,
                'activity_at' => Carbon::now(),
            ];

            if (!empty($details)) {
                $requestData['details'] = $details;
                $historyData['details'] = $details;
            }

            if (!empty($label)) {
                $requestData['label'] = $label;
            }

            $documentRequest->update($requestData);
            $requestFax = Fax::createFaxWithFile($documentRequest->distributor, $faxNumber);

            $historyData['fax_id'] = $requestFax->id;
            $documentRequest->history()->create($historyData);
            $icd10Codes = $documentRequest->order?->diagnosisCodes;

            if ($documentRequest->type === DocumentRequestTypeEnum::CMN) {
                $pdf = $this->makeCmnPDF->execute(
                    distributor: $documentRequest->distributor,
                    providerUser: $documentRequest->providerUser,
                    patient: $documentRequest->patient,
                    products: $documentRequest->getProducts(),
                    provider: $documentRequest->provider,
                    facility: $documentRequest->facility,
                    documentRequestGlobalId: $documentRequest->getGlobalId(),
                    icd10Codes: $icd10Codes,
                );
            } else {
                $pdf = $this->makeDocumentRequestPDF->execute(
                    distributor: $documentRequest->distributor,
                    providerUser: $documentRequest->providerUser,
                    patient: $documentRequest->patient,
                    documentRequests: [$documentRequest],
                    provider: $documentRequest->provider,
                    facility: $documentRequest->facility,
                );
            }

            $this->fileStorageService->uploadFax($requestFax->file, $pdf->output());
            $this->communicationService->sendFax($requestFax);
        });
    }
}
