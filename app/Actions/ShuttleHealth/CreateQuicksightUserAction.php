<?php

namespace App\Actions\ShuttleHealth;

use App\Contracts\QuickSightClientInterface;
use App\Enums\QuicksightEnum;
use App\Models\Distributor;
use App\Models\QuicksightUser;

class CreateQuicksightUserAction
{
    public function __construct(
        private QuickSightClientInterface $quickSightClient,
    ) {
    }

    public function execute(
        Distributor $distributor,
    ): array {
        $authenticatedUser = getUser();

        // Check if a QuickSight user already exists for this distributor and authenticated user
        $existingUser = QuicksightUser::where('distributor_id', $distributor->id)
            ->where('user_id', $authenticatedUser?->id)
            ->exists();

        if ($existingUser) {
            throw new \Exception('QuickSight user already exists for this distributor and user');
        }

        // Prepare parameters for AWS QuickSight user creation
        $awsParameters = [
            'AwsAccountId' => $this->quickSightClient->getAwsAccountId(),
            'Namespace' => QuicksightEnum::QUICK_SIGHT_NAMESPACE->value,
            'IdentityType' => QuicksightEnum::QUICK_SIGHT_IDENTITY_TYPE->value,
            'UserName' => $authenticatedUser->getQuickSightUserName(),
            'Email' => $authenticatedUser->email,
            'UserRole' => QuicksightEnum::READER->value,
            'Tags' => $this->createTags($distributor),
        ];

        // Create a user in AWS QuickSight
        $awsResult = $this->quickSightClient->registerUser($awsParameters);

        // Extract user ARN from AWS response
        $userArn = $awsResult['User']['Arn'] ?? null;

        if (!$userArn) {
            throw new \Exception('Failed to get user ARN from AWS QuickSight response');
        }

        // Create a user in a local database
        $quicksightUser = QuicksightUser::create([
            'distributor_id' => $distributor->id,
            'user_id' => $authenticatedUser?->id,
            'arn' => $userArn,
            'username' => $authenticatedUser->getQuickSightUserName(),
            'role' => QuicksightEnum::READER->value,
            'metadata' => [
                'email' => $authenticatedUser->email,
                'namespace' => QuicksightEnum::QUICK_SIGHT_NAMESPACE->value,
                'aws_user_id' => $awsResult['User']['UserId'] ?? null,
                'created_via_api' => true,
            ],
            'is_active' => true,
        ]);

        return [
            'quicksight_user' => $quicksightUser->only(['id', 'distributor_id', 'user_id', 'username', 'role', 'is_active']),
            'aws_response' => $awsResult,
        ];
    }

    private function createTags(Distributor $distributor): array
    {
        return [
            [
                'Key' => 'distributor_id',
                'Value' => (string) $distributor->id,
            ],
            [
                'Key' => 'role_id',
                'Value' => 'reader',
            ],
        ];
    }
}
