<?php

namespace App\Actions\ShuttleHealth;

use App\Contracts\QuickSightClientInterface;
use App\Enums\QuicksightEnum;
use App\Models\Distributor;
use App\Models\User;
use App\Repositories\QuicksightEmbedLogRepository;
use App\Repositories\QuicksightUserRepository;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

/**
 * Class GenerateQuicksightEmbedUrlAction
 *
 * This action is responsible for generating secure QuickSight embed URLs
 * with proper session tags for Row-Level Security and audit logging.
 *
 * Note: Authorization should be handled at the controller level using policies
 * before calling this action. This action assumes the user is already authorized.
 */
class GenerateQuicksightEmbedUrlAction
{
    public function __construct(
        private readonly QuicksightUserRepository $quicksightUserRepository,
        private readonly QuicksightEmbedLogRepository $quicksightEmbedLogRepository,
        private readonly QuickSightClientInterface $quickSightClient,
    ) {
    }

    public function execute(
        Distributor $distributor,
        string $dashboardId,
        User $user,
        string $ipAddress,
    ): array {
        $result = null;

        if (!$result) {

            $quicksightUser = $this->quicksightUserRepository->findReaderByDistributor($distributor->id, $user->id);

            if (!$quicksightUser) {
                throw ValidationException::withMessages([
                    'quicksight_user' => ['No QuickSight user found for given distributor.'],
                ]);
            }

            $sessionTags = $this->buildSessionTags($distributor->id, $user);
            $awsResult = $this->quickSightClient->getDashboardEmbedUrlForRegisteredUser([
                'AwsAccountId' => $this->quickSightClient->getAwsAccountId(),
                'UserArn' => $quicksightUser->arn,
                'ExperienceConfiguration' => [
                    'Dashboard' => [
                        'InitialDashboardId' => $dashboardId,
                    ],
                ],
                'SessionLifetimeInMinutes' => (int) QuicksightEnum::QUICK_SIGHT_SESSION_LIFETIME_IN_MINUTES->value,
                'Namespace' => QuicksightEnum::QUICK_SIGHT_NAMESPACE->value,
            ]);

            $embedUrl = $awsResult['EmbedUrl'];
            $expiresAt = Carbon::now()->addMinutes((int) QuicksightEnum::QUICK_SIGHT_SESSION_LIFETIME_IN_MINUTES->value);

            $result = [
                'embed_url' => $embedUrl,
                'expires_at' => $expiresAt->toIso8601String(),
            ];

            // Log embed URL generation
            $this->quicksightEmbedLogRepository->logEmbedUrl(
                userId: $user->id ?? null,
                distributorId: $distributor->id,
                dashboardId: $dashboardId,
                embedUrl: $embedUrl,
                ipAddress: $ipAddress,
                sessionTags: $sessionTags,
                expiresAt: $expiresAt,
            );
        }

        return $result;
    }

    private function buildSessionTags(int $distributorId, User $user): array
    {
        return [
            [
                'Key' => 'distributor_id',
                'Value' => (string) $distributorId,
            ],
            [
                'Key' => 'role_id',
                'Value' => 'reader',
            ],
        ];
    }
}
