<?php

namespace App\Actions\ShuttleHealth;

use App\Enums\LogGroupEnum;
use App\Enums\PatientInsuranceCardRequestStatusEnum;
use App\Extensions\Logger;
use App\Models\Patient;
use App\Models\PatientInsuranceCardRequest;
use App\Models\PatientInsuranceCardRequestHistory;
use App\Notifications\Sms\InsuranceCardRequestSms;
use App\Services\PatientInsuranceCardRequestTokenService;
use Illuminate\Support\Facades\Auth;

class CreateInsuranceCardRequestAction extends BaseDatabaseAction
{
    public function __construct(
        protected PatientInsuranceCardRequestTokenService $tokenService,
    ) {
    }

    public function execute(int $patientId, ?string $notes = null, ?int $expirationDays = null, bool $sendSms = true): PatientInsuranceCardRequest
    {
        return $this->dbTransactionCallback(function () use ($patientId, $notes, $expirationDays, $sendSms) {
            $userId = Auth::id();
            $patient = Patient::findOrFail($patientId);

            // Generate secure token and expiration
            $token = $this->tokenService->generateSecureToken();
            $expirationDate = $expirationDays
                ? now()->addDays($expirationDays)
                : $this->tokenService->getDefaultExpirationTime();

            // Create the insurance card request
            $request = PatientInsuranceCardRequest::create([
                'patient_id' => $patient->id,
                'request_token' => $token,
                'status' => PatientInsuranceCardRequestStatusEnum::PENDING,
                'notes' => $notes,
                'expiration_date' => $expirationDate,
                'created_by' => $userId,
                'updated_by' => $userId,
            ]);

            // Create initial history entry
            PatientInsuranceCardRequestHistory::create([
                'patient_insurance_card_request_id' => $request->id,
                'status' => PatientInsuranceCardRequestStatusEnum::PENDING,
                'expiration_date' => $expirationDate,
                'created_by' => $userId,
                'created_at' => now(),
            ]);

            // Send SMS notification to patient only if requested
            if ($sendSms) {
                $distributor = $patient->organization;
                $fromNumber = $distributor?->assigned_fax;
                // Temporarily set mobile to preferred phone number for SMS
                $originalMobile = $patient->mobile;
                $patient->mobile = $patient->getPreferredPhoneNumber();

                Logger::info('Sending insurance card request SMS', LogGroupEnum::DEFAULT, [
                    'patient_id' => $patient->id,
                    'request_id' => $request->id,
                    'request_token' => substr($token, 0, 8) . '...', // Log partial token for security
                    'distributor_id' => $distributor?->id,
                    'distributor_name' => $distributor?->name,
                    'expiration_days' => $expirationDays ?? 'default',
                    'real_time' => true,
                    'forced_sync' => true,
                ]);

                $patient->notify(new InsuranceCardRequestSms($request, $fromNumber, $userId), true);

                // Restore original mobile value
                $patient->mobile = $originalMobile;
            }

            return $request;
        });
    }
}
