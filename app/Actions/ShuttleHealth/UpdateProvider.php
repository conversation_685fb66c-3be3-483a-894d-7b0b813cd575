<?php

namespace App\Actions\ShuttleHealth;

use App\Models\Provider;

/**
 * Class UpdateProvider
 *
 * This class is responsible for updating provider.
 */
class UpdateProvider extends BaseDatabaseAction
{
    public function __construct(protected VerifyOrganizationNPIRecord $verifyOrganizationNPIAction)
    {
        //
    }

    public function execute(
        Provider $provider,
        array $providerData,
        array $npiRecordData,
    ): void {
        $validNpi = $this->verifyOrganizationNPIAction->execute(
            npiRecordData: $npiRecordData,
            existingNpi: $provider->npiRecord->npi ?? 0,
        );

        $callback = function () use ($provider, $providerData, $validNpi) {
            if (!empty($validNpi)) {
                if (isset($provider->npiRecord)) {
                    $provider->npiRecord()->update($validNpi);
                } else {
                    $provider->npiRecord()->create($validNpi);
                }
            }

            !empty($providerData) && $provider->update($providerData);
        };

        $this->dbTransactionCallback($callback);
    }
}
