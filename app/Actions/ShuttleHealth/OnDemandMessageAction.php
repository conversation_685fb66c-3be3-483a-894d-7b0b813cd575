<?php

namespace App\Actions\ShuttleHealth;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\OrganizationSettingNameEnum;
use App\Enums\PlaceholderEnum;
use App\Enums\ReplacePlaceholdersTrait;
use App\Models\OnDemandMessageTemplate;
use App\Models\Order;
use App\Notifications\Sms\Sms;
use App\Services\ShuttleHealth\ActivityLogger;
use App\Traits\HasOrganizationCommunicationWindowOpen;
use Illuminate\Http\Response;

class OnDemandMessageAction
{
    use HasOrganizationCommunicationWindowOpen;
    use ReplacePlaceholdersTrait;

    public function execute($request): Response
    {
        // Fetching required data
        $templateContent = OnDemandMessageTemplate::find($request['template_id']);
        $templateContent = $templateContent->body;

        $order = Order::with('distributor.organizationSettings')->findOrFail($request['order_id']);
        $patient = $order->globalPatient?->getPatient($order->distributor);
        $distributor = $order->distributor;

        // Get patient's timezone for audit logging (but send real-time regardless)
        $patientTimezone = $patient->timezone?->value ?? $patient->timezone ?? config('app.sh_timezone');
        $isOutsideWindow = !$this->isCommunicationWindowOpen($distributor, $patientTimezone);

        $websiteUrl = $order->distributor?->organizationSettings
            ->where('name', OrganizationSettingNameEnum::WEBSITE_URL->value)
            ->first()?->value ?? '';

        // Map placeholders to actual values
        $values = [
            PlaceholderEnum::PATIENT_NAME->value => $patient->getFullName(),
            PlaceholderEnum::ORDER_ID->value => $order->id ?? '',
            PlaceholderEnum::DISTRIBUTOR_NAME->value => $order->distributor?->name ?? '',
            PlaceholderEnum::DISTRIBUTOR_PHONE->value => $order->distributor?->phone ?? '',
            PlaceholderEnum::DISTRIBUTOR_WEBSITE->value => $websiteUrl ?? '',
        ];

        // Replace placeholders in the message template
        $processedContent = $this->replacePlaceholders($templateContent, $values);

        // Select the outbound phone number
        $outboundPhoneNumber = $order->distributor?->getFaxFromNumber();

        // Ensure there's a valid outbound number
        abort_if(
            !$outboundPhoneNumber,
            Response::HTTP_BAD_REQUEST,
            'No outbound phone number available.',
        );

        // Send SMS notification REAL-TIME regardless of communication window
        // On-demand messages should always be sent immediately
        $patient->notify(new Sms($processedContent, $outboundPhoneNumber, getUser()?->id, null, true));

        // Store the message in the activity log
        $this->storeOnDemandMessageToActivityLog($patient, $order, $processedContent, $isOutsideWindow);

        return response()->noContent();
    }

    private function storeOnDemandMessageToActivityLog($patient, $order, string $message, bool $sentDuringQuietHours = false): void
    {
        $metadata = [
            'message' => $message,
            'is_on_demand' => true,
            'sent_during_quiet_hours' => $sentDuringQuietHours,
            'message_type' => 'on_demand_realtime',
            'patient' => [
                'first_name' => $patient->first_name,
                'last_name' => $patient->last_name,
                'mobile' => $patient->phone_cell,
            ],
            'order' => [
                'order_id' => $order->id,
                'status' => $order->status,
                'type' => $order->type,
            ],
        ];
        ActivityLogger::write($patient, ActivityLogTypeEnum::MESSAGE, $metadata);

        ActivityLogger::write($order, ActivityLogTypeEnum::MESSAGE, $metadata);

    }
}
