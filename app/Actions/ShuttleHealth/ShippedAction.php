<?php

namespace App\Actions\ShuttleHealth;

use App\Contracts\StatusTransitionActionInterface;
use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Models\Order;
use App\Notifications\Sms\OrderShipped;
use App\Services\MessageQueueingService;

/**
 * Class ShippedAction
 *
 * This class is responsible for Shipped status transition action.
 */
class ShippedAction implements StatusTransitionActionInterface
{
    public function __construct(
        protected MessageQueueingService $queueingService,
    ) {
    }

    public function authorizeOrFail(Order $order): void
    {
    }

    public function execute(Order $order): void
    {
        $fromNumber = $order->distributor?->assigned_fax;
        $patient = $order->globalPatient?->getPatient($order->distributor);

        if ($patient) {
            $timezone = $patient->timezone?->value ?? $patient->timezone ?? config('app.sh_timezone');
            $shouldQueue = $this->queueingService->shouldQueueMessage('order_status_update', $order->distributor, $timezone);

            Logger::info('Sending order shipped notification', LogGroupEnum::DEFAULT, [
                'order_id' => $order->id,
                'patient_id' => $patient->id,
                'distributor_id' => $order->distributor?->id,
                'distributor_name' => $order->distributor?->name,
                'patient_timezone' => $timezone,
                'will_queue' => $shouldQueue,
                'tracking_number' => $order->shipping?->tracking_number ?? null,
                'has_tracking' => !empty($order->shipping?->tracking_number),
            ]);

            // Order status updates follow the queueing rules
            $patient->notify(new OrderShipped($order, $fromNumber));
        } else {
            Logger::warning('Order shipped notification skipped - no patient found', LogGroupEnum::DEFAULT, [
                'order_id' => $order->id,
                'global_patient_id' => $order->global_patient_id,
                'distributor_id' => $order->distributor_id,
            ]);
        }
    }
}
