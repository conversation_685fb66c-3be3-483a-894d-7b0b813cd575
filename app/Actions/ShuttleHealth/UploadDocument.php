<?php

namespace App\Actions\ShuttleHealth;

use App\Contracts\FileStorageServiceInterface;
use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Events\Imports\NewDocumentCreatedEvent;
use App\Extensions\Logger;
use App\Models\DocumentRequest;
use App\Models\File;
use App\Models\Order;
use App\Models\Patient;
use App\Models\PatientDocument;
use App\Models\User;
use App\Services\ShuttleHealth\DocumentUploadsCollectorService;
use App\Services\ShuttleHealth\OrganizationContextService;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

/**
 * Class UploadDocument
 *
 * This class is responsible for uploading the document.
 */
class UploadDocument
{
    public function __construct(
        protected FileStorageServiceInterface $fileStorageService,
        protected DocumentUploadsCollectorService $documentUploadsCollector,
        protected OrganizationContextService $organizationContextService,
    ) {
    }

    public function execute(
        UploadedFile|File $file,
        ?User $user,
        OrderDocumentTypeEnum $type,
        OrderDocumentSourceEnum $source,
        ?Order $order = null,
        ?DocumentRequest $documentRequest = null,
        ?string $title = null,
        ?array $details = null,
        ?Carbon $signatureDate = null,
        bool $ltp = false,
        ?Carbon $appointmentConfirmationDate = null,
        ?Carbon $expirationDate = null,
        ?Patient $patient = null,
    ): array {
        // for now, a document must be linked to order, patient or to document request
        if (!$order && !$documentRequest && !$patient) {
            throw new Exception('Order, document request or patient is required');
        }

        // set order from the document request if it is not set
        if (!$order && $documentRequest) {
            $order = $documentRequest->order;
        }

        DB::beginTransaction();
        $documentFile = null;
        try {
            $documentFile = $this->createOrderDocumentFile($file);
            $organization = $this->organizationContextService->getOrganization();

            $patientDocument = PatientDocument::create([
                'title' => $title ?? $documentFile->file_name,
                'type' => $type,
                'signature_date' => $signatureDate,
                'expiration_date' => $expirationDate,
                'ltp' => $ltp,
                'details' => $details,
                'source' => $source,
                'order_id' => $order?->id,
                'document_request_id' => $documentRequest?->id,
                'uploaded_by' => $user->id,
                'created_by_organization_id' => $organization->id,
                'created_by_organization_type' => $organization::class,
                'global_patient_id' => $patient?->global_patient_id,
            ]);

            // create a request history record if a document is linked to a request
            if ($documentRequest !== null) {
                $historyType = $file instanceof File && $file->type === FileTypeEnum::FAX
                    ? DocumentRequestHistoryTypeEnum::RECEIVED
                    : DocumentRequestHistoryTypeEnum::UPLOADED;

                $documentRequest->history()->create([
                    'type' => $historyType,
                    'order_document_id' => $patientDocument->id,
                    'user_id' => $user->id,
                    'details' => $details,
                    'activity_at' => Carbon::now(),
                ]);

                $documentRequest->update([
                    'status' => DocumentRequestStatusEnum::RECEIVED,
                    'appointment_confirmation_date' => $appointmentConfirmationDate,
                ]);
            }

            $documentFile->update(['relation_id' => $patientDocument->id]);

            NewDocumentCreatedEvent::dispatch($patientDocument);

            DB::commit();

            // collects uploaded documents with a delay, and then sends a notification to the order assigned user
            if ($order) {
                $this->documentUploadsCollector->recordUploadedDocumentForOrder(
                    $order->id,
                    $patientDocument->id,
                    $patientDocument->updated_at,
                );
                // attaches the document to the order if a document is uploaded from order
                $order->documents()->attach($patientDocument->id);
            }

            return [
                'orderDocument' => $patientDocument,
                'createdFile' => $documentFile,
            ];
        } catch (Throwable $e) {
            DB::rollBack();

            if ($documentFile) {
                Logger::error('Upload document error: \'' . $e->getMessage() . '\'. Deleting already stored file. fileId={' . $documentFile->id . '}');
                $this->fileStorageService->deleteFile($documentFile);
                Logger::error('Already stored file deleted successfully. fileId={\'' . $documentFile->id . '\'}.');
            } else {
                Logger::error('Upload document error: \'' . $e->getMessage() . '\'.');
            }

            throw $e;
        }
    }

    private function createOrderDocumentFile(UploadedFile|File $file): File
    {
        if ($file instanceof UploadedFile) {
            $fileName = $file->getClientOriginalName();
            $fileExtension = $file->getClientOriginalExtension();
        } else {
            $fileName = $file->file_name;
            $fileExtension = $file->extension;
        }

        $documentFile = File::create([
            'uuid' => Str::orderedUuid(),
            'type' => FileTypeEnum::PATIENT_DOCUMENT,
            'file_name' => $fileName,
            'extension' => $fileExtension,
            'relation_id' => null, // order document id as a relation_id will be updated later
        ]);

        if ($file instanceof UploadedFile) {
            $saved = $this->fileStorageService->uploadDocument(
                file: $documentFile,
                uploadedFile: $file,
            );
        } else {
            $saved = $this->fileStorageService->copy(
                originalFile: $file,
                destinationFile: $documentFile,
            );
        }

        if (!$saved) {
            throw new Exception('File saving error');
        }

        return $documentFile;
    }
}
