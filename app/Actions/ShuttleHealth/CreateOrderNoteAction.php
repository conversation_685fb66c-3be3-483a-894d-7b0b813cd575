<?php

namespace App\Actions\ShuttleHealth;

use App\Models\Order;
use App\Models\User;
use App\Notifications\Mail\Order\Notes\NoteAdded;
use App\Repositories\ActivityLogsRepository;

class CreateOrderNoteAction
{
    public function __construct(protected ActivityLogsRepository $activityLogsRepository)
    {

    }

    public function execute(User $user, Order $order, string $noteText): bool
    {
        $orderNote = $this->activityLogsRepository->createOrderNote(
            user: $user,
            order: $order,
            noteText: $noteText,
        );

        // Determine the target recipient based on the hierarchy:
        // 1. First priority: Order assigned distributor user
        // 2. Second priority: Distributor account owner
        $targetUser = $this->getTargetRecipient($order);

        if ($targetUser) {
            $targetUser->notify(new NoteAdded(
                order: $order,
                noteText: $noteText,
                sentFrom: $user->getFullName(), // Manufacturer user who created the note
                timeStamp: $orderNote->created_at->toDateTimeString(),
            ));
        }

        return true;
    }

    /**
     * Get the target recipient for the note notification based on the hierarchy:
     * 1. Order assigned distributor user
     * 2. Distributor account owner
     *
     * If neither exists, no email will be sent.
     */
    private function getTargetRecipient(Order $order): ?User
    {
        // 1. First priority: Order assigned distributor user
        if ($order->distributorUser) {
            return $order->distributorUser;
        }

        // 2. Second priority: Distributor account owner
        if ($order->distributor?->owner) {
            return $order->distributor->owner;
        }

        return null;
    }
}
