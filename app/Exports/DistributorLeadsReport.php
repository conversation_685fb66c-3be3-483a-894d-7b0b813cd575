<?php

namespace App\Exports;

use App\Enums\AddressTypeEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PayerTypeEnum;
use App\Enums\ProductQuantityTypeEnum;
use App\Models\Address;
use App\Models\Distributor;
use App\Models\Lead;
use App\Models\Payer;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

/**
 * Class DistributorLeadsReport
 *
 * Exports distributor leads to a CSV file.
 */
readonly class DistributorLeadsReport implements FromArray, WithHeadings
{
    public const DATE_FORMAT = 'm-d-Y';

    public function __construct(private Distributor $distributor)
    {
        //
    }

    private function reportData(): array
    {
        $data = [];
        $selfPayer = Payer::query()->where('internal_type', PayerTypeEnum::SELF)->first();

        $this->distributor
            ->leads()
            ->with([
                'addresses',
                'assignedUser',
                'distributorCampaign',
                'order',
                'order.cancellation',
                'order.products',
                'order.products.cmsHcpcs',
                'order.products.manufacturer',
                'order.shipping',
                'payers',
                'providerUser',
                'providerUser.npiRecord',
            ])
            ->chunk(500, function (Collection $leads) use (&$data, $selfPayer) {
                /** @var Lead $lead */
                foreach ($leads as $lead) {
                    /** @var Address $address */
                    $address = $lead->addresses->firstWhere('type', AddressTypeEnum::MAILING);
                    $assignedUser = $lead->assignedUser;
                    $campaign = $lead->distributorCampaign;
                    $providerUser = $lead->providerUser;
                    $primaryInsurance = $lead->getActivePayerByPriority(PatientPayerPriorityEnum::PRIMARY) ?? $selfPayer;
                    $secondInsurance = $lead->getActivePayerByPriority(PatientPayerPriorityEnum::SECONDARY);
                    $tertiaryInsurance = $lead->getActivePayerByPriority(PatientPayerPriorityEnum::TERTIARY);
                    $order = $lead->order;
                    $products = $order && $order->products->isNotEmpty()
                        ? $order->products
                        : $lead->products;
                    $productsId = '';
                    $productsName = '';
                    $productsHcpc = '';
                    $productsQuantity = '';
                    $productsManufacturer = '';

                    if ($products) {
                        foreach ($products as $product) {
                            $productsId .= $product->id . ';';
                            $productsName .= $product->name . ';';

                            if ($product->cmsHcpcs->isNotEmpty()) {
                                foreach ($product->cmsHcpcs as $cmsHcpc) {
                                    $productsHcpc .= $cmsHcpc->code . ',';
                                }

                                $productsHcpc .= ';';
                            }

                            $productsQuantity .= ($product->quantity_type === ProductQuantityTypeEnum::STANDARD ? $product->pivot->measure_count : $product->pivot->narrative_measure_count) . ';';
                            $productsManufacturer .= $product->manufacturer->name . ';';
                        }
                    }

                    $notes = '';

                    if ($lead->notes) {
                        foreach ($lead->notes as $note) {
                            $notes .= $note['question'] . ':' . $note['response'] . ';';
                        }
                    }

                    $data[] = [
                        $lead->id,
                        $lead->first_name,
                        $lead->last_name,
                        $assignedUser?->getFullName() . ($assignedUser?->isInactive() ? ' (inactive)' : ''),
                        $lead->date_of_birth?->format(self::DATE_FORMAT),
                        $lead->mobile,
                        $lead->gender?->value,
                        $lead->sms_enabled ? 'Yes' : 'No',
                        $lead->email_enabled ? 'Yes' : 'No',
                        $lead->call_enabled ? 'Yes' : 'No',
                        $lead->email,
                        $lead->payer_type?->value,
                        $campaign->name,
                        $campaign->id,
                        $campaign->created_date?->format(self::DATE_FORMAT),
                        $lead->marketing_id,
                        $lead->converted_date?->format(self::DATE_FORMAT),
                        $lead->policy_number,
                        $lead->source,
                        $lead->source_url,
                        $lead->qualified_date?->format(self::DATE_FORMAT),
                        $lead->quality_rank->value,
                        $lead->status->value,
                        $lead->created_date?->format(self::DATE_FORMAT),
                        // follow_up_at is date field with confusing name
                        $lead->follow_up_at?->format(self::DATE_FORMAT),
                        $lead->canceled_reason?->value,
                        $lead->canceled_date?->format(self::DATE_FORMAT),
                        $address?->address_line_1,
                        $address?->address_line_2,
                        $address?->city,
                        $address?->state,
                        $address?->zip,
                        $providerUser?->getFullName(),
                        $providerUser?->npi,
                        $primaryInsurance?->name,
                        $primaryInsurance?->id,
                        $primaryInsurance?->pivot?->policy_number,
                        $primaryInsurance?->pivot?->group_number,
                        $primaryInsurance?->pivot?->user_defined_payer_type?->value,
                        $secondInsurance?->name,
                        $secondInsurance?->id,
                        $secondInsurance?->pivot?->policy_number,
                        $secondInsurance?->pivot?->group_number,
                        $secondInsurance?->pivot?->user_defined_payer_type?->value,
                        $tertiaryInsurance?->name,
                        $tertiaryInsurance?->id,
                        $tertiaryInsurance?->pivot?->policy_number,
                        $tertiaryInsurance?->pivot?->group_number,
                        $tertiaryInsurance?->pivot?->user_defined_payer_type?->value,
                        $productsId,
                        $productsName,
                        $productsHcpc,
                        $productsQuantity,
                        $productsManufacturer,
                        $notes,
                        $order?->getGlobalId(),
                        $order?->status?->value,
                        $order?->cancellation?->reason?->value,
                        $order?->cancellation?->created_at?->timezone(config('app.sh_timezone'))->format(self::DATE_FORMAT),
                        // shipped_at is date field with confusing name
                        $order?->shipping?->shipped_at?->format(self::DATE_FORMAT),
                        $lead->preferredLanguage?->name ?? 'N/A', // Added for preferred language
                        $lead->first_contact_date?->format(self::DATE_FORMAT) ?? 'N/A', // Added for first contact date
                        $lead->first_contact_date_notes ?? 'N/A', // Added for first contact date notes
                    ];
                }
            });

        return $data;
    }

    public function array(): array
    {
        return $this->reportData();
    }

    public function headings(): array
    {
        return [
            'Lead ID',
            'Customer First Name',
            'Customer Last Name',
            'Assigned User',
            'Date of Birth',
            'Mobile Phone',
            'Gender',
            'Sms Opt-In Status',
            'Email Opt-In Status',
            'Call Opt-In Status',
            'Email',
            'Payer Type',
            'Campaign Name',
            'Campaign ID',
            'Campaign Created Date',
            'Marketing ID',
            'Converted Date',
            'Lead Policy Number',
            'Lead Source',
            'Lead Source URL',
            'Lead Qualified Date',
            'Lead Quality Rank',
            'Lead Status',
            'Lead Created Date',
            'Lead Follow Up Date',
            'Lead Cancelled Reason',
            'Lead Cancelled Date',
            'Lead Street Address 1',
            'Lead Street Address 2',
            'Lead City',
            'Lead State',
            'Lead ZIP',
            'Physician Name',
            'Physician NPI',
            'Primary Insurance Details - Payer Name',
            'Primary Insurance Details - Payer Shuttle Health ID',
            'Primary Insurance Details - Member ID',
            'Primary Insurance Details - Group Number',
            'Primary Insurance Details - Payer Type',
            'Secondary Insurance Details - Payer Name',
            'Secondary Insurance Details - Payer Shuttle Health ID',
            'Secondary Insurance Details - Member ID',
            'Secondary Insurance Details - Group Number',
            'Secondary Insurance Details - Payer Type',
            'Tertiary Insurance Details - Payer Name',
            'Tertiary Insurance Details - Payer Shuttle Health ID',
            'Tertiary Insurance Details - Member ID',
            'Tertiary Insurance Details - Group Number',
            'Tertiary Insurance Details - Payer Type',
            'Product Shuttle Health ID',
            'Product Name',
            'Product HCPC',
            'Product Quantity',
            'Product Manufacturer',
            'Form Notes',
            'Converted Order - Order ID',
            'Converted Order - Order Status',
            'Converted Order - Cancellation Reason',
            'Converted Order - Cancellation Date',
            'Converted Order - Shipment Date',
            'Preferred Language', // Added for preferred language
            'First Contact Date', // Added for first contact date
            'First Contact Date Notes', // Added for first contact date notes
        ];
    }
}
