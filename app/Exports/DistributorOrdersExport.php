<?php

namespace App\Exports;

use App\Actions\ShuttleHealth\GetFacilityPhoneFaxAddressAction;
use App\Enums\AddressTypeEnum;
use App\Enums\OrderTaskMetadataEnum;
use App\Enums\OrderTaskTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PatientPayerPriorityEnum;
use App\Enums\PayerTypeEnum;
use App\Enums\ProductQuantityTypeEnum;
use App\Models\Distributor;
use App\Models\Order;
use App\Models\Payer;
use App\Models\Product;
use App\Utils\PhoneNumberConverter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

/**
 * Class DistributorOrdersExport
 *
 * Exports distributor orders to a CSV file.
 */
class DistributorOrdersExport implements FromArray, WithHeadings
{
    private const DATE_FORMAT = 'm-d-Y';

    private const CUSTOMER_ID = 'Customer ID';
    private const ORDER_ID = 'Order ID';
    private const CUSTOMER_FIRST_NAME = 'Customer First Name';
    private const CUSTOMER_LAST_NAME = 'Customer Last Name';
    private const ASSIGNED_USER = 'Assigned User';
    private const DATE_OF_BIRTH = 'Date of Birth';
    private const MOBILE_PHONE = 'Mobile Phone';
    private const GENDER = 'Gender';
    private const SOR_PATIENT_ID = 'SOR Patient ID';
    private const EMAIL = 'Email';
    private const COMMUNICATIONS_OPT_IN = 'Communications Opt In';

    private const MAILING_ADDRESS_STREET_1 = 'Mailing Address - Street Address 1';
    private const MAILING_ADDRESS_STREET_2 = 'Mailing Address - Street Address 2';
    private const MAILING_ADDRESS_CITY = 'Mailing Address - City';
    private const MAILING_ADDRESS_STATE = 'Mailing Address - State';
    private const MAILING_ADDRESS_ZIP = 'Mailing Address - ZIP';

    private const BILLING_ADDRESS_STREET_1 = 'Billing Address - Street Address 1';
    private const BILLING_ADDRESS_STREET_2 = 'Billing Address - Street Address 2';
    private const BILLING_ADDRESS_CITY = 'Billing Address - City';
    private const BILLING_ADDRESS_STATE = 'Billing Address - State';
    private const BILLING_ADDRESS_ZIP = 'Billing Address - ZIP';

    private const PRIMARY_INSURANCE_PAYER_NAME = 'Primary Insurance Details - Payer Name';
    private const PRIMARY_INSURANCE_PAYER_SHUTTLE_ID = 'Primary Insurance Details - Payer Shuttle Health ID';
    private const PRIMARY_INSURANCE_MEMBER_ID = 'Primary Insurance Details - Member ID';
    private const PRIMARY_INSURANCE_GROUP_NUMBER = 'Primary Insurance Details - Group Number';
    private const PRIMARY_INSURANCE_PAYER_TYPE = 'Primary Insurance Details - Payer Type';
    private const PRIMARY_INSURANCE_EFFECTIVE_DATE = 'Primary Insurance Details - Effective Date';

    private const SECONDARY_INSURANCE_PAYER_NAME = 'Secondary Insurance Details - Payer Name';
    private const SECONDARY_INSURANCE_PAYER_SHUTTLE_ID = 'Secondary Insurance Details - Payer Shuttle Health ID';
    private const SECONDARY_INSURANCE_MEMBER_ID = 'Secondary Insurance Details - Member ID';
    private const SECONDARY_INSURANCE_GROUP_NUMBER = 'Secondary Insurance Details - Group Number';
    private const SECONDARY_INSURANCE_PAYER_TYPE = 'Secondary Insurance Details - Payer Type';
    private const SECONDARY_INSURANCE_EFFECTIVE_DATE = 'Secondary Insurance Details - Effective Date';

    private const TERTIARY_INSURANCE_PAYER_NAME = 'Tertiary Insurance Details - Payer Name';
    private const TERTIARY_INSURANCE_PAYER_SHUTTLE_ID = 'Tertiary Insurance Details - Payer Shuttle Health ID';
    private const TERTIARY_INSURANCE_MEMBER_ID = 'Tertiary Insurance Details - Member ID';
    private const TERTIARY_INSURANCE_GROUP_NUMBER = 'Tertiary Insurance Details - Group Number';
    private const TERTIARY_INSURANCE_PAYER_TYPE = 'Tertiary Insurance Details - Payer Type';
    private const TERTIARY_INSURANCE_EFFECTIVE_DATE = 'Tertiary Insurance Details - Effective Date';

    private const PHYSICIAN_NAME = 'Physician Name';
    private const PHYSICIAN_NPI = 'Physician NPI';
    private const PHYSICIAN_SHUTTLE_ID = 'Physician Shuttle Health ID';

    private const FACILITY_NAME = 'Facility Name';
    private const FACILITY_SHUTTLE_ID = 'Facility Shuttle Health ID';
    private const FACILITY_ADDRESS_STREET_1 = 'Facility Details - Street Address 1';
    private const FACILITY_ADDRESS_STREET_2 = 'Facility Details - Street Address 2';
    private const FACILITY_ADDRESS_CITY = 'Facility Details - City';
    private const FACILITY_ADDRESS_STATE = 'Facility Details - State';
    private const FACILITY_ADDRESS_ZIP = 'Facility Details - ZIP';
    private const FACILITY_OFFICE_PHONE = 'Facility Details Office Phone';
    private const FACILITY_OFFICE_FAX = 'Facility Details Office Fax';

    private const ORDER_SOR_ID = 'Order System of Record ID';
    private const ORDER_STATUS = 'Order Status';
    private const ORDER_CREATION_DATE = 'Order Creation Date';
    private const NEXT_FOLLOW_UP_DATE = 'Next Follow Up Date';
    private const SOURCE = 'Source';
    private const LEAD_SHUTTLE_ID = 'Lead Shuttle Health ID';
    private const DESCRIPTION = 'Description';
    private const CANCELLATION_REASON = 'Cancellation Reason';
    private const CANCELLATION_DATE = 'Cancellation Date';

    private const ORDER_REROUTED = 'Order Rerouted';
    private const ROUTED_TO = 'Routed To';

    private const PRE_AUTH_STATUS = 'Pre Authorization Status';
    private const PRE_AUTH_SUBMISSION_DATE = 'Pre Authorization Submission Date';
    private const PRE_AUTH_COMPLETE_DATE = 'Pre Authorization Complete Date';
    private const PRE_AUTH_PA_NUMBER = 'Pre Authorization PA #';
    private const PRE_AUTH_RULING = 'Pre Authorization Ruling';
    private const PRE_AUTH_NOTES = 'Pre Authorization Notes';

    private const CHART_NOTES_STATUS = 'Chart Notes Status';
    private const CHART_NOTES_REQUEST_DATE = 'Chart Notes Initial Request Date';
    private const CHART_NOTES_COMPLETE_DATE = 'Chart Notes Complete Date';
    private const CHART_NOTES_NOTES = 'Chart Notes Notes';

    private const CMN_STATUS = 'Certificate of Medical Necessity Status';
    private const CMN_REQUEST_DATE = 'Certificate of Medical Necessity Initial Request Date';
    private const CMN_COMPLETE_DATE = 'Certificate of Medical Necessity Complete Date';
    private const CMN_NOTES = 'Certificate of Medical Necessity Notes';

    private const CUSTOMER_AUTH_STATUS = 'Customer Authorization Status';
    private const CUSTOMER_AUTH_REQUEST_DATE = 'Customer Authorization Initial Request Date';
    private const CUSTOMER_AUTH_COMPLETE_DATE = 'Customer Authorization Complete Date';
    private const CUSTOMER_AUTH_NOTES = 'Customer Authorization Notes';

    private const AOB_STATUS = 'AOB Status';
    private const AOB_REQUEST_DATE = 'AOB Initial Request Date';
    private const AOB_COMPLETE_DATE = 'AOB Complete Date';
    private const AOB_NOTES = 'AOB Notes';

    private const PRODUCT_SHUTTLE_ID = 'Product Shuttle Health ID';
    private const PRODUCT_NAME = 'Product Name';
    private const PRODUCT_HCPC = 'Product HCPC';
    private const PRODUCT_QUANTITY = 'Product Quantity';
    private const PRODUCT_MANUFACTURER = 'Product Manufacturer';
    private const PRODUCT_SERIAL_NUMBER = 'Product Serial Number';
    private const TRACKING_NUMBER = 'Tracking Number';
    private const SHIPMENT_DATE = 'Shipment Date';

    private const TERRITORY_NAME = 'Territory Name';
    private const TERRITORY_MANAGER = 'Territory Manager';
    private const DISTRICT_NAME = 'District Name';
    private const DISTRICT_MANAGER = 'District Manager';
    private const REGION_NAME = 'Region Name';
    private const REGION_MANAGER = 'Region Manager';
    private const PREFERRED_LANGUAGE = 'preferred_language';

    private array $orderTaskColumns = [
        OrderTaskTypeEnum::CUSTOM->value => [],
        OrderTaskTypeEnum::LAB->value => [],
    ];

    private array $data;

    public function __construct(
        protected readonly Distributor $distributor,
        protected readonly GetFacilityPhoneFaxAddressAction $getFacilityPhoneFaxAddressAction,
        protected readonly ?array $manufacturerIds = null,
        protected readonly ?array $productCategoryIds = null,
        protected readonly ?array $status = null,
        protected readonly ?array $creationDates = null,
        protected readonly ?array $shippingDates = null,
        protected readonly ?array $cancellationDates = null,
    ) {
        $this->data = $this->prepareData();
    }

    private function prepareData(): array
    {
        $result = [];
        $selfPayer = Payer::query()->where('internal_type', PayerTypeEnum::SELF)->first();

        $this->distributor
            ->orders()
            ->with([
                'cancellation',
                'distributorTerritory.district.manager',
                'distributorTerritory.district.region.manager',
                'distributorTerritory.manager',
                'distributorUser',
                'escalation.assignedOrder.distributor',
                'facility',
                'lead',
                'patient.addresses',
                'patient.payers',
                'products.cmsHcpcs',
                'products.commercialHcpcs',
                'products.manufacturer',
                'products.productCategories',
                'provider.addresses',
                'providerUser.npiRecord',
                'shipping',
                'tasks',
            ])
            ->where('type', OrderTypeEnum::NEW_PRESCRIPTION)
            ->when(!empty($this->productCategoryIds), function (Builder $query) {
                $query->whereHas('products.productCategories', function ($productCategories) {
                    $productCategories->whereIn('product_category_id', $this->productCategoryIds);
                });
            })
            ->when(!empty($this->manufacturerIds), function (Builder $query) {
                $query->whereHas('products', function ($products) {
                    $products->whereIn('manufacturer_id', $this->manufacturerIds);
                });
            })
            ->when(!empty($this->status), function (Builder $query) {
                $query->whereIn('status', $this->status);
            })
            ->when(!empty($this->creationDates), function (Builder $query) {
                $query->whereBetween('created_at', $this->creationDates);
            })
            ->when(!empty($this->shippingDates), function (Builder $query) {
                $query->whereHas('shipping', function ($query) {
                    $query->whereBetween('order_shippings.shipped_at', $this->shippingDates);
                });
            })
            ->when(!empty($this->cancellationDates), function (Builder $query) {
                $query->whereHas('cancellation', function ($query) {
                    $query->whereBetween('order_cancellations.created_at', $this->cancellationDates);
                });
            })
            ->chunk(500, function (Collection $orders) use (&$result, $selfPayer) {
                /** @var Order $order */
                foreach ($orders as $order) {
                    $patient = $order->patient;
                    $patientPrimaryPayer = $patient?->getActivePayerByPriority(PatientPayerPriorityEnum::PRIMARY) ?? $selfPayer;
                    $patientSecondaryPayer = $patient?->getActivePayerByPriority(PatientPayerPriorityEnum::SECONDARY);
                    $patientTertiaryPayer = $patient?->getActivePayerByPriority(PatientPayerPriorityEnum::TERTIARY);

                    $patientMailingAddress = $patient?->addresses->where('type', AddressTypeEnum::MAILING)->first();
                    $patientBillingAddress = $patient?->addresses->where('type', AddressTypeEnum::BILLING)->first();

                    [$facilityPhone, $facilityFax, $facilityAddress] = $this->getFacilityPhoneFaxAddressAction->execute(
                        $order->provider,
                        $order->providerUser,
                        $order->facility,
                        $this->distributor,
                    );

                    $products = $order->products;
                    $productsId = implode(';', $products->pluck('id')->toArray());
                    $productsName = implode(';', $products->pluck('name')->toArray());
                    $productsHcpc = '';
                    $productsQuantity = '';
                    $productsManufacturer = '';
                    $productsSerial = '';

                    if ($products) {
                        /** @var Product $product */
                        foreach ($products as $product) {
                            $hcpcCodes = collect();

                            if ($product->cmsHcpcs->isNotEmpty()) {
                                $hcpcCodes = $hcpcCodes->merge($product->cmsHcpcs->pluck('code'));
                            }

                            if ($product->commercialHcpcs->isNotEmpty()) {
                                $hcpcCodes = $hcpcCodes->merge($product->commercialHcpcs->pluck('code'));
                            }

                            $productsHcpc .= $hcpcCodes->unique()->implode(',') . ';';
                            $productsQuantity .= ($product->quantity_type === ProductQuantityTypeEnum::STANDARD ? $product->pivot?->measure_count : $product->pivot?->narrative_measure_count) . ';';
                            $productsManufacturer .= $product->manufacturer->name . ';';
                            $productsSerial .= $product->pivot?->serial_number . ';';
                        }
                    }

                    $preAuthTask = $order->tasks->where('type', OrderTaskTypeEnum::PRE_AUTHORIZATION)->first();
                    $chartNotesTask = $order->tasks->where('type', OrderTaskTypeEnum::CHART_NOTES)->first();
                    $cmnTask = $order->tasks->where('type', OrderTaskTypeEnum::CMN)->first();
                    $customerAuthTask = $order->tasks->where('type', OrderTaskTypeEnum::CUSTOMER_AUTHORIZATION)->first();
                    $aobTask = $order->tasks->where('type', OrderTaskTypeEnum::AOB)->first();

                    $customTasks = $order->tasks->where('type', OrderTaskTypeEnum::CUSTOM);
                    $labTasks = $order->tasks->where('type', OrderTaskTypeEnum::LAB);

                    $customTasksData = $this->getMultipleTasksData(OrderTaskTypeEnum::CUSTOM, $customTasks);
                    $labTasksData = $this->getMultipleTasksData(OrderTaskTypeEnum::LAB, $labTasks);

                    $row = [
                        self::CUSTOMER_ID => $patient?->id,
                        self::ORDER_ID => $order->getGlobalId(),
                        self::CUSTOMER_FIRST_NAME => $patient?->first_name,
                        self::CUSTOMER_LAST_NAME => $patient?->last_name,
                        self::ASSIGNED_USER => $order->distributorUser?->getFullName() . ($order->distributorUser?->isInactive() ? ' (inactive)' : ''),
                        self::DATE_OF_BIRTH => $patient?->date_of_birth->format(self::DATE_FORMAT),
                        self::MOBILE_PHONE => $patient?->mobile,
                        self::GENDER => $patient?->gender->value,
                        self::SOR_PATIENT_ID => $patient?->external_id,
                        self::EMAIL => $patient?->email,
                        self::COMMUNICATIONS_OPT_IN => $patient?->sms_enabled ? 'true' : 'false',

                        self::MAILING_ADDRESS_STREET_1 => $patientMailingAddress?->address_line_1,
                        self::MAILING_ADDRESS_STREET_2 => $patientMailingAddress?->address_line_2,
                        self::MAILING_ADDRESS_CITY => $patientMailingAddress?->city,
                        self::MAILING_ADDRESS_STATE => $patientMailingAddress?->state,
                        self::MAILING_ADDRESS_ZIP => $patientMailingAddress?->zip,

                        self::BILLING_ADDRESS_STREET_1 => $patientBillingAddress?->address_line_1,
                        self::BILLING_ADDRESS_STREET_2 => $patientBillingAddress?->address_line_2,
                        self::BILLING_ADDRESS_CITY => $patientBillingAddress?->city,
                        self::BILLING_ADDRESS_STATE => $patientBillingAddress?->state,
                        self::BILLING_ADDRESS_ZIP => $patientBillingAddress?->zip,

                        self::PRIMARY_INSURANCE_PAYER_NAME => $patientPrimaryPayer?->name,
                        self::PRIMARY_INSURANCE_PAYER_SHUTTLE_ID => $patientPrimaryPayer?->pivot?->payer_id,
                        self::PRIMARY_INSURANCE_MEMBER_ID => $patientPrimaryPayer?->pivot?->policy_number,
                        self::PRIMARY_INSURANCE_GROUP_NUMBER => $patientPrimaryPayer?->pivot?->group_number,
                        self::PRIMARY_INSURANCE_PAYER_TYPE => $patientPrimaryPayer?->pivot?->user_defined_payer_type?->value,
                        self::PRIMARY_INSURANCE_EFFECTIVE_DATE => $patientPrimaryPayer?->pivot?->insurance_end_date,

                        self::SECONDARY_INSURANCE_PAYER_NAME => $patientSecondaryPayer?->name,
                        self::SECONDARY_INSURANCE_PAYER_SHUTTLE_ID => $patientSecondaryPayer?->pivot?->payer_id,
                        self::SECONDARY_INSURANCE_MEMBER_ID => $patientSecondaryPayer?->pivot?->policy_number,
                        self::SECONDARY_INSURANCE_GROUP_NUMBER => $patientSecondaryPayer?->pivot?->group_number,
                        self::SECONDARY_INSURANCE_PAYER_TYPE => $patientSecondaryPayer?->pivot?->user_defined_payer_type?->value,
                        self::SECONDARY_INSURANCE_EFFECTIVE_DATE => $patientSecondaryPayer?->pivot?->insurance_end_date,

                        self::TERTIARY_INSURANCE_PAYER_NAME => $patientTertiaryPayer?->name,
                        self::TERTIARY_INSURANCE_PAYER_SHUTTLE_ID => $patientTertiaryPayer?->pivot?->payer_id,
                        self::TERTIARY_INSURANCE_MEMBER_ID => $patientTertiaryPayer?->pivot?->policy_number,
                        self::TERTIARY_INSURANCE_GROUP_NUMBER => $patientTertiaryPayer?->pivot?->group_number,
                        self::TERTIARY_INSURANCE_PAYER_TYPE => $patientTertiaryPayer?->pivot?->user_defined_payer_type?->value,
                        self::TERTIARY_INSURANCE_EFFECTIVE_DATE => $patientTertiaryPayer?->pivot?->insurance_end_date,

                        self::PHYSICIAN_NAME => $order->providerUser?->getFullName(),
                        self::PHYSICIAN_NPI => $order->providerUser?->npiRecord?->npi,
                        self::PHYSICIAN_SHUTTLE_ID => $order->provider_user_id,

                        self::FACILITY_NAME => $order->facility?->name ?? $order->provider?->name,
                        self::FACILITY_SHUTTLE_ID => $order->provider_id,
                        self::FACILITY_ADDRESS_STREET_1 => $facilityAddress?->address_line_1,
                        self::FACILITY_ADDRESS_STREET_2 => $facilityAddress?->address_line_2,
                        self::FACILITY_ADDRESS_CITY => $facilityAddress?->city,
                        self::FACILITY_ADDRESS_STATE => $facilityAddress?->state,
                        self::FACILITY_ADDRESS_ZIP => $facilityAddress?->zip,
                        self::FACILITY_OFFICE_PHONE => PhoneNumberConverter::formatPhoneNumber($facilityPhone),
                        self::FACILITY_OFFICE_FAX => PhoneNumberConverter::formatPhoneNumber($facilityFax),

                        self::ORDER_SOR_ID => $order->external_order_id,
                        self::ORDER_STATUS => $order->status->value,
                        self::ORDER_CREATION_DATE => $order->created_at->timezone(config('app.sh_timezone'))->format(self::DATE_FORMAT),
                        self::NEXT_FOLLOW_UP_DATE => $order->follow_up_at?->timezone(config('app.sh_timezone'))->format(self::DATE_FORMAT),
                        self::SOURCE => $order->orderSource?->order_source_name ?? '-',
                        self::LEAD_SHUTTLE_ID => $order->lead?->id,
                        self::DESCRIPTION => $order?->source_description ?? '-',
                        self::CANCELLATION_REASON => $order->cancellation?->reason->value,
                        self::CANCELLATION_DATE => $order->cancellation?->created_at->timezone(config('app.sh_timezone'))->format(self::DATE_FORMAT),

                        self::ORDER_REROUTED => $order->escalation ? 'Yes' : 'No',
                        self::ROUTED_TO => $order->escalation?->assignedOrder?->distributor?->name,

                        self::PRE_AUTH_STATUS => $preAuthTask?->status->value,
                        self::PRE_AUTH_SUBMISSION_DATE => $preAuthTask?->metadata[OrderTaskMetadataEnum::SUBMISSION_DATE->value] ?? null,
                        self::PRE_AUTH_COMPLETE_DATE => $preAuthTask?->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null,
                        self::PRE_AUTH_PA_NUMBER => $preAuthTask?->metadata[OrderTaskMetadataEnum::PA->value] ?? null,
                        self::PRE_AUTH_RULING => $preAuthTask?->metadata[OrderTaskMetadataEnum::RULING->value] ?? null,
                        self::PRE_AUTH_NOTES => $preAuthTask?->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null,

                        self::CHART_NOTES_STATUS => $chartNotesTask?->status->value,
                        self::CHART_NOTES_REQUEST_DATE => $chartNotesTask?->metadata[OrderTaskMetadataEnum::INITIAL_REQUEST_DATE->value] ?? null,
                        self::CHART_NOTES_COMPLETE_DATE => $chartNotesTask?->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null,
                        self::CHART_NOTES_NOTES => $chartNotesTask?->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null,

                        self::CMN_STATUS => $cmnTask?->status->value,
                        self::CMN_REQUEST_DATE => $cmnTask?->metadata[OrderTaskMetadataEnum::INITIAL_REQUEST_DATE->value] ?? null,
                        self::CMN_COMPLETE_DATE => $cmnTask?->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null,
                        self::CMN_NOTES => $cmnTask?->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null,

                        self::CUSTOMER_AUTH_STATUS => $customerAuthTask?->status->value,
                        self::CUSTOMER_AUTH_REQUEST_DATE => $customerAuthTask?->metadata[OrderTaskMetadataEnum::INITIAL_REQUEST_DATE->value] ?? null,
                        self::CUSTOMER_AUTH_COMPLETE_DATE => $customerAuthTask?->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null,
                        self::CUSTOMER_AUTH_NOTES => $customerAuthTask?->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null,

                        self::AOB_STATUS => $aobTask?->status->value,
                        self::AOB_REQUEST_DATE => $aobTask?->metadata[OrderTaskMetadataEnum::INITIAL_REQUEST_DATE->value] ?? null,
                        self::AOB_COMPLETE_DATE => $aobTask?->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null,
                        self::AOB_NOTES => $aobTask?->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null,

                        ...$customTasksData,
                        ...$labTasksData,

                        self::PRODUCT_SHUTTLE_ID => $productsId,
                        self::PRODUCT_NAME => $productsName,
                        self::PRODUCT_HCPC => $productsHcpc,
                        self::PRODUCT_QUANTITY => $productsQuantity,
                        self::PRODUCT_MANUFACTURER => $productsManufacturer,
                        self::PRODUCT_SERIAL_NUMBER => $productsSerial,
                        self::TRACKING_NUMBER => $order->shipping?->tracking_number,
                        // shipped_at is date field with confusing name
                        self::SHIPMENT_DATE => $order->shipping?->shipped_at?->format(self::DATE_FORMAT),

                        self::TERRITORY_NAME => $order->distributorTerritory?->name,
                        self::TERRITORY_MANAGER => $order->distributorTerritory?->manager?->getFullName(),
                        self::DISTRICT_NAME => $order->distributorTerritory?->district?->name,
                        self::DISTRICT_MANAGER => $order->distributorTerritory?->district?->manager?->getFullName(),
                        self::REGION_NAME => $order->distributorTerritory?->district?->region?->name,
                        self::REGION_MANAGER => $order->distributorTerritory?->district?->region?->manager?->getFullName(),
                        self::PREFERRED_LANGUAGE => $patient->preferredLanguage?->name ?? 'N/A',
                    ];

                    $result[] = $row;
                }
            });

        // Fix the array to prevent column shifts when some orders have 1 custom or lab task, and others have 2. This ensures all columns align correctly.
        foreach ($result as $key => $item) {
            $data = $item;
            unset($result[$key]);

            foreach ($this->formatHeadings() as $heading) {
                $result[$key][$heading] = $data[$heading] ?? '';
            }
        }

        return $result;
    }

    public function headings(): array
    {
        return $this->formatHeadings();
    }

    private function formatHeadings(): array
    {
        return [
            self::CUSTOMER_ID,
            self::ORDER_ID,
            self::CUSTOMER_FIRST_NAME,
            self::CUSTOMER_LAST_NAME,
            self::ASSIGNED_USER,
            self::DATE_OF_BIRTH,
            self::MOBILE_PHONE,
            self::GENDER,
            self::SOR_PATIENT_ID,
            self::EMAIL,
            self::COMMUNICATIONS_OPT_IN,

            self::MAILING_ADDRESS_STREET_1,
            self::MAILING_ADDRESS_STREET_2,
            self::MAILING_ADDRESS_CITY,
            self::MAILING_ADDRESS_STATE,
            self::MAILING_ADDRESS_ZIP,

            self::BILLING_ADDRESS_STREET_1,
            self::BILLING_ADDRESS_STREET_2,
            self::BILLING_ADDRESS_CITY,
            self::BILLING_ADDRESS_STATE,
            self::BILLING_ADDRESS_ZIP,

            self::PRIMARY_INSURANCE_PAYER_NAME,
            self::PRIMARY_INSURANCE_PAYER_SHUTTLE_ID,
            self::PRIMARY_INSURANCE_MEMBER_ID,
            self::PRIMARY_INSURANCE_GROUP_NUMBER,
            self::PRIMARY_INSURANCE_PAYER_TYPE,
            self::PRIMARY_INSURANCE_EFFECTIVE_DATE,

            self::SECONDARY_INSURANCE_PAYER_NAME,
            self::SECONDARY_INSURANCE_PAYER_SHUTTLE_ID,
            self::SECONDARY_INSURANCE_MEMBER_ID,
            self::SECONDARY_INSURANCE_GROUP_NUMBER,
            self::SECONDARY_INSURANCE_PAYER_TYPE,
            self::SECONDARY_INSURANCE_EFFECTIVE_DATE,

            self::TERTIARY_INSURANCE_PAYER_NAME,
            self::TERTIARY_INSURANCE_PAYER_SHUTTLE_ID,
            self::TERTIARY_INSURANCE_MEMBER_ID,
            self::TERTIARY_INSURANCE_GROUP_NUMBER,
            self::TERTIARY_INSURANCE_PAYER_TYPE,
            self::TERTIARY_INSURANCE_EFFECTIVE_DATE,

            self::PHYSICIAN_NAME,
            self::PHYSICIAN_NPI,
            self::PHYSICIAN_SHUTTLE_ID,

            self::FACILITY_NAME,
            self::FACILITY_SHUTTLE_ID,
            self::FACILITY_ADDRESS_STREET_1,
            self::FACILITY_ADDRESS_STREET_2,
            self::FACILITY_ADDRESS_CITY,
            self::FACILITY_ADDRESS_STATE,
            self::FACILITY_ADDRESS_ZIP,
            self::FACILITY_OFFICE_PHONE,
            self::FACILITY_OFFICE_FAX,

            self::ORDER_SOR_ID,
            self::ORDER_STATUS,
            self::ORDER_CREATION_DATE,
            self::NEXT_FOLLOW_UP_DATE,
            self::SOURCE,
            self::LEAD_SHUTTLE_ID,
            self::DESCRIPTION,
            self::CANCELLATION_REASON,
            self::CANCELLATION_DATE,

            self::PRE_AUTH_STATUS,
            self::PRE_AUTH_SUBMISSION_DATE,
            self::PRE_AUTH_COMPLETE_DATE,
            self::PRE_AUTH_PA_NUMBER,
            self::PRE_AUTH_RULING,
            self::PRE_AUTH_NOTES,

            self::CHART_NOTES_STATUS,
            self::CHART_NOTES_REQUEST_DATE,
            self::CHART_NOTES_COMPLETE_DATE,
            self::CHART_NOTES_NOTES,

            self::CMN_STATUS,
            self::CMN_REQUEST_DATE,
            self::CMN_COMPLETE_DATE,
            self::CMN_NOTES,

            self::CUSTOMER_AUTH_STATUS,
            self::CUSTOMER_AUTH_REQUEST_DATE,
            self::CUSTOMER_AUTH_COMPLETE_DATE,
            self::CUSTOMER_AUTH_NOTES,

            self::AOB_STATUS,
            self::AOB_REQUEST_DATE,
            self::AOB_COMPLETE_DATE,
            self::AOB_NOTES,

            ...$this->orderTaskColumns[OrderTaskTypeEnum::CUSTOM->value],
            ...$this->orderTaskColumns[OrderTaskTypeEnum::LAB->value],

            self::PRODUCT_SHUTTLE_ID,
            self::PRODUCT_NAME,
            self::PRODUCT_HCPC,
            self::PRODUCT_QUANTITY,
            self::PRODUCT_MANUFACTURER,
            self::PRODUCT_SERIAL_NUMBER,
            self::TRACKING_NUMBER,
            self::SHIPMENT_DATE,

            self::TERRITORY_NAME,
            self::TERRITORY_MANAGER,
            self::DISTRICT_NAME,
            self::DISTRICT_MANAGER,
            self::REGION_NAME,
            self::REGION_MANAGER,
            self::PREFERRED_LANGUAGE,
        ];
    }

    public function array(): array
    {
        return $this->data;
    }

    private function getMultipleTasksData(OrderTaskTypeEnum $type, Collection $tasks): array
    {
        $tasksData = [];

        $idx = 1;

        foreach ($tasks as $task) {
            $typeName = ucfirst($type->value);

            $tasksData[sprintf('%s %d Task Name', $typeName, $idx)] = $task->metadata[OrderTaskMetadataEnum::TASK_NAME->value] ?? null;
            $tasksData[sprintf('%s %d Status', $typeName, $idx)] = $task->status->value;
            $tasksData[sprintf('%s %d Initial Request Date', $typeName, $idx)] = $task->metadata[OrderTaskMetadataEnum::INITIAL_REQUEST_DATE->value] ?? null;
            $tasksData[sprintf('%s %d Complete Date', $typeName, $idx)] = $task->metadata[OrderTaskMetadataEnum::COMPLETE_DATE->value] ?? null;
            $tasksData[sprintf('%s %d Notes', $typeName, $idx)] = $task->metadata[OrderTaskMetadataEnum::NOTES->value] ?? null;

            $idx++;
        }

        $keys = array_keys($tasksData);

        if (count($keys) > count($this->orderTaskColumns[$type->value])) {
            $this->orderTaskColumns[$type->value] = $keys;
        }

        return $tasksData;
    }
}
