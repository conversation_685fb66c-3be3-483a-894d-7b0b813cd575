<?php

namespace App\Http\Requests;

use App\Enums\MessageConfigurationScenarioEnums;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreMessageConfigurationsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $scenarioValues = array_map(fn ($e) => $e->value, MessageConfigurationScenarioEnums::cases());

        return [
            'scenario' => ['required', Rule::in($scenarioValues)],
            'condition' => ['required', 'string'],
            'content' => ['required', 'string'],
            'placeholders' => ['nullable', 'array'],
            'active' => ['boolean'],
        ];
    }
}
