<?php

namespace App\Http\Requests\Distributor\Order;

use App\Enums\OrderDocumentTypeEnum;
use App\Rules\FileMimeTypeRule;
use App\Rules\PdfXssRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UploadDocumentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                new FileMimeTypeRule(['pdf', 'doc', 'docx', 'png', 'jpg', 'jpeg']),
                // new PdfXssRule, TODO: disabled temporarily
            ],
            'type' => ['required', new Enum(OrderDocumentTypeEnum::class)],
            'title' => 'string',
            'details' => 'array',
            'details.message' => 'string',
            'signature_date' => 'date',
            'expiration_date' => 'date',
            'ltp' => 'boolean',
        ];
    }
}
