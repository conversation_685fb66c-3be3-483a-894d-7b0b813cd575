<?php

namespace App\Http\Requests\Distributor\Area;

use App\Models\Distributor;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTerritoryRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('territories', 'name')
                    ->where('organization_id', $this->distributor->id)
                    ->where('organization_type', Distributor::class)
                    ->where('district_id', $this->input('district_id')),
            ],
            'code' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('territories', 'code')
                    ->where('organization_id', $this->distributor->id)
                    ->where('organization_type', Distributor::class),
            ],
            'district_id' => [
                'required',
                'integer',
                Rule::exists('districts', 'id')
                    ->where('organization_id', $this->distributor->id)
                    ->where('organization_type', Distributor::class),
            ],
        ];
    }
}
