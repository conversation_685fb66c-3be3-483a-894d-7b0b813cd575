<?php

namespace App\Http\Resources;

use App\Enums\OrderDocumentTypeEnum;
use App\Http\Resources\RelationAware\BaseJsonResource;
use App\Models\PatientDocument;

class OrderDocumentResource extends BaseJsonResource
{
    public static array $availableRelations = [
        'files' => 'files',
        'uploaded_by' => 'uploadedBy',
        'document_request' => 'documentRequest',
    ];

    public function getRelation(string $relation, $request): mixed
    {
        return match ($relation) {
            'files' => FileResource::collection($this->files),
            'uploaded_by' => UserResource::make($this->uploadedBy),
            'document_request' => DocumentRequestResource::make($this->documentRequest),
        };
    }

    public function toArray($request): array
    {
        /** @var PatientDocument $this */
        $attributes = [
            'id' => $this->id,
            'title' => $this->title,
            'type' => $this->type,
            'details' => $this->details,
            'source' => $this->source,
            'order_id' => $this->order_id,
            'order_document_request_id' => $this->document_request_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'expiration_date' => $this->expiration_date?->toDateString(),
            'global_patient_id' => $this->global_patient_id,
            'files' => FileResource::collection($this->files),
        ];

        if ($this->type === OrderDocumentTypeEnum::CMN) {
            $attributes['signature_date'] = $this->signature_date?->toDateString();
            $attributes['ltp'] = $this->ltp;
        }

        return $this->mergeResource($attributes, $request);
    }
}
