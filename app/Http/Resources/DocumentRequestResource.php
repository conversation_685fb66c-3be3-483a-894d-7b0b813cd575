<?php

namespace App\Http\Resources;

use App\Enums\DocumentRequestTypeEnum;
use App\Http\Resources\Distributor\ProviderUserResource;
use App\Http\Resources\RelationAware\BaseJsonResource;
use App\Models\DocumentRequest;

class DocumentRequestResource extends BaseJsonResource
{
    public static bool $defaultInclude = false;

    public static array $availableRelations = [
        'order' => 'order',
        'patient' => 'patient',
        'provider' => 'provider',
        'provider_user' => 'providerUser',
        'products' => 'products',
        'cms_hcpcs' => 'cmsHcpcs',
        'distributor' => 'distributor',
        'distributor_user' => 'distributorUser',
        'facility' => 'facility',
        'history' => 'history',
        'icd_10s' => 'icd10s',
    ];

    public function getRelation(string $relation, $request): mixed
    {
        /** @var DocumentRequest $model */
        $model = $this->resource;

        return match ($relation) {
            'order' => OrderResource::make($model->order),
            'patient' => PatientResource::make($model->patient),
            'provider' => ProviderResource::make($model->distributor->providerData($this->provider)),
            'provider_user' => ProviderUserResource::make($model->providerUser),
            'products' => !$model->order_id
                ? DocumentRequestProductResource::collection($model->getProducts())
                : OrderProductResource::collection($model->getProducts()),
            'cms_hcpcs' => DocumentRequestCmsHcpcResource::collection($model->cmsHcpcs),
            'distributor' => DistributorResource::make($model->distributor),
            'distributor_user' => UserResource::make($model->distributorUser),
            'facility' => FacilityResource::make($model->facility),
            'history' => DocumentRequestHistoryResource::collection($model->history()->orderByDesc('activity_at')->get()),
            'icd_10s' => ICD10Resource::collection($model->icd10s),
        };
    }

    public function toArray($request): array
    {
        /** @var DocumentRequest $this */
        $attributes = [
            'id' => $this->id,
            'global_id' => $this->getGlobalId(),
            'status' => $this->status,
            'request_type' => $this->request_type,
            'type' => $this->type,
            'label' => $this->label,
            'paused' => $this->paused,
            'date_needed' => $this->date_needed?->toDateString(),
            'follow_up_at' => $this->follow_up_at,
            'details' => $this->details,
            'reason' => $this->reason,
            'is_digital' => $this->is_digital,
            'delivered_by_email' => $this->delivered_by_email,
            'delivered_by_phone' => $this->delivered_by_phone,
            'created_at' => $this->created_at ?? $this->document?->created_at,
            'updated_at' => $this->updated_at ?? $this->document?->updated_at,
        ];

        if ($this->type === DocumentRequestTypeEnum::CHART_NOTES) {
            $attributes['appointment_confirmation_date'] = $this->appointment_confirmation_date?->toDateString();
            $attributes['months_needed'] = $this->months_needed;
        }

        return $this->mergeResource($attributes, $request);
    }
}
