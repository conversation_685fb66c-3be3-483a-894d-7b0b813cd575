<?php

namespace App\Http\Resources;

use App\Http\Resources\RelationAware\BaseJsonResource;
use App\Models\Campaign;

class CampaignResource extends BaseJsonResource
{
    public static array $availableRelations = [
        'manufacturer' => 'manufacturer',
        'distributors' => 'distributors',
    ];

    public function getRelation(string $relation, $request): mixed
    {
        return match ($relation) {
            'manufacturer' => ManufacturerResource::make($this->whenLoaded('manufacturer')),
            'distributors' => DistributorResource::collection($this->distributors),
        };
    }

    public function toArray($request): array
    {
        /** @var Campaign $this */
        $attributes = [
            'id' => $this->id,
            'name' => $this->name,
            'manufacturer_id' => $this->manufacturer_id,
        ];

        return $this->mergeResource($attributes, $request);
    }
}
