<?php

namespace App\Http\Resources\External;

use App\Http\Resources\Distributor\ProviderUserResource;
use App\Http\Resources\LeadProductResource;
use App\Http\Resources\LeadResource as BaseLeadResource;
use App\Http\Resources\UserResource;
use App\Models\Lead;

class LeadResource extends BaseLeadResource
{
    protected const DATE_FORMAT = 'm-d-Y';

    public static array $availableRelations = [
        'assigned_user' => 'assignedUser',
        'distributor_campaign' => 'distributorCampaign',
        'products' => 'products',
        'provider_user' => 'providerUser',
        'custom_fields' => 'customFields',
        'payers' => 'payers',
    ];

    public function getRelation(string $relation, $request): mixed
    {
        /** @var Lead $lead */
        $lead = $this->resource;

        return match ($relation) {
            'assigned_user' => UserResource::make($lead->assignedUser),
            'distributor_campaign' => CampaignResource::make($lead->distributorCampaign),
            'products' => LeadProductResource::collection($lead->products),
            'provider_user' => ProviderUserResource::make($lead->providerUser),
            'custom_fields' => parent::getRelation($relation, $request),
            'payers' => PayerResource::collection($this->payers),
        };
    }

    public function toArray($request): array
    {
        /** @var Lead $lead */
        $lead = $this->resource;
        $parentData = parent::toArray($request);

        // Get the merged resource data first
        $mergedData = $this->mergeResource(array_merge($parentData, [
            'date_of_birth' => $lead->date_of_birth?->format(self::DATE_FORMAT),
            'created_date' => $lead->created_date?->format(self::DATE_FORMAT),
            'qualified_date' => $lead->qualified_date?->format(self::DATE_FORMAT),
            'follow_up_at' => $lead->follow_up_at?->format(self::DATE_FORMAT),
            'canceled_date' => $lead->canceled_date?->format(self::DATE_FORMAT),
            'converted_date' => $lead->converted_date?->format(self::DATE_FORMAT),
            'created_at' => $lead->created_at?->format(self::DATE_FORMAT),
        ]), $request);

        // Always include custom_fields if they exist
        if ($lead->relationLoaded('leadCustomFields') && $lead->leadCustomFields->isNotEmpty()) {
            $mergedData['custom_fields'] = $this->getRelation('custom_fields', $request);
        }

        return $mergedData;
    }
}
