<?php

namespace App\Http\Controllers\API;

use App\Actions\ShuttleHealth\CreateQuicksightUserAction;
use App\Actions\ShuttleHealth\GenerateQuicksightEmbedUrlAction;
use App\Contracts\QuickSightClientInterface;
use App\Enums\QuicksightEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateQuickSightUserByAdminRequest;
use App\Http\Requests\QuicksightEmbedUrlRequest;
use App\Http\Resources\Custom\ArrayResource;
use App\Models\Distributor;
use App\Models\DistributorUser;
use App\Models\QuicksightUser;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class QuicksightController
 *
 * Handles QuickSight embedded analytics API endpoints with intelligent caching
 * and rate-limiting integration for optimal performance and cost control.
 */
class QuicksightController extends Controller
{
    public function __construct(
        private readonly GenerateQuicksightEmbedUrlAction $generateQuicksightEmbedUrlAction,
        private readonly CreateQuicksightUserAction $createQuicksightUserAction,
    ) {
    }

    /**
     * Generate a secure embed URL for QuickSight dashboard
     *
     * This endpoint supports intelligent caching to reduce AWS API costs and improve performance.
     * URLs are cached for a short period and reused when possible, with different rate limiting
     * weights applied to cache hits vs new URL generations.
     *
     * Query Parameters:
     * - dashboard_id: Required. The QuickSight dashboard ID to embed
     * - force_refresh: Optional. Set to 1/true to force generation of a new URL
     */
    public function getEmbedUrl(
        QuicksightEmbedUrlRequest $request,
        Distributor $distributor,
    ): JsonResponse {
        $dashboardId = $request->input('dashboard_id');
        $user = getUser();

        $this->authorize('generate-analytics-embed-url', $distributor);

        // ValidationException is allowed to bubble up naturally
        // QuicksightException will be handled by the global exception handler
        $result = $this->generateQuicksightEmbedUrlAction->execute(
            distributor: $distributor,
            dashboardId: $dashboardId,
            user: $user,
            ipAddress: $request->ip(),
        );

        return ArrayResource::make($result)->response();
    }

    /**
     * Create a new QuickSight user for the distributor
     *
     * This endpoint creates a QuickSight user both in AWS and in the local database.
     * The authenticated user will automatically get a READER role QuickSight account.
     * No additional parameters are required.
     */
    public function createUser(
        Distributor $distributor,
    ): JsonResponse {
        // Check if a user can create QuickSight users for this distributor
        $this->authorize('create-quick-sight-user', $distributor);

        // QuicksightException will be handled by the global exception handler
        // Other exceptions (ValidationException, AuthorizationException) will bubble up naturally
        $result = $this->createQuicksightUserAction->execute(
            distributor: $distributor,
        );

        return ArrayResource::make($result['quicksight_user'])
            ->additional([
                'message' => 'QuickSight user created successfully',
                'status' => 'success',
                'aws_user_id' => $result['aws_response']['User']['UserId'] ?? null,
            ])
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    /**
     * Create a new QuickSight user for the distributor by admin
     *
     * This endpoint creates a QuickSight user both in AWS and in the local database.
     * Email and user_name are required.
     * It also checks for the user in the Database with the given email and then gets the organization owner and creates the user in QuickSight.
     *
     * @param CreateQuickSightUserByAdminRequest $request
     * @param QuickSightClientInterface $quickSightClient
     * @return JsonResponse
     */
    public function createQuickSightUserByAdmin(
        CreateQuickSightUserByAdminRequest $request,
        QuickSightClientInterface $quickSightClient,
    ) {
        $user = User::where('email', $request->input('email'))->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }

        // If the user is a DistributorUser, get their distributor and its owner
        $distributor = $user->distributors()->first();
        $organizationOwner = $distributor?->owner;

        if (!$distributor) {
            return response()->json(['error' => 'No distributor found for user'], 400);
        }

        $awsParameters = [
            'AwsAccountId' => $quickSightClient->getAwsAccountId(),
            'Namespace' => QuicksightEnum::QUICK_SIGHT_NAMESPACE->value,
            'IdentityType' => QuicksightEnum::QUICK_SIGHT_IDENTITY_TYPE->value,
            'UserName' => $request->input('user_name'),
            'Email' => $request->input('email'),
            'UserRole' => QuicksightEnum::READER->value,
            'Tags' => [
                [
                    'Key' => 'role_id',
                    'Value' => strtolower(QuicksightEnum::READER->value),
                ],
            ],
        ];

        $awsResult = $quickSightClient->registerUser($awsParameters);

        // Extract user ARN from AWS response
        $userArn = $awsResult['User']['Arn'] ?? null;
        $quicksightUser = QuicksightUser::create([
            'distributor_id' => $organizationOwner->id,
            'user_id' => $user->id,
            'arn' => $userArn,
            'username' => $request->input('user_name'),
            'role' => QuicksightEnum::READER->value,
            'metadata' => [
                'email' => $request->input('email'),
                'namespace' => QuicksightEnum::QUICK_SIGHT_NAMESPACE->value,
                'aws_user_id' => $awsResult['User']['UserId'] ?? null,
                'created_via_api' => true,
            ],
            'is_active' => true,
        ]);

        $result = [
            'quicksight_user' => $quicksightUser->only(['id', 'distributor_id', 'user_id', 'username', 'role', 'is_active']),
            'aws_response' => $awsResult,
        ];

        return ArrayResource::make($result['quicksight_user'])
            ->additional([
                'message' => 'QuickSight user created successfully',
                'status' => 'success',
                'aws_user_id' => $result['aws_response']['User']['UserId'] ?? null,
            ])
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }
}
