<?php

namespace App\Http\Controllers\External\Manufacturer;

use App\Http\Requests\Manufacturer\UpdateManufacturerRequest;
use App\Http\Resources\External\ManufacturerResource;

/**
 * Class MeController
 * Controller for managing manufacturer organization data
 */
class MeController extends AbstractController
{
    public function show(): ManufacturerResource
    {
        return ManufacturerResource::make($this->getManufacturer());
    }

    public function update(UpdateManufacturerRequest $request): ManufacturerResource
    {
        $manufacturer = $this->getManufacturer();

        $manufacturer->update($request->validated());

        return ManufacturerResource::make($manufacturer);
    }

}
