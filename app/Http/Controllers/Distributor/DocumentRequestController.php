<?php

namespace App\Http\Controllers\Distributor;

use App\Actions\ShuttleHealth\CancelOrDeclineDocumentRequest;
use App\Actions\ShuttleHealth\CreateDocumentRequest;
use App\Actions\ShuttleHealth\FindOrCreateFacilityForProviderUser;
use App\Actions\ShuttleHealth\GetOrCreateProviderUserFromNpi;
use App\Actions\ShuttleHealth\MakeCmnPDF;
use App\Actions\ShuttleHealth\MakeDocumentRequestPDF;
use App\Actions\ShuttleHealth\RequestAobDocumentAgain;
use App\Actions\ShuttleHealth\SendAnalogRequest;
use App\Actions\ShuttleHealth\SendDigitalRequest;
use App\Actions\ShuttleHealth\UpdateOrderTaskAction;
use App\Enums\DocumentRequestCancellationReasonsEnum;
use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\OrderTaskStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Distributor\Order\AssignUserRequest;
use App\Http\Requests\Distributor\Order\CreateDocumentRequestRequest;
use App\Http\Requests\Distributor\RequestDocumentationAgainRequest;
use App\Http\Requests\Distributor\UpdateDocumentRequestRequest;
use App\Http\Requests\Shared\CancelDocumentRequestRequest;
use App\Http\Requests\Shared\Filters\FilterDocumentRequestRequest;
use App\Http\Resources\DocumentRequestResource;
use App\Models\Distributor;
use App\Models\DocumentRequest;
use App\Models\Pivot\DocumentRequestProductPivot;
use App\Models\Product;
use App\Models\Provider;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

/**
 * Class DocumentRequestController
 *
 * Controller for managing document requests.
 */
class DocumentRequestController extends Controller
{
    public function index(FilterDocumentRequestRequest $request, Distributor $distributor): AnonymousResourceCollection
    {
        $this->authorize('read-document-request', $distributor);

        $relations = DocumentRequestResource::getEagerLoadRelations($request);
        $perPage = $request->input('per_page', perPage());
        $query = $distributor->documentRequests()
            ->with($relations)
            ->filter($request)
            ->orderBy('date_needed');

        // Conditionally apply pagination
        if ($perPage === -1) {
            $documentRequests = $query->get();
        } else {
            $documentRequests = $query->paginate($perPage)->withQueryString();
        }

        return DocumentRequestResource::collection($documentRequests);
    }

    public function show(Distributor $distributor, $documentRequestId)
    {
        $this->authorize('read-document-request', [$distributor, $documentRequestId]);

        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        return DocumentRequestResource::make($documentRequest);
    }

    public function showSimilarRequests(Distributor $distributor, $documentRequestId): AnonymousResourceCollection
    {
        $this->authorize('read-document-request', [$distributor, $documentRequestId]);

        /** @var DocumentRequest $documentRequest */
        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        abort_if(
            $documentRequest->request_type !== DocumentRequestRequestTypeEnum::PRESCRIPTION_RENEWAL,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'This is available only for renewal requests.',
        );

        return DocumentRequestResource::collection($documentRequest->getSimilarDocumentRequestsFromNewPrescriptionOrders());
    }

    public function store(
        CreateDocumentRequestRequest $request,
        Distributor $distributor,
        GetOrCreateProviderUserFromNpi $getOrCreateProviderUserFromNpi,
        CreateDocumentRequest $createDocumentRequestAction,
        SendDigitalRequest $sendDigitalRequestAction,
        SendAnalogRequest $sendAnalogRequestAction,
    ): DocumentRequestResource {
        $this->authorize('write-document-request', $distributor);

        $parsed = splitRequestIntoParts($request->safe(), ['fax_number', 'provider_user_npi', 'products']);

        abort_if(
            empty($distributor->fax),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.distributor.has_no_fax_number'),
        );

        // handle provider user make sure it
        $providerUser = $getOrCreateProviderUserFromNpi->execute($parsed['provider_user_npi']);
        abort_if(empty($providerUser), Response::HTTP_UNPROCESSABLE_ENTITY, __('errors.npi_record.not_found'));

        // handle provider
        if (!empty($parsed['main']['provider_id'])) {
            /** @var Provider $provider */
            $provider = Provider::query()->findOrFail($parsed['main']['provider_id']);

            abort_if(
                $provider->users()->where('users.id', $providerUser->id)->doesntExist(),
                Response::HTTP_UNPROCESSABLE_ENTITY,
                __('errors.provider_user.does_not_belong_to_provider'),
            );
        }

        // handle facility, make sure it belongs to providerUser
        if (!empty($parsed['main']['facility_id'])) {
            $facilityExists = $providerUser->facilities()
                ->where('facilities.id', $parsed['main']['facility_id'])
                ->where('distributor_id', $distributor->id)
                ->exists();

            abort_if(
                !$facilityExists,
                Response::HTTP_UNPROCESSABLE_ENTITY,
                __('errors.facility.does_not_belong_to_provider_user'),
            );
        }

        $documentRequestData = $parsed['main'];
        $documentRequestData['follow_up_at'] = Carbon::now()->addDay();

        $documentRequest = $createDocumentRequestAction->execute(
            distributor: $distributor,
            user: getUser(),
            requestType: DocumentRequestRequestTypeEnum::ON_DEMAND,
            documentRequestData: $documentRequestData,
            providerUser: $providerUser,
            products: $parsed['products'],
        );

        if ($documentRequest->is_digital) {
            $sendDigitalRequestAction->execute(
                documentRequest: $documentRequest,
                user: getUser(),
                details: $parsed['main']['details'] ?? null,
            );
        } else {
            $sendAnalogRequestAction->execute(
                documentRequest: $documentRequest,
                user: getUser(),
                faxNumber: $parsed['fax_number'],
                details: $parsed['main']['details'] ?? null,
            );
        }

        return DocumentRequestResource::make($documentRequest);
    }

    public function preview(
        CreateDocumentRequestRequest $request,
        Distributor $distributor,
        GetOrCreateProviderUserFromNpi $getOrCreateProviderUserFromNpi,
        FindOrCreateFacilityForProviderUser $findOrCreateFacilityForProviderUser,
        MakeDocumentRequestPDF $makeDocumentRequestPdfAction,
        MakeCmnPDF $makeCmnPDFAction,
    ): Response {
        $this->authorize('write-document-request', $distributor);

        $parsed = splitRequestIntoParts($request->safe(), ['fax_number', 'provider_user_npi', 'products']);

        abort_if(
            empty($distributor->fax),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.distributor.has_no_fax_number'),
        );

        // handle provider user make sure it
        $providerUser = $getOrCreateProviderUserFromNpi->execute($parsed['provider_user_npi']);
        abort_if(empty($providerUser), Response::HTTP_UNPROCESSABLE_ENTITY, __('errors.npi_record.not_found'));

        // handle provider
        $provider = null;

        if (!empty($parsed['main']['provider_id'])) {
            /** @var Provider $provider */
            $provider = Provider::query()->findOrFail($parsed['main']['provider_id']);

            abort_if(
                $provider->users()->where('users.id', $providerUser->id)->doesntExist(),
                Response::HTTP_UNPROCESSABLE_ENTITY,
                __('errors.provider_user.does_not_belong_to_provider'),
            );
        }

        // handle facility, make sure it belongs to providerUser
        $facility = null;

        if (!empty($parsed['main']['facility_id'])) {
            $facility = $providerUser->facilities()
                ->where('facilities.id', $parsed['main']['facility_id'])
                ->where('distributor_id', $distributor->id)
                ->first();

            abort_if(
                !$facility,
                Response::HTTP_UNPROCESSABLE_ENTITY,
                __('errors.facility.does_not_belong_to_provider_user'),
            );
        }

        if (!$provider && !$facility) {
            $facility = $findOrCreateFacilityForProviderUser->execute($providerUser, $distributor->id);
        }

        $patient = $distributor->patients()->findOrFail($parsed['main']['patient_id']);

        // we need document request object to generate pdf
        if ($parsed['main']['type'] === DocumentRequestTypeEnum::AOB->value) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'AOB document request is not supported for preview.');
        } elseif ($parsed['main']['type'] === DocumentRequestTypeEnum::CMN->value) {
            $brandedProducts = $parsed['main']['details']['cmn']['branded'] ?? true;
            $products = $this->makeDocumentRequestProductPivots($parsed['products']);

            $pdf = $makeCmnPDFAction->execute(
                distributor: $distributor,
                providerUser: $providerUser,
                patient: $patient,
                products: $products,
                provider: $provider,
                facility: $facility,
                brandedProducts: $brandedProducts,
            );
        } else {
            $documentRequest = $distributor->documentRequests()->make($parsed['main']);
            $pdf = $makeDocumentRequestPdfAction->execute(
                distributor: $distributor,
                providerUser: $providerUser,
                patient: $patient,
                documentRequests: [$documentRequest],
                provider: $provider,
                facility: $facility,
            );
        }

        return new Response(base64_encode($pdf->output()));
    }

    private function makeDocumentRequestProductPivots(array $productsData): Collection
    {
        return collect($productsData)->map(function (array $productData) {
            $product = Product::findOrFail($productData['id']);

            $pivot = DocumentRequestProductPivot::make($productData);

            $product->pivot = $pivot;

            return $product;
        });
    }

    public function update(
        UpdateDocumentRequestRequest $request,
        Distributor $distributor,
        int $documentRequestId,
    ): DocumentRequestResource {
        $this->authorize('write-document-request', [$distributor, $documentRequestId]);

        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        $documentRequest->update($request->validated());

        return DocumentRequestResource::make($documentRequest);
    }

    public function counts(FilterDocumentRequestRequest $request, Distributor $distributor): JsonResponse
    {
        $this->authorize('read-document-request', $distributor);

        $count = $distributor->documentRequests()->filter($request)->count();

        return response()->json(['count' => $count]);
    }

    public function requestAgain(
        RequestDocumentationAgainRequest $request,
        Distributor $distributor,
        int $documentRequestId,
        SendAnalogRequest $sendAnalogRequestAction,
        SendDigitalRequest $sendDigitalRequestAction,
        RequestAobDocumentAgain $requestAobAgainAction,
        UpdateOrderTaskAction $updateOrderTaskAction,
    ): DocumentRequestResource {
        $this->authorize('write-document-request', [$distributor, $documentRequestId]);

        /** @var DocumentRequest $documentRequest */
        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        if ($documentRequest->type === DocumentRequestTypeEnum::AOB) {
            $requestAobAgainAction->execute($documentRequest, getUser());
        } else {
            [$faxNumber, $details] = $this->getDataFromPayloadAndPreviousRequest($request, $documentRequest);

            if ($documentRequest->is_digital) {
                $sendDigitalRequestAction->execute(
                    documentRequest: $documentRequest,
                    user: getUser(),
                    details: $details,
                );
            } else {
                $sendAnalogRequestAction->execute(
                    documentRequest: $documentRequest,
                    user: getUser(),
                    faxNumber: $faxNumber,
                    details: $details,
                );
            }
        }

        $orderTasks = $documentRequest->orderTasks;

        foreach ($orderTasks as $orderTask) {
            $metadata = $orderTask->metadata ?? [];

            $metadata['initial_request_date'] = Carbon::now()->format('Y-m-d');
            $metadata['complete_date'] = null;

            $taskData = array(
                'status' => OrderTaskStatusEnum::IN_PROGRESS->value,
                'metadata' => $metadata,
            );

            $updateOrderTaskAction->execute($orderTask, $taskData);
        }

        return DocumentRequestResource::make($documentRequest);
    }

    private function getDataFromPayloadAndPreviousRequest(
        RequestDocumentationAgainRequest $request,
        DocumentRequest $documentRequest,
    ): array {
        $faxNumber = $request->validated('fax_number');
        $details = $request->validated('details');

        if (empty($faxNumber)) {
            $lastAnalogRecord = $documentRequest->history()
                ->where('type', DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG)
                ->latest('activity_at')
                ->first();

            $faxNumber = $lastAnalogRecord?->fax?->to;

            abort_if(
                empty($faxNumber),
                Response::HTTP_UNPROCESSABLE_ENTITY,
                __('errors.document_request.fax_number_empty'),
            );
        }

        $lastRecord = $documentRequest->history()
            ->whereIn('type', [DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG, DocumentRequestHistoryTypeEnum::REQUESTED_DIGITAL])
            ->latest('activity_at')
            ->first();

        if (empty($details) && $lastRecord) {
            $details = $lastRecord->details;
        }

        return [$faxNumber, $details];
    }

    public function assignUser(
        AssignUserRequest $request,
        Distributor $distributor,
        int $documentRequestId,
    ): DocumentRequestResource {
        $this->authorize('write-document-request', [$distributor, $documentRequestId]);

        /** @var DocumentRequest $documentRequest */
        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        $userId = $request->validated('user_id');

        abort_unless(
            $distributor->users()->where('users.id', $userId)->exists(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.distributor_user.does_not_belong_to_distributor'),
        );

        $documentRequest->update(['distributor_user_id' => $userId]);

        return DocumentRequestResource::make($documentRequest);
    }

    public function unassignUser(Distributor $distributor, int $documentRequestId): DocumentRequestResource
    {
        $this->authorize('write-document-request', [$distributor, $documentRequestId]);

        /** @var DocumentRequest $documentRequest */
        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        $documentRequest->update(['distributor_user_id' => null]);

        return DocumentRequestResource::make($documentRequest);
    }

    public function cancel(
        CancelDocumentRequestRequest $request,
        Distributor $distributor,
        int $documentRequestId,
        CancelOrDeclineDocumentRequest $cancelOrDeclineDocumentRequestAction,
    ): DocumentRequestResource {
        $this->authorize('write-document-request', [$distributor, $documentRequestId]);

        /** @var DocumentRequest $documentRequest */
        $documentRequest = $distributor->documentRequests()->findOrFail($documentRequestId);

        if ($documentRequest->order) {
            $this->authorize('read-order', [$distributor, $documentRequest->order]);
            $this->authorize('write-document-request', [$documentRequest->order, $documentRequest]);
        }

        $cancelOrDeclineDocumentRequestAction->execute(
            $documentRequest,
            getUser(),
            DocumentRequestCancellationReasonsEnum::from($request->validated('reason')),
            $request->validated('details'),
        );

        return DocumentRequestResource::make($documentRequest);
    }
}
