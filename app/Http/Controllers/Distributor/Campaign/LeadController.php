<?php

namespace App\Http\Controllers\Distributor\Campaign;

use App\Actions\ShuttleHealth\AdvanceLeadImportTemplate;
use App\Actions\ShuttleHealth\ConvertLeadToOrderAction;
use App\Actions\ShuttleHealth\CreateLeadCustomFieldsAction;
use App\Actions\ShuttleHealth\DeletePhysicianFromLeadAction;
use App\Actions\ShuttleHealth\DownloadLeadsTemplateAction;
use App\Actions\ShuttleHealth\GetOrCreateProviderUserFromNpi;
use App\Actions\ShuttleHealth\HandleLeadCampaignChangeAction;
use App\Actions\ShuttleHealth\StandardLeadImportTemplate;
use App\Actions\ShuttleHealth\ValidateLeadStatusChangeAction;
use App\Enums\ActivityLogTypeEnum;
use App\Enums\LeadImportTemplateTypeEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\MessageTemplateStatusEnum;
use App\Enums\MessageTemplateTypeEnum;
use App\Exceptions\LeadConvertException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Distributor\AssignProviderUserFromNpiRequest;
use App\Http\Requests\Distributor\Campaign\CreateLeadRequest;
use App\Http\Requests\Distributor\Campaign\UpdateLeadRequest;
use App\Http\Requests\Shared\Filters\FilterLeadRequest;
use App\Http\Resources\LeadResource;
use App\Http\Resources\OrderResource;
use App\Imports\LeadsImport;
use App\Models\Distributor;
use App\Models\DistributorCampaign;
use App\Models\Lead;
use App\Models\UnsubscribeToEmails;
use App\Notifications\Sms\Sms;
use App\Rules\FileMimeTypeRule;
use App\Services\ShuttleHealth\ActivityLogger;
use App\Services\ShuttleHealth\LeadAssignmentService;
use App\Traits\HasOrganizationCommunicationWindowOpen;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * Class LeadController
 *
 * Controller for managing leads.
 */
class LeadController extends Controller
{
    use HasOrganizationCommunicationWindowOpen;
    public function __construct(
        private readonly CreateLeadCustomFieldsAction $createLeadCustomFieldsAction,
        private readonly HandleLeadCampaignChangeAction $handleLeadCampaignChangeAction,
        private readonly LeadAssignmentService $leadAssignmentService,
        private readonly ValidateLeadStatusChangeAction $validateLeadStatusChangeAction,
    ) {
    }

    public function index(FilterLeadRequest $request, Distributor $distributor, DistributorCampaign $campaign): AnonymousResourceCollection
    {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign]);

        $relations = LeadResource::getEagerLoadRelations($request);

        return LeadResource::collection(
            $campaign->leads()
                ->with($relations)
                ->filter($request)
                ->paginate(perPage())->withQueryString(),
        );
    }

    public function show(Distributor $distributor, DistributorCampaign $campaign, Lead $lead): LeadResource
    {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign, $lead]);

        return LeadResource::make($lead);
    }

    public function store(CreateLeadRequest $request, Distributor $distributor, DistributorCampaign $campaign): LeadResource
    {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign]);
        $lead = Lead::query()->create([
            'distributor_campaign_id' => $campaign->id,
            'first_name' => $request->input('first_name'),
            'last_name' => $request->input('last_name'),
            'phone_cell' => $request->input('phone_cell'),
            'sms_enabled' => $request->boolean('sms_enabled'),
            'email_enabled' => $request->boolean('email_enabled'),
            'call_enabled' => $request->boolean('call_enabled'),
            'communications_text' => $request->input('communications_text'),
            'created_date' => todayDateTimeTz(),
            'follow_up_at' => todayTz(),
            'source_editable' => true,
            'status' => LeadStatusEnum::OPEN,
            'email' => $request->input('email'),
            'created_by' => getUser()->id,
            'gclid' => $request->input('gclid'),
            'fbclid' => $request->input('fbclid'),
            'msclkid' => $request->input('msclkid'),
            'utm_source' => $request->input('utm_source'),
            'utm_medium' => $request->input('utm_medium'),
            'utm_campaign' => $request->input('utm_campaign'),
            'utm_term' => $request->input('utm_term'),
            'utm_content' => $request->input('utm_content'),
            'timezone' => $request->input('timezone'),
        ]);

        // Handle custom fields
        if ($request->has('custom_fields')) {
            $this->createLeadCustomFieldsAction->execute($lead, $request->input('custom_fields'));
        }

        return LeadResource::make($lead);
    }

    public function update(UpdateLeadRequest $request, Distributor $distributor, DistributorCampaign $campaign, Lead $lead): LeadResource
    {
        // $this->authorize('read-write-lead-relation', [$distributor, $campaign, $lead]);

        $validated = $request->validated();

        // Track assignment changes for logging
        $oldAssignedUserId = $lead->assigned_user_id;
        $newAssignedUserId = null;
        $wasAssignedUserManuallyChanged = false;

        // Handle assigned_user_id validation and change tracking
        if ($request->has('assigned_user_id')) {
            $newAssignedUserId = $request->input('assigned_user_id');
            $wasAssignedUserManuallyChanged = true;

            // Validate the assigned user exists in distributor if not null
            if ($newAssignedUserId !== null) {
                $distributor->users()->findOrFail($newAssignedUserId);
            }
        }

        if ($request->has('canceled_reason')) {
            if (!$request->has('canceled_date')) {
                $validated['canceled_date'] = todayTz();
            }
        }

        if (!empty($validated['status']) && $validated['status'] === LeadStatusEnum::OPEN->value) {
            $validated['canceled_date'] = null;
            $validated['canceled_reason'] = null;
        }

        // Validate status change and handle first contact date
        $statusChanged = false;

        if ($request->has('status')) {
            $newStatus = LeadStatusEnum::from($validated['status']);
            $newCampaignId = $request->has('distributor_campaign_id') ? $validated['distributor_campaign_id'] : null;
            $this->validateLeadStatusChangeAction->execute($lead, $newStatus, $newCampaignId);

            $statusChanged = $lead->status != $newStatus;

            // Handle first contact date for status changes
            if ($statusChanged) {
                $this->leadAssignmentService->handleFirstContactDate($lead, $newStatus->value, [], true, $wasAssignedUserManuallyChanged);
            }
        }

        // Handle campaign change before updating lead
        $campaignChanged = false;

        if ($request->has('distributor_campaign_id') && $lead->distributor_campaign_id !== $validated['distributor_campaign_id']) {
            $this->handleLeadCampaignChangeAction->execute($lead, $validated['distributor_campaign_id']);
            $campaignChanged = true;
        }

        // Get the fields that are being updated for first contact date logging
        $originalData = $lead->getOriginal();
        $updatedFields = array_filter($validated, function ($value, $key) use ($originalData) {
            return array_key_exists($key, $originalData) && $originalData[$key] != $value;
        }, ARRAY_FILTER_USE_BOTH);

        $lead->update($validated);

        // Handle the first contact date for any field update (if not already set)
        if (!empty($updatedFields) && !$statusChanged) {
            $this->leadAssignmentService->handleFirstContactDate($lead, null, $updatedFields, true, $wasAssignedUserManuallyChanged);
        }

        // Handle custom fields (only if a campaign didn't change, as they would have been handled above)
        if ($request->has('custom_fields') && !$campaignChanged) {
            $this->createLeadCustomFieldsAction->execute($lead, $request->input('custom_fields'));
        }

        if ($request->has('address') && is_array($request->input('address'))) {
            foreach ($request->address as $addressData) {
                $isUpdate = isset($addressData['id']);

                $lead->addresses()->updateOrCreate(
                    $isUpdate
                        ? ['id' => $addressData['id']]
                        : [
                            'address_line_1' => $addressData['address_line_1'],
                            'address_line_2' => $addressData['address_line_2'] ?? null,
                            'city' => $addressData['city'],
                            'state' => $addressData['state'],
                            'zip' => $addressData['zip'],
                            'type' => $addressData['type'],
                        ],
                    [
                        'address_line_1' => $addressData['address_line_1'],
                        'address_line_2' => $addressData['address_line_2'] ?? null,
                        'city' => $addressData['city'],
                        'state' => $addressData['state'],
                        'zip' => $addressData['zip'],
                        // Don't include 'type' in update payload
                    ],
                );
            }
        }

        if ($request->has('is_unsubscribed')) {
            if (
                $request->has('email_enabled')
                && $request->boolean('email_enabled') === true
                && $request->boolean('is_unsubscribed') === false
                && !empty($lead->email)
            ) {
                UnsubscribeToEmails::updateOrCreate(
                    ['email' => $lead->email],
                    [
                        'unsubscribed' => false,
                        'ip_address' => $request->ip(),
                        'user_agent' => $request->header('User-Agent'),
                    ],
                );
            }
        }

        // Log assigned user changes if manually modified
        if ($wasAssignedUserManuallyChanged) {
            $this->leadAssignmentService->logAssignedUserChange($lead, $oldAssignedUserId, $newAssignedUserId);
        }

        $status = $request->input('status');

        // Handle assignment logic based on status and manual changes
        if (in_array($status, [LeadStatusEnum::CANCELED->value, LeadStatusEnum::CONVERTED->value])) {
            // Only reassign on status change if the user didn't manually set assigned_user_id
            if (!$wasAssignedUserManuallyChanged) {
                $this->leadAssignmentService->reassignOnStatusChange($lead, getUser()->id, $status);
            }
        } else {
            $lead->refresh();

            // Only auto-assign if the user didn't manually set assigned_user_id
            if ($this->leadAssignmentService->shouldAutoAssign($lead, $wasAssignedUserManuallyChanged)) {
                $this->leadAssignmentService->assignIfFirstActivity($lead, getUser()->id);
            }

            // Handle first contact date and auto-assignment for first activity if no manual assignment was made
            // This covers cases where first contact date wasn't set yet but assignment logic above didn't apply
            if (!$wasAssignedUserManuallyChanged) {
                $this->leadAssignmentService->handleFirstContactDate($lead, null, [], true, $wasAssignedUserManuallyChanged);
            }
        }

        return LeadResource::make($lead);
    }

    public function counts(FilterLeadRequest $request, Distributor $distributor, DistributorCampaign $campaign): JsonResponse
    {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign]);

        $count = $campaign->leads()->filter($request)->count();

        return response()->json(['count' => $count]);
    }

    public function assignProviderUserFromNpi(
        AssignProviderUserFromNpiRequest $request,
        Distributor $distributor,
        DistributorCampaign $campaign,
        Lead $lead,
        GetOrCreateProviderUserFromNpi $setProviderUserFromNpiToOrder,
        DeletePhysicianFromLeadAction $deletePhysicianFromLeadAction,
    ): LeadResource {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign, $lead]);

        $npi = $request->validated('provider_user_npi');
        $facilityId = $request->validated('facility_id');

        if (empty($npi)) {
            // Clear provider from lead
            $deletePhysicianFromLeadAction->execute($lead);

            return LeadResource::make($lead);
        }

        $providerUser = $setProviderUserFromNpiToOrder->execute($npi);

        abort_if(empty($providerUser), Response::HTTP_UNPROCESSABLE_ENTITY, __('errors.npi_record.not_found'));

        $distributor->providerUsers()->syncWithoutDetaching($providerUser);
        $currentProviderUserName = $lead->providerUser?->name;
        $lead->update(['provider_user_id' => $providerUser->id, 'facility_id' => $facilityId]);
        ActivityLogger::write($lead, ActivityLogTypeEnum::MANAGE_LEAD_PHYSICIAN, [
            'from' => $currentProviderUserName,
            'to' => $providerUser->name,
        ]);

        // Handle first contact date and auto-assignment for first activity
        $this->leadAssignmentService->handleFirstContactDate($lead, null, [], true, false);

        return LeadResource::make($lead);
    }

    public function convertToOrder(
        FormRequest $request,
        Distributor $distributor,
        DistributorCampaign $campaign,
        Lead $lead,
        ConvertLeadToOrderAction $convertLeadToOrder,
    ): OrderResource {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign, $lead]);

        $validated = $request->validate([
            'patient_id' => 'filled|integer|exists:patients,id',
            'copy_to_patient' => 'filled|boolean',
            'create_bt_patient' => 'filled|boolean',
            'selected_facility' => 'nullable|array',
            'facility_id' => 'nullable|integer|exists:facilities,id',
        ]);

        abort_if(
            $lead->patient_id || $lead->converted_date || $lead->status === LeadStatusEnum::CONVERTED,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'Lead is converted',
        );
        try {
            $order = $convertLeadToOrder->execute($distributor, $lead, $validated);
            $this->leadAssignmentService->reassignOnStatusChange($lead, getUser()->id, LeadStatusEnum::CONVERTED->value);
        } catch (LeadConvertException $e) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $e->getMessage());
        }

        return OrderResource::make($order);
    }

    public function notify(
        FormRequest $request,
        Distributor $distributor,
        DistributorCampaign $campaign,
        Lead $lead,
    ): Response {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign, $lead]);

        $validated = $request->validate([
            'template_id' => [
                'required',
                'integer',
                Rule::exists('message_templates', 'id')
                    ->where('distributor_campaign_id', $campaign->id)
                    ->where('is_active', true)
                    ->where('status', MessageTemplateStatusEnum::APPROVED)
                    ->where('type', MessageTemplateTypeEnum::AD_HOC),
            ],
        ]);

        $template = $campaign->messageTemplates()->find($validated['template_id']);

        // Get lead's timezone for frontend warning (but send real-time regardless)
        $leadTimezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');
        $isOutsideWindow = !$this->isCommunicationWindowOpen($distributor, $leadTimezone);

        // Send the message REAL-TIME regardless of communication window
        // Ad hoc messages should always be sent immediately
        $lead->notify(new Sms($template->body, $template->phone_number, getUser()->id, null, true));

        // Handle first contact date and auto-assignment for first activity
        $this->leadAssignmentService->handleFirstContactDate($lead, null, [], true, false);

        // Log if sent during quiet hours for audit purposes
        if ($isOutsideWindow) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::MESSAGE, [
                'template_id' => $template->id,
                'template_name' => $template->name,
                'sent_during_quiet_hours' => true,
                'lead_timezone' => $leadTimezone,
                'user_id' => getUser()->id,
                'message_type' => 'ad_hoc_realtime',
            ]);
        }

        return response()->noContent();
    }

    /**
     * Check if sending message to lead is within communication window for frontend warning
     */
    public function checkCommunicationWindow(
        Distributor $distributor,
        DistributorCampaign $campaign,
        Lead $lead,
    ): JsonResponse {
        $this->authorize('readCampaign', [$distributor, $campaign, $lead]);

        $leadTimezone = $lead->timezone?->value ?? $lead->timezone ?? config('app.sh_timezone');
        $isWithinWindow = $this->isCommunicationWindowOpen($distributor, $leadTimezone);

        return response()->json([
            'data' => [
                'is_within_window' => $isWithinWindow,
                'lead_timezone' => $leadTimezone,
                'message' => $isWithinWindow
                    ? 'Message will be sent immediately.'
                    : 'Warning: You are about to send an SMS during lead\'s quiet hours.',
            ],
        ]);
    }

    public function import(Request $request, Distributor $distributor, DistributorCampaign $campaign): Response
    {
        $this->authorize('read-write-lead-relation', [$distributor, $campaign]);

        $request->validate([
            'file' => ['required', 'file', new FileMimeTypeRule(['csv'])],
        ]);

        $file = $request->file('file');
        $import = new LeadsImport($campaign);
        Excel::import($import, $file);

        if ($import->failures()->isNotEmpty()) {
            return response(
                [
                    'message' => 'There were errors in your import.',
                    'errors' => $import->getFormattedFailures(),
                ],
                422,
                ['Content-Type' => 'application/json'],
            );
        }

        return response()->noContent();
    }

    public function downloadTemplate(
        Request $request,
        Distributor $distributor,
        DistributorCampaign $campaign,
        LeadImportTemplateTypeEnum $type,
        StandardLeadImportTemplate $standardLeadImportTemplate,
        AdvanceLeadImportTemplate $advanceLeadImportTemplate,
        DownloadLeadsTemplateAction $downloadLeadsTemplateAction,
    ): BinaryFileResponse {
        $this->authorize('read', $distributor);

        return $downloadLeadsTemplateAction->execute(
            $request,
            $distributor,
            $campaign,
            $type,
            $standardLeadImportTemplate,
            $advanceLeadImportTemplate,
        );
    }
}
