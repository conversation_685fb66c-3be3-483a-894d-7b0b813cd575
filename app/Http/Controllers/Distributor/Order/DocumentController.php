<?php

namespace App\Http\Controllers\Distributor\Order;

use App\Actions\ShuttleHealth\AttachDetachDocumentToOrderAction;
use App\Actions\ShuttleHealth\CreateDocumentsFromFax;
use App\Actions\ShuttleHealth\DestroyOrderDocument;
use App\Actions\ShuttleHealth\UploadDocument;
use App\Contracts\FileStorageServiceInterface;
use App\Enums\ActivityLogTypeEnum;
use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Distributor\DeleteDocumentRequest;
use App\Http\Requests\Distributor\Order\CreateDocumentsFromFaxRequest;
use App\Http\Requests\Distributor\Order\UploadDocumentRequest;
use App\Http\Requests\Shared\Filters\FilterDocumentRequest;
use App\Http\Resources\OrderDocumentResource;
use App\Models\Distributor;
use App\Models\Order;
use App\Models\PatientDocument;
use App\Services\ShuttleHealth\ActivityLogger;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

/**
 * Class DocumentController
 *
 * Controller for managing documents in orders.
 */
class DocumentController extends Controller
{
    public function index(
        FilterDocumentRequest $request,
        Distributor $distributor,
        int $orderId,
    ): AnonymousResourceCollection {
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);

        $this->authorize('read-document', $order);

        $relations = OrderDocumentResource::getEagerLoadRelations($request);

        $documents = $order->documents()
            ->with($relations)
            ->filter($request)
            ->paginate(perPage())
            ->withQueryString();

        ActivityLogger::write(
            parent: $order,
            type: ActivityLogTypeEnum::DOCUMENT_READ,
        );

        return OrderDocumentResource::collection($documents);
    }

    public function show(Distributor $distributor, int $orderId, int $documentId): OrderDocumentResource
    {
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);

        $this->authorize('read-document', $order);

        ActivityLogger::write(
            parent: $order,
            type: ActivityLogTypeEnum::DOCUMENT_READ,
        );

        return OrderDocumentResource::make($order->documents()->with('files')->findOrFail($documentId));
    }

    public function upload(
        UploadDocumentRequest $request,
        Distributor $distributor,
        int $orderId,
        UploadDocument $uploadDocumentAction,
    ): OrderDocumentResource {
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);

        $this->authorize('upload-order-document', [$distributor, $order]);

        $orderDocumentRequest = $order->documentRequests()->where('type', $request->validated('type'))->first();
        $signatureDate = !empty($request->validated('signature_date')) ? Carbon::parse($request->validated('signature_date')) : null;
        $expirationDate = !empty($request->validated('expiration_date')) ? Carbon::parse($request->validated('expiration_date')) : null;

        $type = OrderDocumentTypeEnum::from($request->validated('type'));

        $result = $uploadDocumentAction->execute(
            file: $request->file('file'),
            user: getUser(),
            type: $type,
            source: OrderDocumentSourceEnum::DISTRIBUTOR_USER,
            order: $order,
            documentRequest: $orderDocumentRequest,
            title: $request->validated('title'),
            details: $request->validated('details'),
            signatureDate: $signatureDate,
            ltp: $request->validated('ltp') ?? false,
            expirationDate: $expirationDate,
            patient: $order?->patient,
        );

        return OrderDocumentResource::make($result['orderDocument']);
    }

    public function createFromFax(
        CreateDocumentsFromFaxRequest $request,
        Distributor $distributor,
        int $orderId,
        CreateDocumentsFromFax $createDocumentsFromFaxAction,
    ): Response {
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);

        $this->authorize('write-document', $order);

        $fax = $distributor->faxes()->findOrFail($request->validated('fax_id'));

        $createDocumentsFromFaxAction->execute(
            order: $order,
            fax: $fax,
            user: getUser(),
            orderDocumentRequestsData: $request->validated('order_document_requests'),
            orderDocumentsData: $request->validated('order_documents'),
        );

        return response()->noContent();
    }

    public function delete(
        DeleteDocumentRequest $request,
        Distributor $distributor,
        int $orderId,
        int $documentId,
        DestroyOrderDocument $destroyOrderDocument,
    ): Response {
        $reason = $request->validated('reason');

        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);
        $document = $order->documents()->findOrFail($documentId);

        $this->authorize('delete-order-document', [$distributor, $document]);

        $destroyOrderDocument->execute($document, getUser(), $reason);

        return response()->noContent();
    }

    public function deleteDocumentFile(
        Distributor $distributor,
        int $orderId,
        int $documentId,
        int $fileId,
        FileStorageServiceInterface $fileStorageService,
    ): Response {
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);
        /** @var PatientDocument $document */
        $document = $order->documents()->findOrFail($documentId);
        $file = $document->files()->findOrFail($fileId);

        $this->authorize('delete-order-document', [$distributor, $document]);

        if ($file->delete()) {
            $fileStorageService->deleteFile($file);
        }

        return response()->noContent();
    }

    public function attachDocumentToOrder(
        Distributor $distributor,
        int $orderId,
        int $documentId,
        AttachDetachDocumentToOrderAction $attachDetachDocumentToOrderAction,
    ): Response {
        $document = PatientDocument::findOrFail($documentId);
        $this->authorize('attach-order-document', [$distributor, $document]);
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);
        $attachDetachDocumentToOrderAction->attachDocumentToOrder($order, $document->id);

        return response()->noContent();
    }

    public function detachDocumentFromOrder(
        Distributor $distributor,
        int $orderId,
        int $documentId,
        AttachDetachDocumentToOrderAction $attachDetachDocumentToOrderAction,
    ): Response {
        $document = PatientDocument::findOrFail($documentId);
        $this->authorize('detach-order-document', [$distributor, $document]);
        /** @var Order $order */
        $order = $distributor->orders()->findOrFail($orderId);
        $attachDetachDocumentToOrderAction->detachDocumentFromPatient($order, $document->id);

        return response()->noContent();
    }
}
