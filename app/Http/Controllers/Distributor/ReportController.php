<?php

namespace App\Http\Controllers\Distributor;

use App\Actions\ShuttleHealth\GetFacilityPhoneFaxAddressAction;
use App\Actions\ShuttleHealth\Reports\DistributorManufacturerAction;
use App\Enums\GetFileActionEnum;
use App\Enums\LogGroupEnum;
use App\Enums\OrderStatusEnum;
use App\Exports\DistributorLeadsReport;
use App\Exports\DistributorOrdersExport;
use App\Extensions\Logger;
use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\Filters\FilterReportRequest;
use App\Http\Requests\Shared\Report\CreateReportRequest;
use App\Http\Requests\Shared\Report\UpdateReportRequest;
use App\Http\Resources\Custom\ArrayResource;
use App\Http\Resources\ReportResource;
use App\Models\Distributor;
use App\Models\Report;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\Rules\Enum;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class ReportController
 *
 * Controller for managing reports in distributors.
 */
class ReportController extends Controller
{
    public function index(FilterReportRequest $request, Distributor $distributor): AnonymousResourceCollection
    {
        return ReportResource::collection($distributor->reports()->filter($request)
            ->paginate(perPage())->withQueryString());
    }

    public function store(CreateReportRequest $request, Distributor $distributor): ReportResource
    {
        if (!getUser()->isSHAdmin()) {
            $this->authorize('write', $distributor);
        }

        $report = $distributor->reports()->create($request->validated());

        return ReportResource::make($report);
    }

    public function show(Distributor $distributor, Report $report): ReportResource
    {
        $this->authorize('read', $distributor);

        return ReportResource::make($report);
    }

    public function update(UpdateReportRequest $request, Distributor $distributor, Report $report): ReportResource
    {
        if (!getUser()->isSHAdmin()) {
            $this->authorize('write', $distributor);
        }

        $report->update($request->validated());

        return ReportResource::make($report->refresh());
    }

    public function destroy(Distributor $distributor, Report $report): JsonResponse
    {
        if (!getUser()->isSHAdmin()) {
            $this->authorize('write', $distributor);
        }

        $report->delete();

        return response()->json(null, 204);
    }

    /**
     * Manufacturer orders report
     */
    public function ordersDownload(
        Request $request,
        Distributor $distributor,
        GetFacilityPhoneFaxAddressAction $getFacilityPhoneFaxAddressAction,
    ): BinaryFileResponse {
        $this->authorize('read-order', $distributor);

        $request->validate([
            'manufacturer_id' => 'integer|exists:manufacturers,id',
            'status' => 'array',
            'status.*' => [new Enum(OrderStatusEnum::class)],
            'product_ids' => 'required|array',
            'product_ids.*' => 'required|integer|exists:products,id',
            'date_from' => 'filled|date_format:Y-m-d H:i:s',
            'date_to' => 'filled|date_format:Y-m-d H:i:s|after_or_equal:date_from',
            'shipped_date_from' => 'filled|date_format:Y-m-d H:i:s',
            'shipped_date_to' => 'filled|date_format:Y-m-d H:i:s|after_or_equal:shipped_date_from',
            'zip' => 'nullable|boolean',
            'password' => 'required_with:zip|string',
        ]);

        $productIds = $request->input('product_ids');
        $dateFrom = $request->input('date_from');
        $shippedDateFrom = $request->input('shipped_date_from');

        $start = microtime(true);

        $distributor->products()->findOrFail($productIds);

        $action = new DistributorManufacturerAction(
            getFacilityPhoneFaxAddressAction: $getFacilityPhoneFaxAddressAction,
            betweenDates: $dateFrom ? [$dateFrom, $request->input('date_to')] : [],
            shippedDates: $shippedDateFrom ? [$shippedDateFrom, $request->input('shipped_date_to')] : [],
            statuses: $request->input('status', []),
        );

        $data = $action->buildData($distributor, $productIds, $request->input('manufacturer_id'));

        abort_if(
            empty($data),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.global.no_data_available'),
        );

        $csvFileName = $action->getCsvFileName();
        $csvFilePath = $action->createCsvFile($data, $csvFileName);

        if ($request->input('zip')) {
            $filePath = $action->archiveFileWithPassword($csvFileName, $request->input('password'));

            $response = response()->download($filePath, headers: [
                'Content-Type' => 'application/octet-stream',
            ])->deleteFileAfterSend();
        } else {
            $response = response()->download($csvFilePath, headers: [
                'Content-Type' => 'text/csv',
            ])->deleteFileAfterSend();
        }

        Logger::info(
            sprintf(
                'Manufacturers orders report generation (%s) - %s seconds',
                __FUNCTION__,
                round(microtime(true) - $start, 5),
            ),
            LogGroupEnum::EXECUTION_TIME,
            [
                'distributor_id' => $distributor->id,
                'count' => count($data),
                'memory_peak_usage' => getMeroryPeakUsageInMb(),
                'params' => $request->except('password'),
            ],
        );

        return $response;
    }

    /**
     * All leads report
     */
    public function leadsReport(Request $request, Distributor $distributor): ArrayResource|BinaryFileResponse
    {
        $this->authorize('write', $distributor);

        if ($request->input('action') !== GetFileActionEnum::DOWNLOAD->value) {
            return ArrayResource::make(['download_url' => route('distributors.reports.leads.download', $distributor)]);
        }

        $start = microtime(true);
        $response = Excel::download(
            new DistributorLeadsReport($distributor),
            'leads-report.csv',
            \Maatwebsite\Excel\Excel::CSV,
        );
        Logger::info(
            sprintf(
                'All leads report generation (%s) - %s seconds',
                __FUNCTION__,
                round(microtime(true) - $start, 5),
            ),
            LogGroupEnum::EXECUTION_TIME,
            [
                'distributor_id' => $distributor->id,
                'count' => $distributor->leads()->count(),
                'memory_peak_usage' => getMeroryPeakUsageInMb(),
            ],
        );

        return $response;
    }

    /**
     * Configure and generate orders management report
     */
    public function ordersReport(
        Request $request,
        Distributor $distributor,
        GetFacilityPhoneFaxAddressAction $getFacilityPhoneFaxAddressAction,
    ): ArrayResource|BinaryFileResponse {
        $this->authorize('write', $distributor);

        $request->validate([
            'manufacturer_ids' => 'array',
            'manufacturer_ids.*' => 'integer|exists:manufacturers,id',
            'product_category_ids' => 'array',
            'product_category_ids.*' => 'integer|exists:product_categories,id',
            'status' => 'array',
            'status.*' => [new Enum(OrderStatusEnum::class)],
            'date_from' => 'filled|date_format:Y-m-d H:i:s',
            'date_to' => 'filled|date_format:Y-m-d H:i:s|after_or_equal:date_from',
            'shipped_date_from' => 'filled|date_format:Y-m-d H:i:s',
            'shipped_date_to' => 'filled|date_format:Y-m-d H:i:s|after_or_equal:shipped_date_from',
            'cancelled_date_from' => 'filled|date_format:Y-m-d H:i:s',
            'cancelled_date_to' => 'filled|date_format:Y-m-d H:i:s|after_or_equal:cancelled_from',
        ]);

        if ($request->input('action') !== GetFileActionEnum::DOWNLOAD->value) {
            return ArrayResource::make(['download_url' => route('distributors.reports.orders.download-dump', $distributor)]);
        }

        $manufacturerIds = $request->input('manufacturer_ids');
        $productCategoryIds = $request->input('product_category_ids');
        $status = $request->input('status');
        $creationDates = $request->has('date_from') ? [$request->input('date_from'), $request->input('date_to')] : [];
        $shippingDates = $request->has('shipped_date_from') ? [$request->input('shipped_date_from'), $request->input('shipped_date_to')] : [];
        $cancellationDates = $request->has('cancelled_date_from') ? [$request->input('cancelled_date_from'), $request->input('cancelled_date_to')] : [];

        $start = microtime(true);
        $export = new DistributorOrdersExport(
            distributor: $distributor,
            getFacilityPhoneFaxAddressAction: $getFacilityPhoneFaxAddressAction,
            manufacturerIds: $manufacturerIds,
            productCategoryIds: $productCategoryIds,
            status: $status,
            creationDates: $creationDates,
            shippingDates: $shippingDates,
            cancellationDates: $cancellationDates,
        );
        $response = Excel::download($export, 'orders-report.csv', \Maatwebsite\Excel\Excel::CSV);

        Logger::info(
            sprintf(
                'Orders report generation (%s) - %s seconds',
                __FUNCTION__,
                round(microtime(true) - $start, 5),
            ),
            LogGroupEnum::EXECUTION_TIME,
            [
                'distributor_id' => $distributor->id,
                'memory_peak_usage' => getMeroryPeakUsageInMb(),
                'params' => $request->all(),
            ],
        );

        return $response;
    }
}
