<?php

namespace App\Http\Controllers;

use App\Actions\ShuttleHealth\ResolveNameByPhoneNumberAction;
use App\Actions\ShuttleHealth\SignalWireMobileCommunication;
use App\Contracts\FileStorageServiceInterface;
use App\Contracts\SignalWireClientInterface;
use App\Enums\DocumentRequestStatusEnum;
use App\Enums\FaxDirectionEnum;
use App\Enums\FaxReviewStatusEnum;
use App\Enums\FaxStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\LogGroupEnum;
use App\Enums\SmsDirectionEnum;
use App\Extensions\Logger;
use App\Jobs\SignalWire\FaxReceivedJob;
use App\Jobs\SignalWire\Logs\SaveSignalWireSmsLogJob;
use App\Jobs\SignalWire\RetryFailedFaxJob;
use App\Models\Distributor;
use App\Models\DistributorCampaign;
use App\Models\Fax;
use App\Models\FaxStatusHistory;
use App\Models\SignalWireSmsLog;
use App\Notifications\Mail\FaxDeliveryFailed;
use App\Utils\PhoneNumberConverter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Twilio\Security\RequestValidator as TwilioRequestValidator;

/**
 * Class SignalWireController
 * This controller is responsible for handling the Signal Wire webhooks (inbound messages, inbound faxes, fax status)
 */
class SignalWireController extends Controller
{
    /**
     * Example of fields in payload:
     *
     * MessageSid	95f80457-dddd-43db-9ddd-bcbb59ce4846
     * SmsSid	95f80457-dddd-43db-9ddd-bcbb59ce4846
     * AccountSid	cf18b505-623a-4ea5-9eb7-81d48266d57b
     * From	+***********
     * To	+***********
     * Body	This is a test SMS body
     * NumMedia	0
     * NumSegments	1
     */
    public function inboundMessageWebhook(
        Request $request,
        SignalWireMobileCommunication $mobileCommunicationAction,
        SignalWireClientInterface $signalWireClient,
    ): Response {
        $requestData = $request->post();

        $twilioValidator = new TwilioRequestValidator(config('services.signal-wire.signature'));

        if (!$twilioValidator->validate(
            $request->header('x-signalwire-signature'),
            route('signal-wire.inbound-message-webhook'),
            $requestData,
        )) {
            // try to fetch current sms from SW API, sometimes signature check fails even if sms is genuine
            $smsData = $this->getSmsFromApi($requestData, $signalWireClient);

            if (empty($smsData)) {
                Logger::error('Invalid signature for inbound-sms and sms not found', LogGroupEnum::SIGNAL_WIRE, [
                    'MessageSid' => $requestData['MessageSid'] ?? 'unknown',
                ]);
                abort(Response::HTTP_FORBIDDEN, __('auth.signature'));
            } else {
                Logger::info('Invalid signature for inbound-sms workaround applied', LogGroupEnum::SIGNAL_WIRE, [
                    'MessageSid' => $requestData['MessageSid'] ?? 'unknown',
                ]);
            }
        } else {
            $smsData = $requestData;
        }

        $validator = Validator::make(
            $smsData,
            [
                'From' => 'required',
                'To' => 'required',
                'Body' => 'required|string',
            ],
        );

        if ($validator->fails()) {
            Logger::warning('Inbound SMS webhook validation error.', LogGroupEnum::SIGNAL_WIRE, [
                'data' => $smsData,
                'errors' => $validator->errors(),
            ]);

            // if we return not 200 SW will do 2 more request attempts with same invalid data
            return $this->getXmlResponse();
        }

        $mobileCommunicationAction->execute($smsData);

        return $this->getXmlResponse();
    }

    /**
     * Example of fields in payload:
     *
     * FaxSid	80deaf7a-3129-4373-9e58-a5b7da92a98b
     * AccountSid	cf18b505-623a-4ea5-9eb7-81d48266d57b
     * From	+***********
     * To	+***********
     * RemoteStationId	+***********
     * FaxStatus	received
     * ApiVersion	v1
     * NumPages	1
     * MediaSid	dbdfaa21-9d9d-49c1-8dc4-e46f12aff04b
     * MediaUrl	https://files.signalwire.com/5fb8fd4b-dab3-46cd-b66f-bdd40309b179/cf18b505-623a-4ea5-9eb7-81d48266d57b/faxes/3ba7b191-f079-4ab8-a67b-234de505820c.pdf
     * Timestamp	2023-11-08T22:36:58Z
     */
    public function inboundFaxWebhook(
        Request $request,
        FileStorageServiceInterface $fileStorageService,
        SignalWireClientInterface $signalWireClient,
    ): Response {
        $requestData = $request->post();
        $twilioValidator = new TwilioRequestValidator(config('services.signal-wire.signature'));

        if (!$twilioValidator->validate(
            $request->header('x-signalwire-signature'),
            route('signal-wire.inbound-fax-webhook'),
            $requestData,
        )) {
            // try to fetch current fax from SW API, sometimes signature check fails even if fax is genuine
            $faxData = $this->getFaxFromApi($requestData, $signalWireClient, FaxDirectionEnum::INBOUND);

            if (empty($faxData)) {
                Logger::error('Invalid signature for inbound-fax and fax not found', LogGroupEnum::SIGNAL_WIRE, [
                    'FaxSid' => $requestData['FaxSid'] ?? 'unknown',
                ]);
                abort(Response::HTTP_FORBIDDEN, __('auth.signature'));
            } else {
                Logger::info('Invalid signature for inbound-fax workaround applied', LogGroupEnum::SIGNAL_WIRE, [
                    'FaxSid' => $requestData['FaxSid'] ?? 'unknown',
                ]);
            }
        } else {
            $faxData = $requestData;
        }

        $validator = Validator::make(
            $faxData,
            [
                'FaxSid' => 'required|string',
                'MediaSid' => 'required|string',
                'From' => 'required|string',
                'To' => 'required|string',
                'MediaUrl' => 'required|string',
                'NumPages' => 'required|int',
            ],
        );

        if ($validator->fails()) {
            Logger::warning('Inbound fax webhook validation error.', LogGroupEnum::SIGNAL_WIRE, [
                'data' => $faxData,
                'errors' => $validator->errors(),
            ]);

            // if we return not 200 they SW will do 2 more request attempts with same invalid data
            return $this->getXmlResponse();
        }

        if (Fax::query()->where('fax_uuid', $faxData['FaxSid'])->exists()) {
            abort(Response::HTTP_BAD_REQUEST, 'Fax with uuid ' . $faxData['FaxSid'] . ' already exists.');
        }

        $distributorId = null;
        $distributor = $this->getDistributorByAssignedFax($faxData['To']);

        if ($distributor === null) {
            Logger::warning('Distributor with assigned fax not found', LogGroupEnum::SIGNAL_WIRE, [
                'to_number' => $faxData['To'],
                'from_number' => $faxData['From'],
            ]);
        } else {
            $distributorId = $distributor->id;
        }

        $faxContent = file_get_contents($faxData['MediaUrl']);

        if ($faxContent === false) {
            Logger::error('Fax content download error.', LogGroupEnum::SIGNAL_WIRE, [
                'FaxSid' => $faxData['FaxSid'],
            ]);
        }

        DB::transaction(function () use ($faxData, $distributorId, $fileStorageService, $faxContent) {
            /** @var Fax $fax */
            $fax = Fax::create([
                'distributor_id' => $distributorId,
                'fax_uuid' => $faxData['FaxSid'],
                'review_status' => FaxReviewStatusEnum::NEW,
                'fax_status' => $faxContent ? FaxStatusEnum::DELIVERED : FaxStatusEnum::FAILED,
                'direction' => FaxDirectionEnum::INBOUND,
                'from' => PhoneNumberConverter::toShort($faxData['From']),
                'to' => PhoneNumberConverter::toShort($faxData['To']),
                'pages_count' => $faxData['NumPages'],
                'provider_metadata' => $faxData,
            ]);

            if ($faxContent) {
                $parts = explode('.', $faxData['MediaUrl']);

                $extension = end($parts);
                $uuid = Str::orderedUuid();

                $file = $fax->file()->create([
                    'type' => FileTypeEnum::FAX,
                    'uuid' => $uuid,
                    'file_name' => $uuid . '.' . $extension,
                    'extension' => $extension,
                ]);

                $fileStorageService->uploadFax($file, $faxContent);

                FaxReceivedJob::dispatch($fax)->afterCommit();
            }
        });

        Logger::info('Inbound fax stored successfully.', LogGroupEnum::SIGNAL_WIRE, [
            'FaxSid' => $requestData['FaxSid'],
        ]);

        return $this->getXmlResponse();
    }

    private function getFaxFromApi(array $requestData, SignalWireClientInterface $signalWireClient, FaxDirectionEnum $direction)
    {
        $faxData = null;

        if (!empty($requestData['FaxSid'])) {
            $fax = $signalWireClient->getFax($requestData['FaxSid']);

            // refresh the most important and sensitive fields
            if (!empty($fax['direction']) && $fax['direction'] === $direction->value) {
                $requestData['FaxStatus'] = $fax['status'];
                $requestData['From'] = $fax['from'];
                $requestData['To'] = $fax['to'];
                $requestData['MediaSid'] = $fax['mediaSid'];
                $requestData['MediaUrl'] = $fax['mediaUrl'];

                $faxData = $requestData;
            }
        }

        return $faxData;
    }

    private function getSmsFromApi(array $requestData, SignalWireClientInterface $signalWireClient)
    {
        $smsData = null;

        if (!empty($requestData['MessageSid'])) {
            $sms = $signalWireClient->getMessage($requestData['MessageSid']);

            if (!empty($sms['direction']) && $sms['direction'] === 'inbound') {
                // refresh the most important and sensitive fields
                $requestData['SmsSid'] = $sms['sid'];
                $requestData['From'] = $sms['from'];
                $requestData['To'] = $sms['to'];
                $requestData['Body'] = $sms['body'];
                $requestData['NumSegments'] = $sms['numSegments'];

                $smsData = $requestData;
            }
        }

        return $smsData;
    }

    private function getDistributorByAssignedFax(string $faxNumber)
    {
        $preparedFax = PhoneNumberConverter::toShort($faxNumber);

        return Distributor::where('assigned_fax', $preparedFax)->first();
    }

    /**
     * This webhook is called when fax status is changed and also when status of callback is changed (we will skip last one)
     * Example of fields in payload:
     *
     * FaxSid	80deaf7a-3129-4373-9e58-a5b7da92a98b
     * AccountSid	cf18b505-623a-4ea5-9eb7-81d48266d57b
     * From	+***********
     * To	+***********
     * RemoteStationId	+***********
     * FaxStatus	received
     * ApiVersion	v1
     * NumPages	1
     * MediaSid	dbdfaa21-9d9d-49c1-8dc4-e46f12aff04b
     * MediaUrl	https://files.signalwire.com/5fb8fd4b-dab3-46cd-b66f-bdd40309b179/cf18b505-623a-4ea5-9eb7-81d48266d57b/faxes/3ba7b191-f079-4ab8-a67b-234de505820c.pdf
     * Timestamp	2023-11-08T22:36:58Z
     *
     * And two more fields if FaxStatus is failed
     *
     * ErrorCode 34004
     * ErrorMessage not a PDF file
     */
    public function faxStatusWebhook(Request $request, SignalWireClientInterface $signalWireClient): Response
    {
        $requestData = $request->post();

        // skip if it is change of callback status
        if (!empty($requestData['CallSid'])) {
            return response('', Response::HTTP_OK);
        }

        $twilioValidator = new TwilioRequestValidator(config('services.signal-wire.signature'));

        if (!$twilioValidator->validate(
            $request->header('x-signalwire-signature'),
            route('signal-wire.fax-status-webhook'),
            $requestData,
        )) {
            // try to fetch current fax from SW API, sometimes signature check fails even if fax is genuine
            $faxData = $this->getFaxFromApi($requestData, $signalWireClient, FaxDirectionEnum::OUTBOUND);

            if (empty($faxData)) {
                Logger::error('Invalid signature for fax-status and fax not found', LogGroupEnum::SIGNAL_WIRE, [
                    'FaxSid' => $requestData['FaxSid'] ?? 'unknown',
                ]);
                abort(Response::HTTP_FORBIDDEN, __('auth.signature'));
            } else {
                Logger::info('Invalid signature for fax-status workaround applied', LogGroupEnum::SIGNAL_WIRE, [
                    'FaxSid' => $requestData['FaxSid'] ?? 'unknown',
                ]);
            }
        } else {
            $faxData = $requestData;
        }

        $validator = Validator::make(
            $faxData,
            [
                'FaxSid' => 'required|string',
                'FaxStatus' => 'required|string',
            ],
        );

        if ($validator->fails()) {
            Logger::warning('Fax status webhook validation error.', LogGroupEnum::SIGNAL_WIRE, [
                'data' => $faxData,
                'errors' => $validator->errors(),
            ]);

            // if we return not 200 they SW will do 2 more request attempts with same invalid data
            return $this->getXmlResponse();
        }

        // store fax status history
        $this->storeFaxStatusHistory($requestData);

        /** @var Fax $fax */
        $fax = Fax::where('fax_uuid', $faxData['FaxSid'])->first();

        if (!$fax) {
            Logger::warning("Fax {$faxData['FaxSid']} does not exists.", LogGroupEnum::SIGNAL_WIRE, [
                'FaxSid' => $faxData['FaxSid'],
            ]);

            return response('', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $faxChanges = [
            'fax_status' => $faxData['FaxStatus'],
            'provider_metadata' => $faxData,
        ];

        $unsuccessfulStatuses = [
            FaxStatusEnum::BUSY->value,
            FaxStatusEnum::CANCELED->value,
            FaxStatusEnum::FAILED->value,
            FaxStatusEnum::NO_ANSWER->value,
        ];

        if (in_array($faxData['FaxStatus'], $unsuccessfulStatuses)) {
            $fax->getRelatedDocumentRequestsQuery()->update(['status' => DocumentRequestStatusEnum::FAILED]);

            $faxChanges['failed_at'] = Carbon::now();

            if ($fax->retry_count < 2) {
                RetryFailedFaxJob::dispatch($fax)->delay(
                    Carbon::now()->addSeconds(RetryFailedFaxJob::FAX_RETRY_DELAY),
                );
            } else {
                $documentRequest = $fax->getRelatedDocumentRequestsQuery()->whereNotNull('distributor_user_id')->first();

                if ($documentRequest) {
                    $documentRequest->distributorUser->notify(new FaxDeliveryFailed(
                        fax: $fax,
                        documentRequest: $documentRequest,
                    ), true);
                } else {
                    Logger::warning(
                        "FaxDeliveryFailed mail cannot be send: fax {$fax->fax_uuid} has no related document requests or assigned distributor user.",
                        LogGroupEnum::SIGNAL_WIRE,
                        [
                            'FaxSid' => $faxData['FaxSid'],
                        ],
                    );
                }
            }
        }

        $fax->update($faxChanges);

        return response('', Response::HTTP_OK);
    }

    public function smsStatusWebhook(
        Request $request,
        SignalWireClientInterface $signalWireClient,
    ): Response {
        $requestData = $request->post();

        if (empty($requestData['MessageSid'])) {
            return response('', Response::HTTP_OK);
        }

        $twilioValidator = new TwilioRequestValidator(config('services.signalwire.token'));

        $signature = $request->header('X-Twilio-Signature');
        $url = $request->fullUrl();
        $postData = $request->all();

        if (!$twilioValidator->validate($signature, $url, $postData)) {
            $messageData = $this->getMessageFromApi($requestData, $signalWireClient, SmsDirectionEnum::OUTBOUND);

            if (empty($messageData)) {
                Logger::error('Invalid signature for sms-status and message not found', LogGroupEnum::SIGNAL_WIRE, [
                    'MessageSid' => $requestData['MessageSid'] ?? 'unknown',
                ]);
                abort(Response::HTTP_FORBIDDEN, __('auth.signature'));
            } else {
                Logger::info('Invalid signature for sms-status workaround applied', LogGroupEnum::SIGNAL_WIRE, [
                    'MessageSid' => $requestData['MessageSid'] ?? 'unknown',
                ]);
            }
        } else {
            $messageData = $requestData;
        }

        // Get the actual SMS direction from SignalWire API if available
        $actualDirection = $messageData['direction'] ?? null;

        $normalizedData = [
            'MessageSid' => $messageData['MessageSid'] ?? $messageData['sid'] ?? null,
            'MessageStatus' => $messageData['MessageStatus'] ?? $messageData['status'] ?? null,
            'AccountSid' => $messageData['AccountSid'] ?? $messageData['accountSid'] ?? null,
            'From' => $messageData['From'] ?? $messageData['from'] ?? null,
            'To' => $messageData['To'] ?? $messageData['to'] ?? null,
            'Body' => $messageData['Body'] ?? $messageData['body'] ?? null,
            'NumMedia' => $messageData['NumMedia'] ?? $messageData['numMedia'] ?? null,
            'NumSegments' => $messageData['NumSegments'] ?? $messageData['numSegments'] ?? null,
            'ErrorCode' => $messageData['ErrorCode'] ?? $messageData['errorCode'] ?? null,
            'ErrorMessage' => $messageData['ErrorMessage'] ?? $messageData['errorMessage'] ?? null,
        ];

        $validator = Validator::make(
            $normalizedData,
            [
                'MessageSid' => 'required_without:sid|string',
                'sid' => 'required_without:MessageSid|string',

                'MessageStatus' => 'required_without:status|string',
                'status' => 'required_without:MessageStatus|string',
                'AccountSid' => 'nullable|string',
                'From' => 'nullable|string',
                'To' => 'nullable|string',
                'Body' => 'nullable|string',
                'NumMedia' => 'nullable|integer',
                'NumSegments' => 'nullable|integer',
                'ErrorCode' => 'nullable|string',
            ],
        );

        if ($validator->fails()) {
            Logger::warning('SMS status webhook validation error.', LogGroupEnum::SIGNAL_WIRE, [
                'data' => $normalizedData,
                'errors' => $validator->errors(),
            ]);

            return $this->getXmlResponse();
        }


        $smsLog = SignalWireSmsLog::where('sid', $normalizedData['MessageSid'])->first();

        if (!$smsLog) {
            Logger::warning("SMS {$normalizedData['MessageSid']} does not exist.", LogGroupEnum::SIGNAL_WIRE, [
                'MessageSid' => $normalizedData['MessageSid'],
            ]);
            $nameResolver = app(ResolveNameByPhoneNumberAction::class);

            // Try to resolve distributor from SMS data using actual direction
            $distributor = $this->getDistributorFromSmsData($normalizedData, $actualDirection);

            if ($distributor) {
                SaveSignalWireSmsLogJob::dispatch($distributor, $normalizedData['MessageSid'], [
                    'sid' => $normalizedData['MessageSid'],
                    'status' => $normalizedData['MessageStatus'],
                    'from' => $normalizedData['From'] ?? null,
                    'to' => $normalizedData['To'] ?? null,
                    'sender_name' => $nameResolver->execute($normalizedData['From'] ?? null),
                    'receiver_name' => $nameResolver->execute($normalizedData['To'] ?? null),
                    'direction' => $actualDirection ?? 'unknown',
                    'body' => $normalizedData['Body'] ?? 'Sample message body',
                    'error_code' => $normalizedData['ErrorCode'] ?? null,
                    'error_message' => $normalizedData['ErrorMessage'] ?? null,
                ]);
            } else {
                Logger::warning('Could not resolve distributor for SMS log', LogGroupEnum::SIGNAL_WIRE, [
                    'MessageSid' => $normalizedData['MessageSid'],
                    'From' => $normalizedData['From'] ?? null,
                    'To' => $normalizedData['To'] ?? null,
                    'direction' => $actualDirection ?? 'unknown',
                    'reason' => $actualDirection ? 'distributor_not_found' : 'direction_unknown',
                ]);
            }

            return response('', Response::HTTP_OK);
        }

        $smsChanges = [
            'status' => $normalizedData['MessageStatus'],
            'error_code' => $normalizedData['ErrorCode'] ?? null,
            'error_message' => $normalizedData['ErrorMessage'] ?? null,
            'updated_at' => now(),
        ];

        $failedStatuses = ['failed', 'undelivered'];

        if (in_array(strtolower($normalizedData['MessageStatus']), $failedStatuses)) {
            $smsChanges['failed_at'] = Carbon::now();

            // Optional: Handle failed SMS notifications or retry logic
            // You could notify users or trigger other actions here
            Logger::warning('SMS delivery failed', LogGroupEnum::SIGNAL_WIRE, [
                'MessageSid' => $normalizedData['MessageSid'],
                'Status' => $normalizedData['MessageStatus'],
                'ErrorCode' => $normalizedData['ErrorCode'] ?? null,
                'ErrorMessage' => $normalizedData['ErrorMessage'] ?? null,
            ]);
        }

        // Update the SMS log
        $smsLog->update($smsChanges);

        return response('', Response::HTTP_OK);
    }

    private function getMessageFromApi(array $requestData, SignalWireClientInterface $signalWireClient, SmsDirectionEnum $direction): array
    {
        if (empty($requestData['MessageSid'])) {
            return [];
        }

        try {
            $message = $signalWireClient->getMessage($requestData['MessageSid']);

            if (!$message) {
                return [];
            }

            return $message;
        } catch (Throwable $e) {
            Logger::error('Failed to fetch message from API', LogGroupEnum::SIGNAL_WIRE, [
                'MessageSid' => $requestData['MessageSid'],
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }



    /**
     * Returns XML response which is needed for Signal Wire to process fax successfully
     */
    private function getXmlResponse()
    {
        $xml = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Response>
</Response>
XML;

        return response($xml, Response::HTTP_OK, ['Content-Type' => 'application/xml']);
    }

    /**
     * @param array $data
     * @return void
     *
     * Store fax status history if needed
     * This method can be implemented to log or store the status changes of the fax,
     * For example, you can create a FaxStatusHistory model and save the status changes there
     */
    protected function storeFaxStatusHistory(array $data): void
    {
        try {
            FaxStatusHistory::create([
                'fax_sid' => $data['FaxSid'],
                'fax_status' => $data['FaxStatus'],
                'fax_status_timestamps' => Carbon::parse($data['Timestamp']),
                'fax_status_description' => FaxStatusEnum::getFaxStatusDescription($data['FaxStatus']),
                'fax_metadata' => json_encode($data),
            ]);
        } catch (\Exception $e) {
            Logger::error('Failed to store fax status history', LogGroupEnum::SIGNAL_WIRE, [
                'FaxSid' => $data['FaxSid'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get distributor from SMS data considering direction context
     *
     * For OUTBOUND SMS (system → patient): From = distributor, To = patient
     *   - Direction values: "outbound", "outbound-api", or "outbound-reply"
     * For INBOUND SMS (patient → system): From = patient, To = distributor
     *   - Direction value: "inbound"
     *
     * @param array $smsData
     * @param string|null $direction
     * @return Distributor|null
     */
    private function getDistributorFromSmsData(array $smsData, ?string $direction): ?Distributor
    {
        // If direction is unknown, we cannot safely determine which phone number contains the distributor
        // This prevents associating messages with wrong distributors
        if (!$direction) {
            return null;
        }

        // Determine which phone number should contain the distributor based on direction
        $distributorPhoneNumber = null;

        if ($direction === 'outbound' || $direction === 'outbound-api' || $direction === 'outbound-reply') {
            // For outbound SMS: From = distributor, To = patient
            $distributorPhoneNumber = $smsData['From'] ?? null;
        } elseif ($direction === 'inbound') {
            // For inbound SMS: From = patient, To = distributor
            $distributorPhoneNumber = $smsData['To'] ?? null;
        } else {
            // Unknown direction value - don't assume anything
            return null;
        }

        if (!$distributorPhoneNumber) {
            return null;
        }

        // First try to find distributor by assigned fax number
        $distributor = $this->getDistributorByAssignedFax($distributorPhoneNumber);

        if ($distributor) {
            return $distributor;
        }

        // Try to find distributor through distributor campaigns with message templates
        $distributorCampaign = DistributorCampaign::query()
            ->whereHas('messageTemplates', function ($query) use ($distributorPhoneNumber) {
                $query->where('phone_number', PhoneNumberConverter::toShort($distributorPhoneNumber));
            })
            ->first();

        if ($distributorCampaign) {
            return $distributorCampaign->distributor;
        }

        return null;
    }
}
