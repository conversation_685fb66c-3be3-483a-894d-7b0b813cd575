<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Laravel\Passport\Passport;

class SetTokenExpiration
{
    public function handle($request, Closure $next)
    {
        if (!$request->routeIs('passport.token')) {
            if ($request->routeIs('ext.api.*') || App::isLocal()) {
                Passport::tokensExpireIn(now()->addDays(7));
                Passport::refreshTokensExpireIn(now()->addDays(14));
            } else {
                Passport::tokensExpireIn(now()->addMinutes(45));
                Passport::refreshTokensExpireIn(now()->addMinutes(60));
            }
        }

        return $next($request);
    }
}
